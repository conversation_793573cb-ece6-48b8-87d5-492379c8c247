<!--Heading-->

# SmartSewa

Flutter representation of a SmartSewa(Application).

## General setup

Download zip file or clone this repo.

### Use the stable channel

Make sure you're on the stable channel with the most recent version of Flutter.

```
flutter channel stable
flutter upgrade
```

## Testing it out

Run the app on an Android and iOS device or emulator/simulator to make sure the splash screen,
launcher icon and app name display correctly.

# Libraries & Tools Used

## Here is a checklist for things to pay attention to in **project **:

- Android Studio version : Android Studio Electric Eel |2022.1.1
- Flutter vresion : Flutter 3.3.4 - channel stable
- Dart version : Dart SDK version: 2.18.2 (stable)
- project minSdkVersion : flutter.minSdkVersion
- project targetSdkVersion : flutter.targetSdkVersion
- jav version : java  "17.0.6"

## Here is a checklist for things to pay attention to in **pubspec.yaml**:

- Set the app name and description.
- Remove all the default comments.
- To add assets to your application, add an assets section, like this:

```
   assets:
    - assets/ 
  // - images/a_dot_ham.jpeg
```

- Then Add below depencencies .

### Dependencies:

- shared_preferences: ^2.0.17
- custom_info_window: ^1.0.1
- http: ^0.13.5
- carousel_slider: ^4.2.1
- google_maps_flutter: ^2.2.3
- permission_handler: ^10.2.0
- geolocator: ^9.0.2
- get: ^4.6.5
- smooth_page_indicator: ^1.0.1
- image_picker: ^0.8.6+1

### dev_dependencies:

- flutter_launcher_icons: "^0.11.0"

  flutter_icons:
  android: true
  image_path: "assets/appslogo.png"

## Here is a checklist for things to pay attention to in **AndroidManifest**:

### Google Map integration in project :

1- Depencencies.
google_maps_flutter: ^2.2.3
permission_handler: ^10.2.0
geolocator: ^9.0.2

2- Build .gradle:
compileSdkVersion 33
minSdkVersion 20

### andoridmanifest:

#### <uses-permission android:name="android.permission.INTERNET" />

uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

<meta-data android:name="com.google.android.geo.API_KEY"
android:value="write your own api key "/>

## Here is the core folder structure which flutter provides.

- flutter-app/
- android
- build
- ios
- lib
- test

## Here is the folder structure we have been using in this project : lib/

- network
- view_model
- views
- main

## Now, lets dive into the lib folder which has the main code for the application.

1. Network
    - Services : Contains all controller and remote api services.
    - Models : Contains user details model and categories detail model.

2. view_model
    - Controllers : Contains all text editing controller .
    - Search : Contains search functions .

3. views
    - User_screen :contains all screens related to user.
    - Auth: This screen contains all the authentication operation (Registration, Login, OTP).
        - Forget Password :This folder contains screens and operation to change password.
        - Login : Contains screen for login.
        - otpField :Contains OTP field (textbox to input verification otp).
        - Registration : Contains user registration screen & registration otp screen.
    - Service_provider_screen : Contains all the screens related to service provider .
    - Widgets:  Contains custom appbar , Drawer.
    - Buttons : Contains custom reusable material button and text button.
    - Intro_screen : contains onboarding screen ,Splash screen , select_screen.
    - textField : Contains all custom TextFormfield box.
    - Theme : This screen consists of Theme used in application.


4. main.dart - This is the starting point of the application. All the application level
   configurations are defined in this file like theme, routes, title.

```
void main() {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      theme: MyTheme().appTheme(),
      debugShowCheckedModeBanner: false,
      home: SplashScreen(),
    );
  }
}

```

## assets/

This Folder includes all image assets of project . You can either add or delete images in this
folder .

## BaseUrl

To change base url for any network activities , you need to open baseClient folder (
lib/network/baseClient.dart) . On baseClient class you need to change String baseUrl= "your base
url".

Example:

class BaseClient {

String baseUrl = "http://10.0.4.30:9000";

}

## Build Apk

flutter build apk --target-platform android-arm64 --analyze-size

Gmail: <EMAIL>

