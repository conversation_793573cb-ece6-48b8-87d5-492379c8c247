// // ignore_for_file: public_member_api_docs, sort_constructors_first
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';

// import 'package:smartsewa/network/services/authServices/auth_controller.dart';

// import '../../../views/widgets/buttons/app_buttons.dart';
// import '../../widgets/custom_snackbar.dart';

// class ForgetChangePassword extends StatefulWidget {
//   final String? mobileNumber;
//   const ForgetChangePassword({
//     super.key,
//     this.mobileNumber,
//   });

//   @override
//   State<ForgetChangePassword> createState() => _ForgetChangePasswordState();
// }

// class _ForgetChangePasswordState extends State<ForgetChangePassword> {
//   //final bool _oldPasswordvisible = false;
//   bool _passwordvisible = false;
//   bool _confirmpasswordvisible = false;

//   AuthController authController = Get.put(AuthController());

//   // final TextEditingController _oldPassword = TextEditingController();

//   final TextEditingController _password = TextEditingController();

//   final TextEditingController _confirmPassword = TextEditingController();

//   final _formKey = GlobalKey<FormState>();
//   ///////////////////////////////  validation for password //////////////////////////////////////
//   bool _containsUppercase(String value) {
//     return value.contains(RegExp(r'[A-Z]'));
//   }

//   bool _containsLowercase(String value) {
//     return value.contains(RegExp(r'[a-z]'));
//   }

//   bool _containsNumber(String value) {
//     return value.contains(RegExp(r'[0-9]'));
//   }

//   bool _containsSpecialCharacter(String value) {
//     return value.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
//   }

//   RegExp regex =
//       RegExp(r'^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?\d)(?=.*?[!@#$&*~]).{8,}$');

//   @override
//   Widget build(BuildContext context) {
//     Size size = MediaQuery.of(context).size;

//     return Scaffold(
//       body: Form(
//         key: _formKey,
//         child: Padding(
//           padding: EdgeInsets.all(size.aspectRatio * 68),
//           child: ListView(
//             children: [
//               SizedBox(
//                 height: size.height * 0.05,
//               ),
//               Image.asset(
//                 'assets/Logo.png',
//                 height: size.height * 0.2,
//               ),
//               SizedBox(height: size.height * 0.05),
//               const Text(
//                 'Reset Password',
//                 style: TextStyle(
//                   fontFamily: 'hello',
//                   fontSize: 28,
//                   fontWeight: FontWeight.w400,
//                   color: Color.fromRGBO(0, 131, 143, 1),
//                 ),
//               ),
//               SizedBox(height: size.height * 0.025),
//               SizedBox(height: size.height * 0.025),
//               Column(
//                 children: [
//                   const Row(
//                     children: [
//                       Icon(
//                         Icons.lock_open_rounded,
//                         size: 25,
//                         color: Color.fromRGBO(0, 131, 143, 1),
//                       ),
//                       SizedBox(width: 5),
//                       Text(
//                         'Enter New Password',
//                         style: TextStyle(
//                           fontFamily: 'hello',
//                           fontSize: 16,
//                           fontWeight: FontWeight.w500,
//                           color: Color.fromRGBO(0, 131, 143, 1),
//                         ),
//                       ),
//                     ],
//                   ),
//                   SizedBox(height: size.height * 0.012),
//                   TextFormField(
//                     autovalidateMode: AutovalidateMode.onUserInteraction,
//                     controller: _password,
//                     validator: (password) {
//                       if (password!.isEmpty) {
//                         return 'Required**';
//                       } else if (password.length < 8) {
//                         return 'Password must be at least 8 characters long';
//                       } else if (!_containsUppercase(password) ||
//                           !_containsLowercase(password) ||
//                           !_containsNumber(password) ||
//                           !_containsSpecialCharacter(password)) {
//                         return 'Include one uppercase&lowercase letter,number,specialcharacter';
//                       }
//                       return null;
//                     },
//                     textAlign: TextAlign.left,
//                     style: const TextStyle(color: Colors.black),
//                     obscureText: !_passwordvisible,
//                     decoration: InputDecoration(
//                         suffixIcon: IconButton(
//                             onPressed: () {
//                               setState(() {
//                                 _passwordvisible = !_passwordvisible;
//                               });
//                             },
//                             icon: Icon(_passwordvisible
//                                 ? CupertinoIcons.eye
//                                 : CupertinoIcons.eye_slash)),
//                         filled: true,
//                         fillColor: Colors.white,
//                         border: OutlineInputBorder(
//                           borderRadius: BorderRadius.circular(18),
//                         ),
//                         hintText: '********',
//                         hintStyle: Theme.of(context).textTheme.titleLarge),
//                   ),
//                 ],
//               ),
//               SizedBox(height: size.height * 0.025),
//               Column(
//                 children: [
//                   const Row(
//                     children: [
//                       Icon(
//                         Icons.lock_open_rounded,
//                         size: 25,
//                         color: Color.fromRGBO(0, 131, 143, 1),
//                       ),
//                       SizedBox(width: 5),
//                       Text(
//                         'Confirm New Password',
//                         style: TextStyle(
//                           fontFamily: 'hello',
//                           fontSize: 16,
//                           fontWeight: FontWeight.w500,
//                           color: Color.fromRGBO(0, 131, 143, 1),
//                         ),
//                       ),
//                     ],
//                   ),
//                   SizedBox(height: size.height * 0.012),
//                   TextFormField(
//                     autovalidateMode: AutovalidateMode.onUserInteraction,
//                     controller: _confirmPassword,
//                     textAlign: TextAlign.left,
//                     style: const TextStyle(color: Colors.black),
//                     obscureText: !_confirmpasswordvisible,
//                     validator: (confirmPassword) {
//                       if (confirmPassword!.isEmpty) {
//                         return 'Required**';
//                       } else if (confirmPassword != _password.text) {
//                         return 'Password do not match';
//                       }
//                       return null;
//                     },
//                     decoration: InputDecoration(
//                         suffixIcon: IconButton(
//                           onPressed: () {
//                             setState(() {
//                               _confirmpasswordvisible =
//                                   !_confirmpasswordvisible;
//                             });
//                           },
//                           icon: Icon(
//                             _confirmpasswordvisible
//                                 ? CupertinoIcons.eye
//                                 : CupertinoIcons.eye_slash,
//                           ),
//                         ),
//                         fillColor: Colors.white,
//                         filled: true,
//                         border: OutlineInputBorder(
//                           borderRadius: BorderRadius.circular(18),
//                         ),
//                         hintText: '********',
//                         hintStyle: Theme.of(context).textTheme.titleLarge),
//                   ),
//                 ],
//               ),
//               SizedBox(height: size.height * 0.04),
//               Obx(
//                 () => authController.isLoading.value
//                     ? const Center(
//                         child: CircularProgressIndicator(),
//                       )
//                     : Center(
//                         child: AppButton(
//                           name: 'Continue',
//                           onPressed: () {
//                             if (_formKey.currentState!.validate()) {
//                               authController.forgotPassword(
//                                 newPassword: _password.text.trim(),
//                                 confirmPassword: _confirmPassword.text.trim(),
//                                 mobileNumber: widget.mobileNumber,
//                               );
//                             } else {
//                               CustomSnackBar.showSnackBar(
//                                 title: "Please fill",
//                                 color: Colors.red,
//                               );
//                             }
//                           },
//                         ),
//                       ),
//               ),

//               /*
//               Obx(
//                 () => authController.isLoading.value
//                     ? const Center(
//                         child: CircularProgressIndicator(),
//                       )
//                     : AppButton(
//                         name: 'Continue',
//                         onPressed: () {
//                           if (_formKey.currentState!.validate()) {
//                             authController.forgotPassword(
//                               confirmPassword: _confirmPassword.text.trim(),
//                               newPassword: _password.text.trim(),
//                               mobileNumber: widget.mobileNumber,
//                             );
//                           } else {
//                             CustomSnackBar.showSnackBar(
//                                 title: "Please fill", color: Colors.red);
//                           }
//                         },
//                       ),
//               ),

//               */
//               SizedBox(height: size.height * 0.1),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:smartsewa/network/services/authServices/auth_controller.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';

import '../../../views/widgets/buttons/app_buttons.dart';
import '../../widgets/custom_snackbar.dart';

class ForgetChangePassword extends StatefulWidget {
  final String? mobileNumber;

  const ForgetChangePassword({super.key, this.mobileNumber});

  @override
  State<ForgetChangePassword> createState() => _ForgetChangePasswordState();
}

class _ForgetChangePasswordState extends State<ForgetChangePassword> {
  bool _passwordVisible = false;
  bool _confirmPasswordVisible = false;

  AuthController authController = Get.put(AuthController());

  final TextEditingController _password = TextEditingController();
  final TextEditingController _confirmPassword = TextEditingController();

  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: myAppbar(context, true, ""),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color.fromARGB(255, 245, 245, 245),
              Color.fromARGB(255, 255, 255, 255)
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Center(
          child: SingleChildScrollView(
            child: Container(
              width: size.width * 0.9,
              padding: EdgeInsets.all(size.width * 0.06),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.3),
                    spreadRadius: 5,
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 10),
                    Image.asset(
                      'assets/Logo.png',
                      height: size.height * 0.15,
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'Reset Password',
                      style: TextStyle(
                        fontFamily: 'hello',
                        fontSize: size.width * 0.08,
                        fontWeight: FontWeight.w600,
                        color: const Color.fromRGBO(0, 131, 143, 1),
                      ),
                    ),
                    const SizedBox(height: 30),
                    _buildPasswordField(
                        size, 'Enter New Password', _password, _passwordVisible,
                        () {
                      setState(() {
                        _passwordVisible = !_passwordVisible;
                      });
                    }),
                    const SizedBox(height: 20),
                    _buildPasswordField(size, 'Confirm New Password',
                        _confirmPassword, _confirmPasswordVisible, () {
                      setState(() {
                        _confirmPasswordVisible = !_confirmPasswordVisible;
                      });
                    }),
                    const SizedBox(height: 40),
                    Obx(
                      () => authController.isLoading.value
                          ? const Center(child: CircularProgressIndicator())
                          : AppButton(
                              name: 'Continue',
                              onPressed: () {
                                if (_formKey.currentState!.validate()) {
                                  authController.forgotPassword(
                                    newPassword: _password.text.trim(),
                                    confirmPassword:
                                        _confirmPassword.text.trim(),
                                    mobileNumber: widget.mobileNumber,
                                  );
                                } else {
                                  CustomSnackBar.showSnackBar(
                                    title: "Please fill",
                                    color: Colors.red,
                                  );
                                }
                              },
                            ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPasswordField(Size size, String label,
      TextEditingController controller, bool isVisible, Function onToggle) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(
              Icons.lock_outline,
              size: 24,
              color: Color.fromRGBO(0, 131, 143, 1),
            ),
            const SizedBox(width: 10),
            Text(
              label,
              style: TextStyle(
                fontFamily: 'hello',
                fontSize: size.width * 0.045,
                fontWeight: FontWeight.w500,
                color: const Color.fromRGBO(0, 131, 143, 1),
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        TextFormField(
          controller: controller,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          obscureText: !isVisible,
          validator: (password) {
            if (password!.isEmpty) return 'Required**';
            if (password.length < 8) return 'Must be at least 8 characters';
            return null;
          },
          decoration: InputDecoration(
            filled: true,
            fillColor: Colors.grey.shade200,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide.none,
            ),
            hintText: '********',
            suffixIcon: IconButton(
              onPressed: () => onToggle(),
              icon: Icon(
                isVisible
                    ? CupertinoIcons.eye_fill
                    : CupertinoIcons.eye_slash_fill,
                color: Colors.grey.shade600,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
