
// AppBar searchBar(BuildContext context) {
  
//     return AppBar(
//       backgroundColor: Theme.of(context).scaffoldBackgroundColor,
//       title: const TextField(
//         decoration: InputDecoration(
//             border: InputBorder.none,
//             hintText: 'Search...',
//             hintStyle: TextStyle(color: Colors.white)),
//         textInputAction: TextInputAction.search,
//       ),
//       actions: [
//         IconButton(onPressed: () {}, icon: const Icon(Icons.search_outlined))
//       ],
//     );
//   }

