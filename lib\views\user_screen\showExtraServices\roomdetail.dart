import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:async';

import 'package:smartsewa/views/widgets/my_appbar.dart';

class RoomDetailPage extends StatefulWidget {
  final Map<String, dynamic> roomDetails;

  const RoomDetailPage({super.key, required this.roomDetails});

  @override
  State<RoomDetailPage> createState() => _RoomDetailPageState();
}

class _RoomDetailPageState extends State<RoomDetailPage> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  Timer? _timer;
  final primaryColor = const Color.fromARGB(240, 0, 131, 143);

  final List<String> dummyImages = [
    'assets/image1.jpg',
    'assets/image2.jpg',
    'assets/image3.jpg',
    'assets/image4.jpg',
  ];

  @override
  void initState() {
    super.initState();
    _startImageSlider();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  void _startImageSlider() {
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (_currentPage < dummyImages.length - 1) {
        _currentPage++;
      } else {
        _currentPage = 0;
      }
      _pageController.animateToPage(
        _currentPage,
        duration: const Duration(milliseconds: 350),
        curve: Curves.easeIn,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: myAppbar(context, true, "Your Perfect Home"),
      body: CustomScrollView(
        slivers: [
          // Custom App Bar with Image Slider
          SliverAppBar(
            expandedHeight: size.height * 0.25, // Reduced height of the image
            backgroundColor: Colors.transparent,
            automaticallyImplyLeading: false, // This will remove the back icon
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  // Image Slider with Padding
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32.0),
                    child: PageView.builder(
                      controller: _pageController,
                      onPageChanged: (index) {
                        setState(() => _currentPage = index);
                      },
                      itemCount: dummyImages.length,
                      itemBuilder: (context, index) {
                        return Stack(
                          children: [
                            // Main Image with ClipRRect for rounded corners
                            ClipRRect(
                              borderRadius: BorderRadius.circular(15),
                              child: Container(
                                margin:
                                    const EdgeInsets.symmetric(vertical: 20),
                                child: widget.roomDetails['image'] != null
                                    ? Image.file(
                                        widget.roomDetails['image'],
                                        fit: BoxFit.cover,
                                      )
                                    : Image.asset(
                                        dummyImages[index],
                                        fit: BoxFit.cover,
                                      ),
                              ),
                            ),
                            // Small Image Preview Row - Position moved further down
                            Positioned(
                              bottom:
                                  0, // Adjusted to ensure it's not overlapping
                              left: 0,
                              right: 0,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: List.generate(
                                  dummyImages.length,
                                  (i) => Container(
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 4),
                                    width: i == _currentPage ? 24 : 12,
                                    height: 8,
                                    decoration: BoxDecoration(
                                      color: i == _currentPage
                                          ? primaryColor
                                          : Colors.white,
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                  // Gradient overlay with reduced opacity
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16.0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.5), // Reduced opacity
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Content
          SliverToBoxAdapter(
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Thumbnail Images
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: SizedBox(
                      height: 39,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: dummyImages.length,
                        itemBuilder: (context, index) {
                          return GestureDetector(
                            onTap: () {
                              _pageController.animateToPage(
                                index,
                                duration: const Duration(milliseconds: 350),
                                curve: Curves.easeIn,
                              );
                            },
                            child: Container(
                              width: 45,
                              margin: const EdgeInsets.only(right: 8),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: _currentPage == index
                                      ? primaryColor
                                      : Colors.transparent,
                                  width: 2,
                                ),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(6),
                                child: widget.roomDetails['image'] != null
                                    ? Image.file(
                                        widget.roomDetails['image'],
                                        fit: BoxFit.cover,
                                      )
                                    : Image.asset(
                                        dummyImages[index],
                                        fit: BoxFit.cover,
                                      ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  const SizedBox(height: 0.5),
                  // Title and Price Section
                  Padding(
                    padding: const EdgeInsets.all(10),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                widget.roomDetails['title'] ??
                                    'Luxurious Duplex Flat',
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.share,
                                  color: Colors.grey), // Share icon
                              onPressed: () {
                                _shareRoomDetails(); // Call share function
                              },
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                color: primaryColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                "Rs. ${widget.roomDetails['price'] ?? '20,000'}/mo",
                                style: TextStyle(
                                  color: primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(Icons.location_on,
                                size: 16, color: primaryColor),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                widget.roomDetails['location'] ??
                                    'New Baneshwor, Kathmandu',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Room Features
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildFeatureItem(Icons.bed, "2 Beds"),
                        _buildFeatureItem(Icons.bathtub, "1 Bath"),
                        _buildFeatureItem(Icons.kitchen, "1 Kitchen"),
                        _buildFeatureItem(Icons.aspect_ratio, "1200 sq.ft"),
                      ],
                    ),
                  ),

                  // Description
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          "Description",
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Text(
                          widget.roomDetails['description'] ??
                              'This room is equipped with 2 master bedrooms, 1 kitchen, and 1 toilet. A beautiful view can be seen from the balcony. The water is running 24 hours and drinking water is supplied once a week.',
                          style: TextStyle(
                            height: 1.5,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Facilities
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          "Facilities",
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: [
                            _buildFacilityChip(Icons.wifi, "WiFi"),
                            _buildFacilityChip(Icons.water, "24/7 Water"),
                            _buildFacilityChip(Icons.local_parking, "Parking"),
                            _buildFacilityChip(Icons.security, "Security"),
                            _buildFacilityChip(Icons.park, "Garden"),
                            _buildFacilityChip(Icons.elevator, "Elevator"),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Contact Person
                  Container(
                    margin: const EdgeInsets.all(20),
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: 30,
                          backgroundImage: AssetImage(
                            widget.roomDetails['contactImage'] ??
                                'assets/person.jpg',
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.roomDetails['contactPerson'] ??
                                    "Elon Musk",
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                "Property Owner",
                                style: TextStyle(
                                  color: Colors.grey[600],
                                ),
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  Icon(Icons.phone,
                                      size: 14, color: Colors.grey[600]),
                                  const SizedBox(width: 4),
                                  Text(
                                    widget.roomDetails['contactNumber'] ??
                                        "9819878545",
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Row(
                          children: [
                            _buildContactButton(
                              Icons.message,
                              Colors.blue,
                              () {},
                            ),
                            const SizedBox(width: 8),
                            _buildContactButton(
                              Icons.call,
                              Colors.green,
                              () {},
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _shareRoomDetails() {
    final String title = widget.roomDetails['title'] ?? 'Luxurious Duplex Flat';
    final String price = widget.roomDetails['price'] ?? '20,000';
    final String location =
        widget.roomDetails['location'] ?? 'New Baneshwor, Kathmandu';

    // Create the share message
    String shareMessage =
        "Check out this property: $title\nPrice: Rs. $price/mo\nLocation: $location";

    // Use the 'share' package (you need to add it to your pubspec.yaml)
    Share.share(shareMessage);
  }

  Widget _buildFeatureItem(IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: primaryColor),
          const SizedBox(height: 4),
          Text(
            text,
            style: TextStyle(
              color: primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFacilityChip(IconData icon, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 12,
        vertical: 8,
      ),
      decoration: BoxDecoration(
        color: primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: primaryColor),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactButton(
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return CircleAvatar(
      radius: 20,
      backgroundColor: color.withOpacity(0.1),
      child: IconButton(
        icon: Icon(icon, size: 20, color: color),
        onPressed: onPressed,
      ),
    );
  }
}
