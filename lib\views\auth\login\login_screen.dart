// // import 'dart:developer';
// // import 'package:flutter/cupertino.dart';
// // import 'package:flutter/material.dart';
// // import 'package:flutter/services.dart';
// // import 'package:get/get.dart';

// // import 'package:shared_preferences/shared_preferences.dart';
// // import 'package:smartsewa/core/development/console.dart';
// // import 'package:smartsewa/views/auth/registration/user_registration.dart';
// // import 'package:smartsewa/views/widgets/custom_snackbar.dart';
// // import 'package:smartsewa/views/widgets/my_appbar.dart';
// // import '../../../network/services/authServices/auth_controller.dart';
// // import '../../widgets/buttons/app_buttons.dart';
// // import '../forgotPassword/forget_password_mobile.dart';

// // class LoginScreen extends StatefulWidget {
// //   const LoginScreen({super.key});

// //   @override
// //   State<LoginScreen> createState() => _LoginScreenState();
// // }

// // class _LoginScreenState extends State<LoginScreen> {
// //   bool isVisible = false;
// //   final _formKey = GlobalKey<FormState>();
// //   final _controller = Get.put(AuthController());
// //   final phoneController = TextEditingController();
// //   final passwordController = TextEditingController();
// //   final LocalAuthentication auth = LocalAuthentication();
// //   bool _isAuthenticating = false;
// //   bool _isAuthenticated = false;
// //   bool _isFingerprintEnabled = false;
// //   bool canCheckBiometrics = false;

// //   @override
// //   void initState() {
// //     super.initState();
// //     _checkFingerprintStatus(); // Check if fingerprint is enabled on app startup
// //   }

// //   Future<void> _checkFingerprintStatus() async {
// //     SharedPreferences preferences = await SharedPreferences.getInstance();
// //     bool? fingerprintStatus = preferences.getBool('fingerprintEnabled');
// //     log('Fingerprint status from SharedPreferences: $fingerprintStatus');

// //     setState(() {
// //       _isFingerprintEnabled = fingerprintStatus ?? false; // Handles null case
// //     });
// //   }

// //   Future<void> _authenticateWithBiometrics() async {
// //     try {
// //       setState(() {
// //         _isAuthenticating = true;
// //       });

// //       bool canCheckBiometrics = await auth.canCheckBiometrics;
// //       if (!canCheckBiometrics) {
// //         CustomSnackBar.showSnackBar(
// //           title: "Biometric authentication not available.",
// //           color: Colors.red,
// //         );
// //         return;
// //       }

// //       final bool authenticated = await auth.authenticate(
// //         localizedReason: 'Please authenticate to log in',
// //         options: const AuthenticationOptions(biometricOnly: true),
// //       );

// //       setState(() {
// //         _isAuthenticated = authenticated;
// //         _isAuthenticating = false;
// //       });

// //       if (authenticated) {
// //         SharedPreferences pref = await SharedPreferences.getInstance();
// //         phoneController.text = pref.getString('username')!;
// //         passwordController.text = pref.getString('password')!;

// //         _controller.login(phoneController, passwordController);
// //       }
// //     } on PlatformException catch (e) {
// //       log(e.toString());
// //       setState(() {
// //         _isAuthenticating = false;
// //       });
// //       CustomSnackBar.showSnackBar(
// //         title: "Authentication failed. Please try again.",
// //         color: Colors.red,
// //       );
// //     }
// //   }

// //   Future<void> _enableFingerprint() async {
// //     SharedPreferences preferences = await SharedPreferences.getInstance();
// //     await preferences.setBool('fingerprintEnabled', true);
// //     setState(() {
// //       _isFingerprintEnabled = true;
// //     });
// //     CustomSnackBar.showSnackBar(
// //       title: "Fingerprint authentication enabled.",
// //       color: const Color.fromRGBO(0, 131, 143, 1),
// //     );
// //   }

// //   @override
// //   Widget build(BuildContext context) {
// //     Size size = MediaQuery.of(context).size;
// //     return Scaffold(
// //       appBar: myAppbar(context, true, ""),
// //       body: Form(
// //         key: _formKey,
// //         child: Padding(
// //           padding: EdgeInsets.symmetric(horizontal: size.width * 0.1),
// //           child: SingleChildScrollView(
// //             child: Column(
// //               children: [
// //                 SizedBox(height: size.height * 0.05),
// //                 Center(
// //                   child: Container(
// //                     padding: const EdgeInsets.all(16.0),
// //                     decoration: BoxDecoration(
// //                       color: Colors.white,
// //                       borderRadius: BorderRadius.circular(10),
// //                       boxShadow: [
// //                         BoxShadow(
// //                           color: Colors.grey.withOpacity(0.5),
// //                           spreadRadius: 2,
// //                           blurRadius: 5,
// //                           offset: const Offset(0, 3),
// //                         ),
// //                       ],
// //                     ),
// //                     child: Column(
// //                       mainAxisSize: MainAxisSize.min,
// //                       children: [
// //                         Image.asset('assets/Logo.png',
// //                             height: size.height * 0.17),
// //                         SizedBox(height: size.height * 0.03),
// //                         phone(),
// //                         SizedBox(height: size.height * 0.02),
// //                         password(),
// //                         SizedBox(height: size.height * 0.02),
// //                         Center(
// //                           child: TextButton(
// //                             onPressed: () {
// //                               Get.to(() => const ForgetPassword());
// //                             },
// //                             child: const Text(
// //                               'Forgot password?',
// //                               style: TextStyle(
// //                                 fontSize: 16,
// //                                 fontWeight: FontWeight.w600,
// //                                 color: Color.fromARGB(240, 0, 131, 143),
// //                               ),
// //                             ),
// //                           ),
// //                         ),
// //                         Obx(() {
// //                           if (_controller.isLoading.value) {
// //                             return const Center(
// //                               child: CircularProgressIndicator(),
// //                             );
// //                           } else {
// //                             return Column(
// //                               children: [
// //                                 AppButton(
// //                                   name: 'Login',
// //                                   onPressed: () async {
// //                                     if (_formKey.currentState!.validate()) {
// //                                       FocusScope.of(context)
// //                                           .requestFocus(FocusNode());

// //                                       bool? isAuthenticated =
// //                                           await _controller.login(
// //                                         phoneController,
// //                                         passwordController,
// //                                       );

// //                                       var prefs =
// //                                           await SharedPreferences.getInstance();
// //                                       isAuthenticated =
// //                                           prefs.getBool("isAuthenticated") ??
// //                                               false;

// //                                       log('Login successful: $isAuthenticated');
// //                                       canCheckBiometrics =
// //                                           await auth.canCheckBiometrics;

// //                                       if (isAuthenticated == true &&
// //                                           canCheckBiometrics == true) {
// //                                         if (!_isFingerprintEnabled) {
// //                                           bool enableFingerprint =
// //                                               await showDialog(
// //                                             context: context,
// //                                             builder: (context) {
// //                                               return AlertDialog(
// //                                                 title: const Text(
// //                                                     "Enable Fingerprint Login"),
// //                                                 content: const Text(
// //                                                     "Would you like to enable fingerprint login for future logins?"),
// //                                                 actions: [
// //                                                   TextButton(
// //                                                     child: const Text("Cancel"),
// //                                                     onPressed: () =>
// //                                                         Navigator.of(context)
// //                                                             .pop(false),
// //                                                   ),
// //                                                   TextButton(
// //                                                     child: const Text("Enable"),
// //                                                     onPressed: () =>
// //                                                         Navigator.of(context)
// //                                                             .pop(true),
// //                                                   ),
// //                                                 ],
// //                                               );
// //                                             },
// //                                           );

// //                                           log('User opted to enable fingerprint: $enableFingerprint');

// //                                           if (enableFingerprint) {
// //                                             await _enableFingerprint(); // Enable fingerprint login
// //                                           }
// //                                         }
// //                                       } else {}
// //                                     } else {
// //                                       CustomSnackBar.showSnackBar(
// //                                         title: "Please fill all fields",
// //                                         color: Colors.red,
// //                                       );
// //                                     }
// //                                   },
// //                                 ),
// //                                 SizedBox(height: size.height * 0.02),
// //                                 if (_isFingerprintEnabled)
// //                                   IconButton(
// //                                     icon:
// //                                         const Icon(Icons.fingerprint, size: 48),
// //                                     color: const Color.fromRGBO(0, 131, 143, 1),
// //                                     onPressed: _authenticateWithBiometrics,
// //                                   ),
// //                               ],
// //                             );
// //                           }
// //                         }),
// //                         SizedBox(height: size.height * 0.02),
// //                         Row(
// //                           mainAxisAlignment: MainAxisAlignment.center,
// //                           children: [
// //                             const Text(
// //                               "Don't have an Account?",
// //                               style: TextStyle(
// //                                 fontSize: 16,
// //                                 fontWeight: FontWeight.w500,
// //                                 color: Colors.black,
// //                               ),
// //                             ),
// //                             TextButton(
// //                               onPressed: () {
// //                                 Get.to(() => UserRegistration());
// //                               },
// //                               child: const Text(
// //                                 'Signup',
// //                                 style: TextStyle(
// //                                   fontSize: 16,
// //                                   fontWeight: FontWeight.w600,
// //                                   color: Color.fromARGB(240, 0, 131, 143),
// //                                 ),
// //                               ),
// //                             ),
// //                           ],
// //                         ),
// //                       ],
// //                     ),
// //                   ),
// //                 ),
// //               ],
// //             ),
// //           ),
// //         ),
// //       ),
// //     );
// //   }

// //   phone() {
// //     Size size = MediaQuery.of(context).size;
// //     return Column(
// //       children: [
// //         Row(
// //           children: [
// //             Icon(
// //               Icons.phone,
// //               size: size.aspectRatio * 55,
// //               color: const Color.fromARGB(240, 0, 131, 143),
// //             ),
// //             SizedBox(width: size.width * 0.02),
// //             const Text(
// //               'Phone',
// //               style: TextStyle(
// //                 color: Color.fromARGB(240, 0, 131, 143),
// //                 fontWeight: FontWeight.w500,
// //                 fontSize: 16,
// //               ),
// //             ),
// //           ],
// //         ),
// //         SizedBox(height: size.height * 0.012),
// //         TextFormField(
// //           controller: phoneController,
// //           autovalidateMode: AutovalidateMode.onUserInteraction,
// //           validator: (value) {
// //             if (value == null || value.isEmpty) {
// //               return 'Please enter your phone number';
// //             }
// //             if (value.length != 10) {
// //               return 'Phone number must be exactly 10 digits';
// //             }
// //             if (!RegExp(r'^(98|97|96)\d{8}$').hasMatch(value)) {
// //               return 'Phone number must start with 98, 97, or 96';
// //             }
// //             return null;
// //           },
// //           textAlign: TextAlign.left,
// //           style: const TextStyle(color: Colors.black),
// //           keyboardType: TextInputType.phone,
// //           inputFormatters: [
// //             FilteringTextInputFormatter.digitsOnly,
// //             LengthLimitingTextInputFormatter(10),
// //           ],
// //           decoration: InputDecoration(
// //             fillColor: Colors.white,
// //             filled: true,
// //             border: OutlineInputBorder(borderRadius: BorderRadius.circular(5)),
// //             focusedBorder: OutlineInputBorder(
// //               borderRadius: BorderRadius.circular(5),
// //               borderSide:
// //                   const BorderSide(color: Color.fromARGB(240, 0, 131, 143)),
// //             ),
// //             enabledBorder: OutlineInputBorder(
// //               borderRadius: BorderRadius.circular(5),
// //               borderSide:
// //                   const BorderSide(color: Color.fromARGB(240, 0, 131, 143)),
// //             ),
// //             hintText: 'Enter your phone number',
// //             hintStyle: const TextStyle(color: Colors.grey),
// //           ),
// //         ),
// //       ],
// //     );
// //   }

// //   password() {
// //     Size size = MediaQuery.of(context).size;
// //     return Column(
// //       children: [
// //         Row(
// //           children: [
// //             Icon(
// //               Icons.lock,
// //               size: size.aspectRatio * 55,
// //               color: const Color.fromARGB(240, 0, 131, 143),
// //             ),
// //             SizedBox(width: size.width * 0.02),
// //             const Text(
// //               'Password',
// //               style: TextStyle(
// //                 color: Color.fromARGB(240, 0, 131, 143),
// //                 fontWeight: FontWeight.w500,
// //                 fontSize: 16,
// //               ),
// //             ),
// //           ],
// //         ),
// //         SizedBox(height: size.height * 0.012),
// //         TextFormField(
// //           controller: passwordController,
// //           autovalidateMode: AutovalidateMode.onUserInteraction,
// //           validator: (value) {
// //             if (value == null || value.isEmpty) {
// //               return 'Please enter your password';
// //             }
// //             return null;
// //           },
// //           obscureText: !isVisible,
// //           textAlign: TextAlign.left,
// //           style: const TextStyle(color: Colors.black),
// //           decoration: InputDecoration(
// //             fillColor: Colors.white,
// //             filled: true,
// //             border: OutlineInputBorder(borderRadius: BorderRadius.circular(5)),
// //             focusedBorder: OutlineInputBorder(
// //               borderRadius: BorderRadius.circular(5),
// //               borderSide:
// //                   const BorderSide(color: Color.fromARGB(240, 0, 131, 143)),
// //             ),
// //             enabledBorder: OutlineInputBorder(
// //               borderRadius: BorderRadius.circular(5),
// //               borderSide:
// //                   const BorderSide(color: Color.fromARGB(240, 0, 131, 143)),
// //             ),
// //             hintText: 'Enter your password',
// //             hintStyle: const TextStyle(color: Colors.grey),
// //             suffixIcon: IconButton(
// //               icon: Icon(
// //                 isVisible ? Icons.visibility : Icons.visibility_off,
// //               ),
// //               onPressed: () {
// //                 setState(() {
// //                   isVisible = !isVisible;
// //                 });
// //               },
// //             ),
// //           ),
// //         ),
// //       ],
// //     );
// //   }
// // }

// import 'dart:developer';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:get/get.dart';
// import 'package:local_auth/local_auth.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smartsewa/core/development/console.dart';
// import 'package:smartsewa/views/auth/registration/user_registration.dart';
// import 'package:smartsewa/views/user_screen/main_screen.dart';
// import 'package:smartsewa/views/widgets/custom_snackbar.dart';
// import 'package:smartsewa/views/widgets/my_appbar.dart';
// import '../../../network/services/authServices/auth_controller.dart';
// import '../../widgets/buttons/app_buttons.dart';
// import '../forgotPassword/forget_password_mobile.dart';

// class LoginScreen extends StatefulWidget {
//   const LoginScreen({super.key});

//   @override
//   State<LoginScreen> createState() => _LoginScreenState();
// }

// class _LoginScreenState extends State<LoginScreen> {
//   bool isVisible = false;
//   final _formKey = GlobalKey<FormState>();
//   final _controller = Get.put(AuthController());
//   final phoneController = TextEditingController();
//   final passwordController = TextEditingController();
//   final LocalAuthentication auth = LocalAuthentication();
//   bool _isAuthenticating = false;
//   bool _isAuthenticated = false;
//   bool _isFingerprintEnabled = false;
//   bool canCheckBiometrics = false;
//   bool _isGuestLoading = false;
//   bool _isPressed = false;
//   bool _isLoading = false;

//   @override
//   void initState() {
//     super.initState();
//     _isAuthenticated = false;
//     _checkFingerprintStatus(); // Check if fingerprint is enabled on app startup
//   }

//   void _handleLogin() async {
//     try {
//       setState(() => _isLoading = true);

//       bool? isAuthenticated =
//           await _controller.login(phoneController, passwordController);

//       var prefs = await SharedPreferences.getInstance();
//       isAuthenticated = prefs.getBool("isAuthenticated") ?? false;

//       log('Login successful: $isAuthenticated');

//       if (isAuthenticated) {
//         CustomSnackBar.showSnackBar(
//           title: "Login Successful",
//           color: const Color.fromRGBO(0, 131, 143, 1),
//         );
//         Get.offAll(() => const MainScreen());
//       } else {
//         CustomSnackBar.showSnackBar(
//           title: "Invalid credentials, please try again",
//           color: Colors.red,
//         );
//       }
//     } catch (e) {
//       log('Login error: $e');
//       CustomSnackBar.showSnackBar(
//         title: "Something went wrong, please try again",
//         color: Colors.red,
//       );
//     } finally {
//       setState(() => _isLoading = false);
//     }
//   }

//   void _handleGuestLogin() async {
//     try {
//       setState(() => _isGuestLoading = true);

//       bool success = await _controller.handleGuestLogin();
//       String token =
//           "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI5ODQxMzcxMTE5IiwiaWF0IjoxNzM4NDk3NTg2LCJleHAiOjU3NTAwMzUxMTg5NDE1MzN9.NG8Pk4uZ0JLlXJvo2D9bpZQYOHNbiyZfkWcMO_CVVxk";
//       if (success) {
//         SharedPreferences prefs = await SharedPreferences.getInstance();
//         await prefs.setString('token', token);

//         CustomSnackBar.showSnackBar(
//           title: "Logged in as Guest",
//           color: const Color.fromRGBO(0, 131, 143, 1),
//         );
//         Get.offAll(() => const MainScreen());
//       } else {
//         CustomSnackBar.showSnackBar(
//           title: "Failed to login as Guest",
//           color: Colors.red,
//         );
//       }
//     } catch (e) {
//       log('Guest login error: $e');
//       CustomSnackBar.showSnackBar(
//         title: "Error logging in as Guest",
//         color: Colors.red,
//       );
//     } finally {
//       setState(() => _isGuestLoading = false);
//     }
//   }

//   Future<void> _checkFingerprintStatus() async {
//     SharedPreferences preferences = await SharedPreferences.getInstance();
//     bool? fingerprintStatus = preferences.getBool('fingerprintEnabled');
//     log('Fingerprint status from SharedPreferences: $fingerprintStatus');

//     setState(() {
//       _isFingerprintEnabled = fingerprintStatus ?? false; // Handles null case
//     });
//   }

//   Future<void> _authenticateWithBiometrics() async {
//     try {
//       setState(() {
//         _isAuthenticating = true;
//       });

//       bool canCheckBiometrics = await auth.canCheckBiometrics;
//       if (!canCheckBiometrics) {
//         CustomSnackBar.showSnackBar(
//           title: "Biometric authentication not available.",
//           color: Colors.red,
//         );
//         return;
//       }

//       final bool authenticated = await auth.authenticate(
//         localizedReason: 'Please authenticate to log in',
//         options: const AuthenticationOptions(biometricOnly: true),
//       );

//       setState(() {
//         _isAuthenticated = authenticated;
//         _isAuthenticating = false;
//       });

//       if (authenticated) {
//         SharedPreferences pref = await SharedPreferences.getInstance();
//         phoneController.text = pref.getString('username')!;
//         passwordController.text = pref.getString('password')!;

//         _controller.login(phoneController, passwordController);
//       }
//     } on PlatformException catch (e) {
//       log(e.toString());
//       setState(() {
//         _isAuthenticating = false;
//       });
//       CustomSnackBar.showSnackBar(
//         title: "Authentication failed. Please try again.",
//         color: Colors.red,
//       );
//     }
//   }

//   Future<void> _enableFingerprint() async {
//     SharedPreferences preferences = await SharedPreferences.getInstance();
//     await preferences.setBool('fingerprintEnabled', true);
//     setState(() {
//       _isFingerprintEnabled = true;
//     });
//     CustomSnackBar.showSnackBar(
//       title: "Fingerprint authentication enabled.",
//       color: const Color.fromRGBO(0, 131, 143, 1),
//     );
//   }

//   Widget _buildGuestLoginButton() {
//     return LayoutBuilder(
//       builder: (context, constraints) {
//         // Get screen dimensions
//         final screenSize = MediaQuery.of(context).size;
//         final screenWidth = screenSize.width;
//         final screenHeight = screenSize.height;

//         // Calculate responsive dimensions
//         final fontSize = (screenWidth * 0.04).clamp(14.0, 20.0);
//         final loadingSize = (screenWidth * 0.04).clamp(16.0, 24.0);
//         final strokeWidth = (screenWidth * 0.005).clamp(2.0, 3.0);
//         final marginTop = (screenHeight * 0.01).clamp(6.0, 12.0);
//         final horizontalPadding = (screenWidth * 0.02).clamp(8.0, 16.0);
//         final containerHeight = (screenHeight * 0.05).clamp(35.0, 50.0);

//         return AnimatedScale(
//           scale: _isGuestLoading ? 0.95 : 1.0,
//           duration: const Duration(milliseconds: 200),
//           child: Container(
//             height: containerHeight,
//             margin: EdgeInsets.only(top: marginTop),
//             // Add subtle hover effect
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(containerHeight * 0.25),
//               color: Colors.transparent,
//             ),
//             child: Material(
//               color: Colors.transparent,
//               child: InkWell(
//                 borderRadius: BorderRadius.circular(containerHeight * 0.25),
//                 onTap: _isGuestLoading ? null : _handleGuestLogin,
//                 splashColor: const Color.fromRGBO(0, 131, 143, 0.1),
//                 highlightColor: const Color.fromRGBO(0, 131, 143, 0.05),
//                 child: Padding(
//                   padding: EdgeInsets.symmetric(
//                     horizontal: horizontalPadding,
//                     vertical: containerHeight * 0.2,
//                   ),
//                   child: Row(
//                     mainAxisSize: MainAxisSize.min,
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       _isGuestLoading
//                           ? SizedBox(
//                               height: loadingSize,
//                               width: loadingSize,
//                               child: CircularProgressIndicator(
//                                 color: const Color.fromRGBO(0, 131, 143, 1),
//                                 strokeWidth: strokeWidth,
//                               ),
//                             )
//                           : Row(
//                               mainAxisSize: MainAxisSize.min,
//                               children: [
//                                 Text(
//                                   'Guest Login',
//                                   style: TextStyle(
//                                     color: const Color.fromRGBO(0, 131, 143, 1),
//                                     fontSize: fontSize,
//                                     fontWeight: FontWeight.bold,
//                                   ),
//                                 ),
//                                 SizedBox(width: horizontalPadding * 0.25),
//                                 Text(
//                                   '?',
//                                   style: TextStyle(
//                                     color: const Color.fromRGBO(0, 131, 143, 1),
//                                     fontSize: fontSize,
//                                     fontWeight: FontWeight.bold,
//                                   ),
//                                 ),
//                               ],
//                             ),
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     Size size = MediaQuery.of(context).size;
//     return Scaffold(
//       appBar: myAppbar(context, true, ""),
//       body: Form(
//         key: _formKey,
//         child: Padding(
//           padding: EdgeInsets.symmetric(horizontal: size.width * 0.1),
//           child: SingleChildScrollView(
//             child: Column(
//               children: [
//                 SizedBox(height: size.height * 0.05),
//                 Center(
//                   child: Container(
//                     padding: const EdgeInsets.all(16.0),
//                     decoration: BoxDecoration(
//                       color: Colors.white,
//                       borderRadius: BorderRadius.circular(10),
//                       boxShadow: [
//                         BoxShadow(
//                           color: Colors.grey.withOpacity(0.5),
//                           spreadRadius: 2,
//                           blurRadius: 5,
//                           offset: const Offset(0, 3),
//                         ),
//                       ],
//                     ),
//                     child: Column(
//                       mainAxisSize: MainAxisSize.min,
//                       children: [
//                         Image.asset('assets/Logo.png',
//                             height: size.height * 0.17),
//                         SizedBox(height: size.height * 0.03),
//                         phone(),
//                         SizedBox(height: size.height * 0.02),
//                         password(),
//                         SizedBox(height: size.height * 0.02),
//                         Center(
//                           child: TextButton(
//                             onPressed: () {
//                               Get.to(() => const ForgetPassword());
//                             },
//                             child: const Text(
//                               'Forgot password?',
//                               style: TextStyle(
//                                 fontSize: 16,
//                                 fontWeight: FontWeight.w600,
//                                 color: Color.fromARGB(240, 0, 131, 143),
//                               ),
//                             ),
//                           ),
//                         ),
//                         Obx(() {
//                           return AnimatedSwitcher(
//                             duration: const Duration(milliseconds: 300),
//                             child: _controller.isLoading.value
//                                 ? const Center(
//                                     child: CircularProgressIndicator(),
//                                   )
//                                 : Column(
//                                     children: [
//                                       AppButton(
//                                         name: 'Login',
//                                         onPressed: () async {
//                                           if (_formKey.currentState!
//                                               .validate()) {
//                                             FocusScope.of(context)
//                                                 .unfocus(); // Hide keyboard

//                                             _controller.isLoading.value =
//                                                 true; // Show loading state

//                                             bool? isAuthenticated =
//                                                 await _controller.login(
//                                               phoneController,
//                                               passwordController,
//                                             );

//                                             var prefs = await SharedPreferences
//                                                 .getInstance();
//                                             isAuthenticated = prefs.getBool(
//                                                     "isAuthenticated") ??
//                                                 false;

//                                             log('Login successful: $isAuthenticated');
//                                             canCheckBiometrics =
//                                                 await auth.canCheckBiometrics;

//                                             if (isAuthenticated &&
//                                                 canCheckBiometrics) {
//                                               if (!_isFingerprintEnabled) {
//                                                 bool enableFingerprint =
//                                                     await showDialog(
//                                                   context: context,
//                                                   builder: (context) {
//                                                     return AlertDialog(
//                                                       title: const Text(
//                                                           "Enable Fingerprint Login"),
//                                                       content: const Text(
//                                                           "Would you like to enable fingerprint login for future logins?"),
//                                                       actions: [
//                                                         TextButton(
//                                                           child: const Text(
//                                                               "Cancel"),
//                                                           onPressed: () =>
//                                                               Navigator.of(
//                                                                       context)
//                                                                   .pop(false),
//                                                         ),
//                                                         TextButton(
//                                                           child: const Text(
//                                                               "Enable"),
//                                                           onPressed: () =>
//                                                               Navigator.of(
//                                                                       context)
//                                                                   .pop(true),
//                                                         ),
//                                                       ],
//                                                     );
//                                                   },
//                                                 );

//                                                 log('User opted to enable fingerprint: $enableFingerprint');

//                                                 if (enableFingerprint) {
//                                                   await _enableFingerprint(); // Enable fingerprint login
//                                                 }
//                                               }
//                                             }

//                                             _controller.isLoading.value =
//                                                 false; // Hide loading state
//                                           } else {
//                                             CustomSnackBar.showSnackBar(
//                                               title: "Please fill all fields",
//                                               color: Colors.red,
//                                             );
//                                           }
//                                         },
//                                       ),
//                                       SizedBox(height: size.height * 0.02),
//                                       if (_isFingerprintEnabled)
//                                         IconButton(
//                                           icon: const Icon(Icons.fingerprint,
//                                               size: 48),
//                                           color: const Color.fromRGBO(
//                                               0, 131, 143, 1),
//                                           onPressed:
//                                               _authenticateWithBiometrics,
//                                         ),
//                                       const SizedBox(height: 8),
//                                       _buildGuestLoginButton(),
//                                     ],
//                                   ),
//                           );
//                         }),
//                         SizedBox(height: size.height * 0.02),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             Text(
//                               "Don't have an Account?",
//                               style: TextStyle(
//                                 fontSize:
//                                     MediaQuery.of(context).size.width * 0.04,
//                                 fontWeight: FontWeight.w500,
//                                 color: Colors.black,
//                               ),
//                             ),
//                             TextButton(
//                               onPressed: () {
//                                 Get.to(() => UserRegistration());
//                               },
//                               child: const Text(
//                                 'Signup',
//                                 style: TextStyle(
//                                   fontSize: 16,
//                                   fontWeight: FontWeight.w600,
//                                   color: Color.fromARGB(240, 0, 131, 143),
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }

//   phone() {
//     Size size = MediaQuery.of(context).size;
//     return Column(
//       children: [
//         Row(
//           children: [
//             Icon(
//               Icons.phone,
//               size: size.aspectRatio * 55,
//               color: const Color.fromARGB(240, 0, 131, 143),
//             ),
//             SizedBox(width: size.width * 0.02),
//             const Text(
//               'Phone',
//               style: TextStyle(
//                 color: Color.fromARGB(240, 0, 131, 143),
//                 fontWeight: FontWeight.w500,
//                 fontSize: 16,
//               ),
//             ),
//           ],
//         ),
//         SizedBox(height: size.height * 0.012),
//         TextFormField(
//           controller: phoneController,
//           autovalidateMode: AutovalidateMode.onUserInteraction,
//           validator: (value) {
//             if (value == null || value.isEmpty) {
//               return 'Please enter your phone number';
//             }
//             if (value.length != 10) {
//               return 'Phone number must be exactly 10 digits';
//             }
//             if (!RegExp(r'^(98|97|96)\d{8}$').hasMatch(value)) {
//               return 'Phone number must start with 98, 97, or 96';
//             }
//             return null;
//           },
//           textAlign: TextAlign.left,
//           style: const TextStyle(color: Colors.black),
//           keyboardType: TextInputType.phone,
//           inputFormatters: [
//             FilteringTextInputFormatter.digitsOnly,
//             LengthLimitingTextInputFormatter(10),
//           ],
//           decoration: InputDecoration(
//             fillColor: Colors.white,
//             filled: true,
//             border: OutlineInputBorder(borderRadius: BorderRadius.circular(5)),
//             focusedBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(5),
//               borderSide:
//                   const BorderSide(color: Color.fromARGB(240, 0, 131, 143)),
//             ),
//             enabledBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(5),
//               borderSide:
//                   const BorderSide(color: Color.fromARGB(240, 0, 131, 143)),
//             ),
//             hintText: 'Enter your phone number',
//             hintStyle: const TextStyle(color: Colors.grey),
//           ),
//         ),
//       ],
//     );
//   }

//   password() {
//     Size size = MediaQuery.of(context).size;
//     return Column(
//       children: [
//         Row(
//           children: [
//             Icon(
//               Icons.lock,
//               size: size.aspectRatio * 55,
//               color: const Color.fromARGB(240, 0, 131, 143),
//             ),
//             SizedBox(width: size.width * 0.02),
//             const Text(
//               'Password',
//               style: TextStyle(
//                 color: Color.fromARGB(240, 0, 131, 143),
//                 fontWeight: FontWeight.w500,
//                 fontSize: 16,
//               ),
//             ),
//           ],
//         ),
//         SizedBox(height: size.height * 0.012),
//         TextFormField(
//           controller: passwordController,
//           autovalidateMode: AutovalidateMode.onUserInteraction,
//           validator: (value) {
//             if (value == null || value.isEmpty) {
//               return 'Please enter your password';
//             }
//             return null;
//           },
//           obscureText: !isVisible,
//           textAlign: TextAlign.left,
//           style: const TextStyle(color: Colors.black),
//           decoration: InputDecoration(
//             fillColor: Colors.white,
//             filled: true,
//             border: OutlineInputBorder(borderRadius: BorderRadius.circular(5)),
//             focusedBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(5),
//               borderSide:
//                   const BorderSide(color: Color.fromARGB(240, 0, 131, 143)),
//             ),
//             enabledBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(5),
//               borderSide:
//                   const BorderSide(color: Color.fromARGB(240, 0, 131, 143)),
//             ),
//             hintText: 'Enter your password',
//             hintStyle: const TextStyle(color: Colors.grey),
//             suffixIcon: IconButton(
//               icon: Icon(
//                 isVisible ? Icons.visibility : Icons.visibility_off,
//               ),
//               onPressed: () {
//                 setState(() {
//                   isVisible = !isVisible;
//                 });
//               },
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }

import 'dart:developer';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/views/auth/registration/user_registration.dart';
import 'package:smartsewa/views/user_screen/main_screen.dart';
import 'package:smartsewa/views/widgets/custom_snackbar.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';
import 'package:upgrader/upgrader.dart';
import '../../../network/services/authServices/auth_controller.dart';
import '../../widgets/buttons/app_buttons.dart';
import '../forgotPassword/forget_password_mobile.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  bool isVisible = false;
  final _formKey = GlobalKey<FormState>();
  final _controller = Get.put(AuthController());
  final phoneController = TextEditingController();
  final passwordController = TextEditingController();
  final LocalAuthentication auth = LocalAuthentication();
  bool _isAuthenticating = false;
  bool _isAuthenticated = false;
  bool _isFingerprintEnabled = false;
  bool canCheckBiometrics = false;
  bool _isGuestLoading = false;
  bool _isPressed = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _isAuthenticated = false;
    _checkFingerprintStatus(); // Check if fingerprint is enabled on app startup
  }

  void _handleLogin() async {
    try {
      setState(() => _isLoading = true);

      bool? isAuthenticated =
          await _controller.login(phoneController, passwordController);

      var prefs = await SharedPreferences.getInstance();
      isAuthenticated = prefs.getBool("isAuthenticated") ?? false;

      log('Login successful: $isAuthenticated');

      if (isAuthenticated) {
        CustomSnackBar.showSnackBar(
          title: "Login Successful",
          color: const Color.fromRGBO(0, 131, 143, 1),
        );
        Get.offAll(() => const MainScreen());
      } else {
        CustomSnackBar.showSnackBar(
          title: "Invalid credentials, please try again",
          color: Colors.red,
        );
      }
    } catch (e) {
      log('Login error: $e');
      CustomSnackBar.showSnackBar(
        title: "Something went wrong, please try again",
        color: Colors.red,
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _handleGuestLogin() async {
    try {
      setState(() => _isGuestLoading = true);

      bool success = await _controller.handleGuestLogin();
      String token =
          "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI5ODQxMzcxMTE5IiwiaWF0IjoxNzM4NDk3NTg2LCJleHAiOjU3NTAwMzUxMTg5NDE1MzN9.NG8Pk4uZ0JLlXJvo2D9bpZQYOHNbiyZfkWcMO_CVVxk";
      if (success) {
        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setString('token', token);

        CustomSnackBar.showSnackBar(
          title: "Logged in as Guest",
          color: const Color.fromRGBO(0, 131, 143, 1),
        );
        Get.offAll(() => const MainScreen());
      } else {
        CustomSnackBar.showSnackBar(
          title: "Failed to login as Guest",
          color: Colors.red,
        );
      }
    } catch (e) {
      log('Guest login error: $e');
      CustomSnackBar.showSnackBar(
        title: "Error logging in as Guest",
        color: Colors.red,
      );
    } finally {
      setState(() => _isGuestLoading = false);
    }
  }

  Future<void> _checkFingerprintStatus() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool? fingerprintStatus = preferences.getBool('fingerprintEnabled');
    log('Fingerprint status from SharedPreferences: $fingerprintStatus');

    setState(() {
      _isFingerprintEnabled = fingerprintStatus ?? false; // Handles null case
    });
  }

  Future<void> _authenticateWithBiometrics() async {
    try {
      setState(() {
        _isAuthenticating = true;
      });

      bool canCheckBiometrics = await auth.canCheckBiometrics;
      if (!canCheckBiometrics) {
        CustomSnackBar.showSnackBar(
          title: "Biometric authentication not available.",
          color: Colors.red,
        );
        return;
      }

      final bool authenticated = await auth.authenticate(
        localizedReason: 'Please authenticate to log in',
        options: const AuthenticationOptions(biometricOnly: true),
      );

      setState(() {
        _isAuthenticated = authenticated;
        _isAuthenticating = false;
      });

      if (authenticated) {
        SharedPreferences pref = await SharedPreferences.getInstance();
        phoneController.text = pref.getString('username')!;
        passwordController.text = pref.getString('password')!;

        _controller.login(phoneController, passwordController);
      }
    } on PlatformException catch (e) {
      log(e.toString());
      setState(() {
        _isAuthenticating = false;
      });
      CustomSnackBar.showSnackBar(
        title: "Authentication failed. Please try again.",
        color: Colors.red,
      );
    }
  }

  Future<void> _enableFingerprint() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    await preferences.setBool('fingerprintEnabled', true);

    // Use a post-frame callback to ensure the state update happens after the current build cycle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _isFingerprintEnabled = true;
        });
      }
    });

    CustomSnackBar.showSnackBar(
      title: "Fingerprint authentication enabled.",
      color: const Color.fromRGBO(0, 131, 143, 1),
    );
  }

  Widget _buildGuestLoginButton() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Get screen dimensions
        final screenSize = MediaQuery.of(context).size;
        final screenWidth = screenSize.width;
        final screenHeight = screenSize.height;

        // Calculate responsive dimensions
        final fontSize = (screenWidth * 0.04).clamp(14.0, 20.0);
        final loadingSize = (screenWidth * 0.04).clamp(16.0, 24.0);
        final strokeWidth = (screenWidth * 0.005).clamp(2.0, 3.0);
        final marginTop = (screenHeight * 0.01).clamp(6.0, 12.0);
        final horizontalPadding = (screenWidth * 0.02).clamp(8.0, 16.0);
        final containerHeight = (screenHeight * 0.05).clamp(35.0, 50.0);

        return AnimatedScale(
          scale: _isGuestLoading ? 0.95 : 1.0,
          duration: const Duration(milliseconds: 200),
          child: Container(
            height: containerHeight,
            margin: EdgeInsets.only(top: marginTop),
            // Add subtle hover effect
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(containerHeight * 0.25),
              color: Colors.transparent,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(containerHeight * 0.25),
                onTap: _isGuestLoading ? null : _handleGuestLogin,
                splashColor: const Color.fromRGBO(0, 131, 143, 0.1),
                highlightColor: const Color.fromRGBO(0, 131, 143, 0.05),
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: horizontalPadding,
                    vertical: containerHeight * 0.2,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _isGuestLoading
                          ? SizedBox(
                              height: loadingSize,
                              width: loadingSize,
                              child: CircularProgressIndicator(
                                color: const Color.fromRGBO(0, 131, 143, 1),
                                strokeWidth: strokeWidth,
                              ),
                            )
                          : Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'Guest Login',
                                  style: TextStyle(
                                    color: const Color.fromRGBO(0, 131, 143, 1),
                                    fontSize: fontSize,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(width: horizontalPadding * 0.25),
                                Text(
                                  '?',
                                  style: TextStyle(
                                    color: const Color.fromRGBO(0, 131, 143, 1),
                                    fontSize: fontSize,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // Extract the login container styling to a separate method to ensure consistency
  Widget _buildLoginContainer(Size size) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white, // Ensure white background is always maintained
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset('assets/Logo.png', height: size.height * 0.17),
          SizedBox(height: size.height * 0.03),
          phone(),
          SizedBox(height: size.height * 0.02),
          password(),
          SizedBox(height: size.height * 0.02),
          Center(
            child: TextButton(
              onPressed: () {
                Get.to(() => const ForgetPassword());
              },
              child: const Text(
                'Forgot password?',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color.fromARGB(240, 0, 131, 143),
                ),
              ),
            ),
          ),
          Obx(() {
            return AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: _controller.isLoading.value
                  ? const Center(
                      child: CircularProgressIndicator(),
                    )
                  : Column(
                      children: [
                        AppButton(
                          name: 'Login',
                          onPressed: () async {
                            if (_formKey.currentState!.validate()) {
                              FocusScope.of(context).unfocus(); // Hide keyboard

                              _controller.isLoading.value =
                                  true; // Show loading state

                              bool? isAuthenticated = await _controller.login(
                                phoneController,
                                passwordController,
                              );

                              var prefs = await SharedPreferences.getInstance();
                              isAuthenticated =
                                  prefs.getBool("isAuthenticated") ?? false;

                              log('Login successful: $isAuthenticated');
                              canCheckBiometrics =
                                  await auth.canCheckBiometrics;

                              if (isAuthenticated && canCheckBiometrics) {
                                if (!_isFingerprintEnabled) {
                                  bool enableFingerprint = await showDialog(
                                    context: context,
                                    builder: (context) {
                                      return AlertDialog(
                                        title: const Text(
                                            "Enable Fingerprint Login"),
                                        content: const Text(
                                            "Would you like to enable fingerprint login for future logins?"),
                                        actions: [
                                          TextButton(
                                            child: const Text("Cancel"),
                                            onPressed: () =>
                                                Navigator.of(context)
                                                    .pop(false),
                                          ),
                                          TextButton(
                                            child: const Text("Enable"),
                                            onPressed: () =>
                                                Navigator.of(context).pop(true),
                                          ),
                                        ],
                                      );
                                    },
                                  );

                                  log('User opted to enable fingerprint: $enableFingerprint');

                                  if (enableFingerprint) {
                                    // Store credentials for biometric login
                                    SharedPreferences prefs =
                                        await SharedPreferences.getInstance();
                                    await prefs.setString(
                                        'username', phoneController.text);
                                    await prefs.setString(
                                        'password', passwordController.text);

                                    await _enableFingerprint(); // Enable fingerprint login
                                  }
                                }
                              }

                              _controller.isLoading.value =
                                  false; // Hide loading state
                            } else {
                              CustomSnackBar.showSnackBar(
                                title: "Please fill all fields",
                                color: Colors.red,
                              );
                            }
                          },
                        ),
                        SizedBox(height: size.height * 0.02),
                        if (_isFingerprintEnabled)
                          IconButton(
                            icon: const Icon(Icons.fingerprint, size: 48),
                            color: const Color.fromRGBO(0, 131, 143, 1),
                            onPressed: _authenticateWithBiometrics,
                          ),
                        const SizedBox(height: 8),
                        _buildGuestLoginButton(),
                      ],
                    ),
            );
          }),
          SizedBox(height: size.height * 0.02),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "Don't have an Account?",
                style: TextStyle(
                  fontSize: MediaQuery.of(context).size.width * 0.04,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
              TextButton(
                onPressed: () {
                  Get.to(() => UserRegistration());
                },
                child: const Text(
                  'Signup',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color.fromARGB(240, 0, 131, 143),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: size.height * 0.02),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return UpgradeAlert(
      upgrader: Upgrader(
        debugLogging: true,
        debugDisplayAlways: false,
        countryCode: 'NP',
        durationUntilAlertAgain: const Duration(days: 1),
        minAppVersion: '1.0.61',
      ),
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5), // Light gray background
        appBar: myAppbar(context, true, ""),
        body: Form(
          key: _formKey,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: size.width * 0.08),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(height: size.height * 0.02),
                  Center(
                    child:
                        _buildLoginContainer(size), // Use the extracted method
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  phone() {
    Size size = MediaQuery.of(context).size;
    return Column(
      children: [
        Row(
          children: [
            Icon(
              Icons.phone,
              size: size.aspectRatio * 55,
              color: const Color.fromARGB(240, 0, 131, 143),
            ),
            SizedBox(width: size.width * 0.02),
            const Text(
              'Phone',
              style: TextStyle(
                color: Color.fromARGB(240, 0, 131, 143),
                fontWeight: FontWeight.w500,
                fontSize: 16,
              ),
            ),
          ],
        ),
        SizedBox(height: size.height * 0.012),
        TextFormField(
          controller: phoneController,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your phone number';
            }
            if (value.length != 10) {
              return 'Phone number must be exactly 10 digits';
            }
            if (!RegExp(r'^(98|97|96)\d{8}$').hasMatch(value)) {
              return 'Phone number must start with 98, 97, or 96';
            }
            return null;
          },
          textAlign: TextAlign.left,
          style: const TextStyle(color: Colors.black),
          keyboardType: TextInputType.phone,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(10),
          ],
          decoration: InputDecoration(
            fillColor: Colors.white,
            filled: true,
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(5)),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5),
              borderSide:
                  const BorderSide(color: Color.fromARGB(240, 0, 131, 143)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5),
              borderSide:
                  const BorderSide(color: Color.fromARGB(240, 0, 131, 143)),
            ),
            hintText: 'Enter your phone number',
            hintStyle: const TextStyle(color: Colors.grey),
          ),
        ),
      ],
    );
  }

  password() {
    Size size = MediaQuery.of(context).size;
    return Column(
      children: [
        Row(
          children: [
            Icon(
              Icons.lock,
              size: size.aspectRatio * 55,
              color: const Color.fromARGB(240, 0, 131, 143),
            ),
            SizedBox(width: size.width * 0.02),
            const Text(
              'Password',
              style: TextStyle(
                color: Color.fromARGB(240, 0, 131, 143),
                fontWeight: FontWeight.w500,
                fontSize: 16,
              ),
            ),
          ],
        ),
        SizedBox(height: size.height * 0.012),
        TextFormField(
          controller: passwordController,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your password';
            }
            return null;
          },
          obscureText: !isVisible,
          textAlign: TextAlign.left,
          style: const TextStyle(color: Colors.black),
          decoration: InputDecoration(
            fillColor: Colors.white,
            filled: true,
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(5)),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5),
              borderSide:
                  const BorderSide(color: Color.fromARGB(240, 0, 131, 143)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5),
              borderSide:
                  const BorderSide(color: Color.fromARGB(240, 0, 131, 143)),
            ),
            hintText: 'Enter your password',
            hintStyle: const TextStyle(color: Colors.grey),
            suffixIcon: IconButton(
              icon: Icon(
                isVisible ? Icons.visibility : Icons.visibility_off,
              ),
              onPressed: () {
                setState(() {
                  isVisible = !isVisible;
                });
              },
            ),
          ),
        ),
      ],
    );
  }
}
