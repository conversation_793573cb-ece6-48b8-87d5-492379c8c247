// // // ignore_for_file: public_member_api_docs, sort_constructors_first
// // import 'dart:async';

// // import 'package:flutter/material.dart';
// // import 'package:geolocator/geolocator.dart';
// // import 'package:get/get.dart';

// // import 'package:google_maps_flutter/google_maps_flutter.dart';
// // import 'package:shared_preferences/shared_preferences.dart';
// // import 'package:smartsewa/core/development/console.dart';

// // import '../../../core/states.dart';
// // import '../../widgets/my_appbar.dart';
// // import 'package:geocoding/geocoding.dart';

// // import 'map_controller.dart';

// // typedef LocationCallback = void Function(LatLng location);

// // class OpenMapScreen extends StatefulWidget {
// //   GoogleMapController? googleMapController;
// //   final mapController = Get.put(MapController());
// //   Completer<GoogleMapController> completeGoogleMapController;

// //   final LocationCallback onLocationSelected;

// //   CameraPosition kGoogle;
// //   final List<Marker> marker;
// //   bool onpressed;

// //   OpenMapScreen({
// //     super.key,
// //     this.googleMapController,
// //     required this.completeGoogleMapController,
// //     required this.kGoogle,
// //     required this.onpressed,
// //     required this.marker,
// //     required this.onLocationSelected,
// //   });

// //   @override
// //   State<OpenMapScreen> createState() => _OpenMapScreenState();
// // }

// // class _OpenMapScreenState extends State<OpenMapScreen> {
// //   final TextEditingController _workingAddressController =
// //       TextEditingController();

// //   LocationPermission? permission;
// //   Position? position;

// //   // @override
// //   // void initState() {
// //   //   super.initState();

// //   //   getUserCurrentLocation().then((position) {
// //   //     if (position != null) {
// //   //       widget.googleMapController?.animateCamera(CameraUpdate.newLatLngZoom(
// //   //         LatLng(position.latitude, position.longitude),
// //   //         18.0,
// //   //       ));
// //   //       setState(() {
// //   //         widget.marker.add(
// //   //           Marker(
// //   //             markerId: const MarkerId('currentLocation'),
// //   //             position: LatLng(position.latitude, position.longitude),
// //   //             infoWindow: const InfoWindow(title: 'Your Location'),
// //   //           ),
// //   //         );

// //   //         consolelog("Prajwal ${widget.onLocationSelected}");

// //   //         //  widget.marker.add(
// //   //         //   Marker(
// //   //         //     markerId: const MarkerId('Previously Selected Location'),
// //   //         //     position: LatLng(position.latitude, position.longitude),
// //   //         //     infoWindow: const InfoWindow(title: 'Your Location'),
// //   //         //   ),
// //   //         // );
// //   //       });
// //   //       startTrackingLocation();
// //   //     }
// //   //   });

// //   //   //getUserCurrentLocation();
// //   //   // initialGeo();
// //   // }

// //   @override
// //   void initState() {
// //     super.initState();

// //     // First check for saved location in shared preferences
// //     _checkSavedLocation().then((savedPosition) {
// //       if (savedPosition != null) {
// //         // Display the saved location
// //         _showSavedLocation(savedPosition);
// //       } else {
// //         // If no saved location, get current location
// //         getUserCurrentLocation().then((position) {
// //           if (position != null) {
// //             _showCurrentLocation(position);
// //           }
// //         });
// //       }
// //     });
// //   }

// //   Future<LatLng?> _checkSavedLocation() async {
// //     SharedPreferences prefs = await SharedPreferences.getInstance();
// //     String? savedLatLng = prefs.getString("latitude");

// //     if (savedLatLng != null && savedLatLng.isNotEmpty) {
// //       List<String> coordinates = savedLatLng.split(',');
// //       if (coordinates.length == 2) {
// //         double? lat = double.tryParse(coordinates[0]);
// //         double? lng = double.tryParse(coordinates[1]);

// //         if (lat != null && lng != null) {
// //           return LatLng(lat, lng);
// //         }
// //       }
// //     }
// //     return null;
// //   }

// //   void _showSavedLocation(LatLng savedPosition) {
// //     widget.googleMapController?.animateCamera(CameraUpdate.newLatLngZoom(
// //       savedPosition,
// //       18.0,
// //     ));

// //     setState(() {
// //       widget.marker.clear();
// //       widget.marker.add(
// //         Marker(
// //           markerId: const MarkerId('savedLocation'),
// //           position: savedPosition,
// //           infoWindow: const InfoWindow(title: 'Your Saved Location'),
// //         ),
// //       );

// //       // Also set as selected location
// //       selectedLatLng.value = savedPosition;
// //       widget.onLocationSelected(savedPosition);
// //     });
// //   }

// //   void _showCurrentLocation(Position position) {
// //     widget.googleMapController?.animateCamera(CameraUpdate.newLatLngZoom(
// //       LatLng(position.latitude, position.longitude),
// //       18.0,
// //     ));

// //     setState(() {
// //       widget.marker.clear();
// //       widget.marker.add(
// //         Marker(
// //           markerId: const MarkerId('currentLocation'),
// //           position: LatLng(position.latitude, position.longitude),
// //           infoWindow: const InfoWindow(title: 'Your Current Location'),
// //         ),
// //       );
// //     });

// //     if (selectedLatLng.value == null) {
// //       startTrackingLocation();
// //     }
// //   }

// //   Future<Position?> getUserCurrentLocation() async {
// //     permission = await Geolocator.requestPermission();
// //     if (permission == LocationPermission.denied) {
// //       permission = await Geolocator.requestPermission();
// //     } else if (permission == LocationPermission.deniedForever) {
// //       await Geolocator.openAppSettings();
// //     } else if (permission != LocationPermission.denied &&
// //         permission != LocationPermission.deniedForever) {
// //       position = await Geolocator.getCurrentPosition(
// //           desiredAccuracy: LocationAccuracy.high);
// //       return position;
// //     }
// //     return null;
// //   }

// // /*
// //   Future<Position?> getUserCurrentLocation() async {
// //     permission = await Geolocator.requestPermission();
// //     if (permission == LocationPermission.denied) {
// //       permission = await Geolocator.requestPermission();
// //     } else if (permission == LocationPermission.deniedForever) {
// //       await Geolocator.openAppSettings();
// //     } else if (permission != LocationPermission.denied &&
// //         permission != LocationPermission.deniedForever) {
// //       position = await Geolocator.getCurrentPosition(
// //           desiredAccuracy: LocationAccuracy.high);
// //       print('permission allowed');
// //       if (position != null) {
// //         List<Placemark> placemarks = await placemarkFromCoordinates(
// //             position!.latitude, position!.longitude);

// //         return await Geolocator.getCurrentPosition();
// //       }
// //     }
// //     return null;
// //     // await Geolocator.requestPermission().then((value) async {
// //     //   return await Geolocator.getCurrentPosition();
// //     // }).onError((error, stackTrace) async {
// //     //   logger(error, loggerType: LoggerType.error);
// //     //   await Geolocator.requestPermission();
// //     //   CustomSnackBar.showSnackBar(
// //     //       title: "Permission denied", color: Colors.red);
// //     //   return await Geolocator.getCurrentPosition();
// //     // });
// //     // return await Geolocator.getCurrentPosition();
// //   }

// //   */

// //   initialGeo() async {
// //     getUserCurrentLocation().then((value) async {
// //       // print("${value.latitude} ${value.longitude}");
// //       // // marker added for current users location
// //       // _marker.add(Marker(
// //       //   markerId: const MarkerId("2"),
// //       //   position: LatLng(value.latitude, value.longitude),
// //       //   infoWindow: const InfoWindow(
// //       //     title: 'My Current Location',
// //       //   ),
// //       // ));

// //       // specified current users location
// //       // CameraPosition cameraPosition = CameraPosition(
// //       //   target: LatLng(value.latitude, value.longitude),
// //       //   zoom: 14,
// //       // );
// //       await widget.completeGoogleMapController.future
// //           .then((val) => val.animateCamera(CameraUpdate.newLatLngZoom(
// //                 LatLng(value!.latitude, value.longitude),
// //                 18.0,
// //               )));
// //     });
// //   }

// //   void startTrackingLocation() {
// //     Geolocator.getPositionStream(
// //       locationSettings: const LocationSettings(
// //         accuracy: LocationAccuracy.high,
// //         distanceFilter: 10,
// //       ),
// //     ).listen((Position position) {
// //       widget.googleMapController?.animateCamera(CameraUpdate.newLatLng(
// //         LatLng(position.latitude, position.longitude),
// //       ));
// //       if (selectedLatLng.value == null) {
// //         setState(() {
// //           widget.marker.removeWhere(
// //               (marker) => marker.markerId.value == 'currentLocation');
// //           widget.marker.add(
// //             Marker(
// //               markerId: const MarkerId('currentLocation'),
// //               position: LatLng(position.latitude, position.longitude),
// //               infoWindow: const InfoWindow(title: 'Your Location'),
// //             ),
// //           );
// //         });
// //       }
// //     });
// //   }

// //   @override
// //   Widget build(BuildContext context) {
// //     return WillPopScope(
// //       onWillPop: () async {
// //         selectedLatLng.value = null;
// //         return true;
// //       },
// //       child: Scaffold(
// //         appBar: myAppbar(
// //           context,
// //           false,
// //           "Select Your Location",
// //           leading: GestureDetector(
// //             onTap: () {
// //               selectedLatLng.value = null;
// //               Get.back();
// //             },
// //             child: const Icon(Icons.arrow_back),
// //           ),
// //         ),
// //         body: Column(
// //           children: [
// //             // const SizedBox(height: 100),
// //             Expanded(
// //               // height: size.height * 0.795,
// //               // width: 500,
// //               // color: Colors.red,
// //               child: GoogleMap(
// //                 initialCameraPosition: widget.kGoogle,
// //                 markers: Set<Marker>.of(widget.marker),
// //                 mapType: MapType.normal,
// //                 myLocationEnabled: true,
// //                 // minMaxZoomPreference: MinMaxZoomPreference.unbounded,
// //                 compassEnabled: true,
// //                 onMapCreated: (GoogleMapController controller) {
// //                   widget.mapController.isMapLoading.value = false;
// //                   if (!widget.completeGoogleMapController.isCompleted) {
// //                     widget.completeGoogleMapController.complete(controller);
// //                   }
// //                   /////added /////////////////////
// //                   //  widget.googleMapController =
// //                   //  controller; // Ensure the googleMapController is set
// //                 },
// //                 onTap: (LatLng latLng) async {
// //                   List<Placemark> placemarks = await placemarkFromCoordinates(
// //                     latLng.latitude,
// //                     latLng.longitude,
// //                   );
// //                   if (placemarks.isNotEmpty) {
// //                     Placemark placemark = placemarks.first;

// //                     String address =
// //                         '${placemark.thoroughfare ?? ''} ${placemark.subThoroughfare ?? ''}, '
// //                         '${placemark.locality ?? ''}, ${placemark.subLocality ?? ''}, '
// //                         '${placemark.administrativeArea ?? ''}, ${placemark.subAdministrativeArea ?? ''} '
// //                         '${placemark.postalCode ?? ''}, ${placemark.country ?? ''}';

// //                     setState(() {
// //                       _workingAddressController.text = address;
// //                       selectedLatLng.value = latLng;

// //                       widget.marker.clear();
// //                       widget.marker.add(
// //                         Marker(
// //                           markerId: const MarkerId('Selected Location'),
// //                           position: selectedLatLng.value ??
// //                               const LatLng(27.707795, 85.343362),
// //                           infoWindow: InfoWindow(
// //                             title: selectedLatLng.value.toString(),
// //                           ),
// //                         ),
// //                       );
// //                     });
// //                     widget.onLocationSelected(latLng);
// //                   }
// //                 },
// //                 // onTap: (LatLng latLng) {
// //                 //   setState(
// //                 //     () {
// //                 //       _selectedLatLng = latLng;
// //                 //     },
// //                 //   );
// //                 // },
// //                 // initialCameraPosition: currentLocation != null
// //                 //     ? CameraPosition(
// //                 //         target: LatLng(
// //                 //           currentLocation!.latitude!,
// //                 //           currentLocation!.longitude!,
// //                 //         ),
// //                 //         zoom: 14,
// //                 //       )
// //                 //     : const CameraPosition(
// //                 //         target: LatLng(27.706969, 85.341963),
// //                 //         zoom: 14,
// //                 //       ),
// //                 // initialCameraPosition: CameraPosition(
// //                 //   target: _selectedLatLng,
// //                 //   zoom: 14,
// //                 // ),
// //                 // ignore: unnecessary_null_comparison
// //                 // markers: _selectedLatLng != null
// //                 //     ? {
// //                 //         Marker(
// //                 //             markerId: const MarkerId('Selected Location'),
// //                 //             position: _selectedLatLng,
// //                 //             infoWindow:
// //                 //                 InfoWindow(title: _selectedLatLng.toString())),
// //                 //       }
// //                 //     : {},
// //               ),
// //             ),
// //             Container(
// //               height: 50,
// //               width: double.infinity,
// //               color: Theme.of(context).primaryColor,
// //               child: Align(
// //                 alignment: Alignment.bottomRight,
// //                 child: Padding(
// //                   padding: const EdgeInsets.only(right: 8.0),
// //                   child: TextButton(
// //                     style: ButtonStyle(
// //                         shape: WidgetStatePropertyAll(
// //                           RoundedRectangleBorder(
// //                             borderRadius: BorderRadius.circular(6),
// //                           ),
// //                         ),
// //                         backgroundColor: const WidgetStatePropertyAll(
// //                             Color.fromARGB(230, 255, 255, 255))),
// //                     child: const Text(
// //                       'Ok',
// //                       style: TextStyle(
// //                         fontSize: 18,
// //                         fontWeight: FontWeight.w500,
// //                         color: Color.fromARGB(240, 0, 131, 143),
// //                       ),
// //                     ),
// //                     onPressed: () {
// //                       setState(() {
// //                         widget.onpressed = false;
// //                       });
// //                       Get.back();
// //                     },
// //                   ),
// //                 ),
// //               ),
// //             )
// //           ],
// //         ),
// //       ),
// //     );
// //   }
// // }

// // ignore_for_file: public_member_api_docs, sort_constructors_first
// import 'dart:async';

// import 'package:flutter/material.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:get/get.dart';

// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smartsewa/core/development/console.dart';

// import '../../../core/states.dart';
// import '../../widgets/my_appbar.dart';
// import 'package:geocoding/geocoding.dart';

// import 'map_controller.dart';

// typedef LocationCallback = void Function(LatLng location);

// class OpenMapScreen extends StatefulWidget {
//   GoogleMapController? googleMapController;
//   final mapController = Get.put(MapController());
//   Completer<GoogleMapController> completeGoogleMapController;

//   final LocationCallback onLocationSelected;

//   CameraPosition kGoogle;
//   final List<Marker> marker;
//   bool onpressed;

//   OpenMapScreen({
//     super.key,
//     this.googleMapController,
//     required this.completeGoogleMapController,
//     required this.kGoogle,
//     required this.onpressed,
//     required this.marker,
//     required this.onLocationSelected,
//   });

//   @override
//   State<OpenMapScreen> createState() => _OpenMapScreenState();
// }

// class _OpenMapScreenState extends State<OpenMapScreen> {
//   final TextEditingController _workingAddressController =
//       TextEditingController();

//   LocationPermission? permission;
//   Position? position;

//   @override
//   void initState() {
//     super.initState();

//     // First check for saved location in shared preferences
//     _checkSavedLocation().then((savedPosition) {
//       if (savedPosition != null) {
//         // Display the saved location
//         _showSavedLocation(savedPosition);
//       } else {
//         // If no saved location, get current location
//         getUserCurrentLocation().then((position) {
//           if (position != null) {
//             _showCurrentLocation(position);
//           }
//         });
//       }
//     });
//   }

//   Future<LatLng?> _checkSavedLocation() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     String? savedLatLng = prefs.getString("latitude");

//     if (savedLatLng != null && savedLatLng.isNotEmpty) {
//       List<String> coordinates = savedLatLng.split(',');
//       if (coordinates.length == 2) {
//         double? lat = double.tryParse(coordinates[0]);
//         double? lng = double.tryParse(coordinates[1]);

//         if (lat != null && lng != null) {
//           return LatLng(lat, lng);
//         }
//       }
//     }
//     return null;
//   }

//   void _showSavedLocation(LatLng savedPosition) {
//     widget.googleMapController?.animateCamera(CameraUpdate.newLatLngZoom(
//       savedPosition,
//       18.0,
//     ));

//     setState(() {
//       widget.marker.clear();
//       widget.marker.add(
//         Marker(
//           markerId: const MarkerId('savedLocation'),
//           position: savedPosition,
//           infoWindow: const InfoWindow(title: 'Your Saved Location'),
//         ),
//       );

//       // Also set as selected location
//       selectedLatLng.value = savedPosition;
//       widget.onLocationSelected(savedPosition);
//     });
//   }

//   void _showCurrentLocation(Position position) {
//     widget.googleMapController?.animateCamera(CameraUpdate.newLatLngZoom(
//       LatLng(position.latitude, position.longitude),
//       18.0,
//     ));

//     setState(() {
//       widget.marker.clear();
//       widget.marker.add(
//         Marker(
//           markerId: const MarkerId('currentLocation'),
//           position: LatLng(position.latitude, position.longitude),
//           infoWindow: const InfoWindow(title: 'Your Current Location'),
//         ),
//       );
//     });

//     if (selectedLatLng.value == null) {
//       startTrackingLocation();
//     }
//   }

//   Future<Position?> getUserCurrentLocation() async {
//     permission = await Geolocator.requestPermission();
//     if (permission == LocationPermission.denied) {
//       permission = await Geolocator.requestPermission();
//     } else if (permission == LocationPermission.deniedForever) {
//       await Geolocator.openAppSettings();
//     } else if (permission != LocationPermission.denied &&
//         permission != LocationPermission.deniedForever) {
//       position = await Geolocator.getCurrentPosition(
//           desiredAccuracy: LocationAccuracy.high);
//       return position;
//     }
//     return null;
//   }

//   initialGeo() async {
//     getUserCurrentLocation().then((value) async {
//       await widget.completeGoogleMapController.future
//           .then((val) => val.animateCamera(CameraUpdate.newLatLngZoom(
//                 LatLng(value!.latitude, value.longitude),
//                 18.0,
//               )));
//     });
//   }

//   void startTrackingLocation() {
//     Geolocator.getPositionStream(
//       locationSettings: const LocationSettings(
//         accuracy: LocationAccuracy.high,
//         distanceFilter: 10,
//       ),
//     ).listen((Position position) {
//       widget.googleMapController?.animateCamera(CameraUpdate.newLatLng(
//         LatLng(position.latitude, position.longitude),
//       ));
//       if (selectedLatLng.value == null) {
//         setState(() {
//           widget.marker.removeWhere(
//               (marker) => marker.markerId.value == 'currentLocation');
//           widget.marker.add(
//             Marker(
//               markerId: const MarkerId('currentLocation'),
//               position: LatLng(position.latitude, position.longitude),
//               infoWindow: const InfoWindow(title: 'Your Location'),
//             ),
//           );
//         });
//       }
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return WillPopScope(
//       onWillPop: () async {
//         selectedLatLng.value = null;
//         return true;
//       },
//       child: Scaffold(
//         appBar: myAppbar(
//           context,
//           false,
//           "Select Your Location",
//           leading: GestureDetector(
//             onTap: () {
//               selectedLatLng.value = null;
//               Get.back();
//             },
//             child: const Icon(Icons.arrow_back),
//           ),
//         ),
//         body: Column(
//           children: [
//             Expanded(
//               child: GoogleMap(
//                 initialCameraPosition: widget.kGoogle,
//                 markers: Set<Marker>.of(widget.marker),
//                 mapType: MapType.normal,
//                 myLocationEnabled: true,
//                 compassEnabled: true,
//                 onMapCreated: (GoogleMapController controller) {
//                   widget.mapController.isMapLoading.value = false;
//                   if (!widget.completeGoogleMapController.isCompleted) {
//                     widget.completeGoogleMapController.complete(controller);
//                   }
//                 },
//                 onTap: (LatLng latLng) async {
//                   List<Placemark> placemarks = await placemarkFromCoordinates(
//                     latLng.latitude,
//                     latLng.longitude,
//                   );
//                   if (placemarks.isNotEmpty) {
//                     Placemark placemark = placemarks.first;

//                     String address =
//                         '${placemark.thoroughfare ?? ''} ${placemark.subThoroughfare ?? ''}, '
//                         '${placemark.locality ?? ''}, ${placemark.subLocality ?? ''}, '
//                         '${placemark.administrativeArea ?? ''}, ${placemark.subAdministrativeArea ?? ''} '
//                         '${placemark.postalCode ?? ''}, ${placemark.country ?? ''}';

//                     setState(() {
//                       _workingAddressController.text = address;
//                       selectedLatLng.value = latLng;

//                       widget.marker.clear();
//                       widget.marker.add(
//                         Marker(
//                           markerId: const MarkerId('Selected Location'),
//                           position: selectedLatLng.value ??
//                               const LatLng(27.707795, 85.343362),
//                           infoWindow: InfoWindow(
//                             title: selectedLatLng.value.toString(),
//                           ),
//                         ),
//                       );
//                     });
//                     widget.onLocationSelected(latLng);
//                   }
//                 },
//               ),
//             ),
//             // Fixed bottom container with better touch handling
//             SafeArea(
//               child: Container(
//                 width: double.infinity,
//                 padding: const EdgeInsets.symmetric(
//                     horizontal: 16.0, vertical: 12.0),
//                 decoration: BoxDecoration(
//                   color: Theme.of(context).primaryColor,
//                   boxShadow: [
//                     BoxShadow(
//                       color: Colors.black.withOpacity(0.1),
//                       blurRadius: 4,
//                       offset: const Offset(0, -2),
//                     ),
//                   ],
//                 ),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.end,
//                   children: [
//                     Material(
//                       color: Colors.transparent,
//                       child: InkWell(
//                         borderRadius: BorderRadius.circular(8),
//                         onTap: () {
//                           setState(() {
//                             widget.onpressed = false;
//                           });
//                           Get.back();
//                         },
//                         child: Container(
//                           padding: const EdgeInsets.symmetric(
//                             horizontal: 24.0,
//                             vertical: 12.0,
//                           ),
//                           decoration: BoxDecoration(
//                             color: const Color.fromARGB(230, 255, 255, 255),
//                             borderRadius: BorderRadius.circular(8),
//                           ),
//                           child: const Text(
//                             'OK',
//                             style: TextStyle(
//                               fontSize: 16,
//                               fontWeight: FontWeight.w600,
//                               color: Color.fromARGB(240, 0, 131, 143),
//                             ),
//                           ),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';

import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartsewa/core/development/console.dart';

import '../../../core/states.dart';
import '../../widgets/my_appbar.dart';
import 'package:geocoding/geocoding.dart';

import 'map_controller.dart';

typedef LocationCallback = void Function(LatLng location);

class OpenMapScreen extends StatefulWidget {
  GoogleMapController? googleMapController;
  final mapController = Get.put(MapController());
  Completer<GoogleMapController> completeGoogleMapController;

  final LocationCallback onLocationSelected;

  CameraPosition kGoogle;
  final List<Marker> marker;
  bool onpressed;

  OpenMapScreen({
    super.key,
    this.googleMapController,
    required this.completeGoogleMapController,
    required this.kGoogle,
    required this.onpressed,
    required this.marker,
    required this.onLocationSelected,
  });

  @override
  State<OpenMapScreen> createState() => _OpenMapScreenState();
}

class _OpenMapScreenState extends State<OpenMapScreen> {
  final TextEditingController _workingAddressController =
      TextEditingController();

  LocationPermission? permission;
  Position? position;

  @override
  void initState() {
    super.initState();

    // First check for previously selected location in shared preferences
    _checkPreviouslySelectedLocation().then((previousPosition) {
      if (previousPosition != null) {
        // Display the previously selected location
        _showPreviouslySelectedLocation(previousPosition);
      } else {
        // If no previously selected location, get current location
        getUserCurrentLocation().then((position) {
          if (position != null) {
            _showCurrentLocation(position);
          }
        });
      }
    });
  }

  // Check for previously selected location
  Future<LatLng?> _checkPreviouslySelectedLocation() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? previousLocation = prefs.getString('working_address_location');

      if (previousLocation != null && previousLocation.isNotEmpty) {
        List<String> coordinates = previousLocation.split(',');
        if (coordinates.length == 2) {
          double? lat = double.tryParse(coordinates[0].trim());
          double? lng = double.tryParse(coordinates[1].trim());

          if (lat != null && lng != null) {
            return LatLng(lat, lng);
          }
        }
      }
    } catch (e) {
      consolelog('Error loading previously selected location: $e');
    }
    return null;
  }

  // Show previously selected location on map
  void _showPreviouslySelectedLocation(LatLng previousPosition) {
    widget.googleMapController?.animateCamera(CameraUpdate.newLatLngZoom(
      previousPosition,
      18.0,
    ));

    setState(() {
      widget.marker.clear();
      widget.marker.add(
        Marker(
          markerId: const MarkerId('previousLocation'),
          position: previousPosition,
          infoWindow: const InfoWindow(title: 'Previously Selected Location'),
        ),
      );

      // Set as selected location
      selectedLatLng.value = previousPosition;
    });
  }

  void _showCurrentLocation(Position position) {
    widget.googleMapController?.animateCamera(CameraUpdate.newLatLngZoom(
      LatLng(position.latitude, position.longitude),
      18.0,
    ));

    setState(() {
      widget.marker.clear();
      widget.marker.add(
        Marker(
          markerId: const MarkerId('currentLocation'),
          position: LatLng(position.latitude, position.longitude),
          infoWindow: const InfoWindow(title: 'Your Current Location'),
        ),
      );
    });

    if (selectedLatLng.value == null) {
      startTrackingLocation();
    }
  }

  Future<Position?> getUserCurrentLocation() async {
    permission = await Geolocator.requestPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
    } else if (permission == LocationPermission.deniedForever) {
      await Geolocator.openAppSettings();
    } else if (permission != LocationPermission.denied &&
        permission != LocationPermission.deniedForever) {
      position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);
      return position;
    }
    return null;
  }

  initialGeo() async {
    getUserCurrentLocation().then((value) async {
      await widget.completeGoogleMapController.future
          .then((val) => val.animateCamera(CameraUpdate.newLatLngZoom(
                LatLng(value!.latitude, value.longitude),
                18.0,
              )));
    });
  }

  void startTrackingLocation() {
    Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10,
      ),
    ).listen((Position position) {
      widget.googleMapController?.animateCamera(CameraUpdate.newLatLng(
        LatLng(position.latitude, position.longitude),
      ));
      if (selectedLatLng.value == null) {
        setState(() {
          widget.marker.removeWhere(
              (marker) => marker.markerId.value == 'currentLocation');
          widget.marker.add(
            Marker(
              markerId: const MarkerId('currentLocation'),
              position: LatLng(position.latitude, position.longitude),
              infoWindow: const InfoWindow(title: 'Your Location'),
            ),
          );
        });
      }
    });
  }

  // Save selected location for next visit
  Future<void> _saveSelectedLocation(LatLng location) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String locationString = '${location.latitude},${location.longitude}';
      await prefs.setString('working_address_location', locationString);
    } catch (e) {
      consolelog('Error saving selected location: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Don't clear selectedLatLng when going back
        return true;
      },
      child: Scaffold(
        appBar: myAppbar(
          context,
          false,
          "Select Your Location",
          leading: GestureDetector(
            onTap: () {
              // Don't clear selectedLatLng when going back
              Get.back();
            },
            child: const Icon(Icons.arrow_back),
          ),
        ),
        body: Column(
          children: [
            Expanded(
              child: GoogleMap(
                initialCameraPosition: widget.kGoogle,
                markers: Set<Marker>.of(widget.marker),
                mapType: MapType.normal,
                myLocationEnabled: true,
                compassEnabled: true,
                onMapCreated: (GoogleMapController controller) {
                  widget.mapController.isMapLoading.value = false;
                  widget.googleMapController = controller;
                  if (!widget.completeGoogleMapController.isCompleted) {
                    widget.completeGoogleMapController.complete(controller);
                  }
                },
                onTap: (LatLng latLng) async {
                  List<Placemark> placemarks = await placemarkFromCoordinates(
                    latLng.latitude,
                    latLng.longitude,
                  );
                  if (placemarks.isNotEmpty) {
                    Placemark placemark = placemarks.first;

                    String address =
                        '${placemark.thoroughfare ?? ''} ${placemark.subThoroughfare ?? ''}, '
                        '${placemark.locality ?? ''}, ${placemark.subLocality ?? ''}, '
                        '${placemark.administrativeArea ?? ''}, ${placemark.subAdministrativeArea ?? ''} '
                        '${placemark.postalCode ?? ''}, ${placemark.country ?? ''}';

                    setState(() {
                      _workingAddressController.text = address;
                      selectedLatLng.value = latLng;

                      widget.marker.clear();
                      widget.marker.add(
                        Marker(
                          markerId: const MarkerId('Selected Location'),
                          position: latLng,
                          infoWindow: InfoWindow(
                            title: 'Selected Location',
                            snippet:
                                '${latLng.latitude.toStringAsFixed(6)}, ${latLng.longitude.toStringAsFixed(6)}',
                          ),
                        ),
                      );
                    });

                    // Save the selected location immediately for next visit
                    _saveSelectedLocation(latLng);

                    // Call the callback to update the parent widget
                    widget.onLocationSelected(latLng);
                  }
                },
              ),
            ),
            // Fixed bottom container with better touch handling
            SafeArea(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                    horizontal: 16.0, vertical: 12.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () {
                          setState(() {
                            widget.onpressed = false;
                          });
                          Get.back();
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24.0,
                            vertical: 12.0,
                          ),
                          decoration: BoxDecoration(
                            color: const Color.fromARGB(230, 255, 255, 255),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Text(
                            'OK',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Color.fromARGB(240, 0, 131, 143),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
