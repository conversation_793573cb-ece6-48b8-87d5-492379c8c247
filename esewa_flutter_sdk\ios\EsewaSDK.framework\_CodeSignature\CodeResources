<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>.DS_Store</key>
		<data>
		JPB9eS//lP4T59i1czcTXu1zw30=
		</data>
		<key>Assets.car</key>
		<data>
		wcaviRRNvCQPOC6VyqtBnqFapCI=
		</data>
		<key>Headers/Encryptor.h</key>
		<data>
		RCJkF3wK0oiuNiTu96dA2ztrd8M=
		</data>
		<key>Headers/EsewaSDK-Swift.h</key>
		<data>
		xXRTKWdnety1A6fbSiUqFaOwhIU=
		</data>
		<key>Headers/EsewaSDK.h</key>
		<data>
		smWkTfDpDRKkUXo1W+AyAYq8SWs=
		</data>
		<key>Headers/NSData+Base64.h</key>
		<data>
		q3w/tkhv0JkcBZvjUhupC0nKHMo=
		</data>
		<key>Info.plist</key>
		<data>
		cgdAbu/TiNyAqGRktG95Ap0m9DI=
		</data>
		<key>Modules/EsewaSDK.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		vPPoHeT5xzKg00sl+Fa2ASpo8KI=
		</data>
		<key>Modules/EsewaSDK.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		MOuvWgd/f93MvHSC0G2Fb+/NDio=
		</data>
		<key>Modules/EsewaSDK.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		N1K33n6QnL/08lCZWKg6THWmeZ0=
		</data>
		<key>Modules/EsewaSDK.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		SttnfIfzr9BOPZq5d+RQTXDMV/g=
		</data>
		<key>Modules/EsewaSDK.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		YkAjf+pRNx54t62rYFXbJtv38sQ=
		</data>
		<key>Modules/EsewaSDK.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		SttnfIfzr9BOPZq5d+RQTXDMV/g=
		</data>
		<key>Modules/EsewaSDK.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		B14sMcwAxZPIKcNz35BDKOim3i8=
		</data>
		<key>Modules/EsewaSDK.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		N1K33n6QnL/08lCZWKg6THWmeZ0=
		</data>
		<key>Modules/EsewaSDK.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		O2WHSjSTsFFwHi7SH63FOanJlTI=
		</data>
		<key>Modules/EsewaSDK.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		JiMzrcKc/m6SdzdgfCTPF6W7Z1E=
		</data>
		<key>Modules/EsewaSDK.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		O2WHSjSTsFFwHi7SH63FOanJlTI=
		</data>
		<key>Modules/EsewaSDK.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		Cpd6uy1tJT0rK0wyWoiqa09ifMU=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		Y6VeyXkX8hE+KGzMk0tM7T1XS8w=
		</data>
		<key>OFL.txt</key>
		<data>
		d2AiIA7VW+t7oC7pca0Y1Hsf+ok=
		</data>
		<key>SourceSansPro-Black.ttf</key>
		<data>
		Assihp+0O4cRfCeCtTCGfVpi250=
		</data>
		<key>SourceSansPro-BlackItalic.ttf</key>
		<data>
		ZKTQy2pTxeZpSZjuFZRR0THXCNE=
		</data>
		<key>SourceSansPro-Bold.ttf</key>
		<data>
		ESOLD6scPYhK7DyNjgTfxM50/5E=
		</data>
		<key>SourceSansPro-BoldItalic.ttf</key>
		<data>
		Y6K3901jywPanf4Q7OwrvP7m4y8=
		</data>
		<key>SourceSansPro-ExtraLight.ttf</key>
		<data>
		dwf3vvpvpLI3UEs37UfygdPw4So=
		</data>
		<key>SourceSansPro-ExtraLightItalic.ttf</key>
		<data>
		wLLDGmFAbZ26MclD8iiMxaXZ4WM=
		</data>
		<key>SourceSansPro-Italic.ttf</key>
		<data>
		4sgCsB1UN/OQF3OyGDxGHvpJ6qQ=
		</data>
		<key>SourceSansPro-Light.ttf</key>
		<data>
		YVA1g5W6DFgOBIQaekY1ugFNu8E=
		</data>
		<key>SourceSansPro-LightItalic.ttf</key>
		<data>
		Fqr/PzVtSEvRGsNtwBPIXKspKvA=
		</data>
		<key>SourceSansPro-Regular.ttf</key>
		<data>
		+k4wOWDNi/N6IXHEvGGGaE8tQXg=
		</data>
		<key>SourceSansPro-SemiBold.ttf</key>
		<data>
		nhDjfHXhP4ljgvtf8Ede3EVPRYk=
		</data>
		<key>SourceSansPro-SemiBoldItalic.ttf</key>
		<data>
		qEPYPZVgGTOt4/f1+ZgE53OMCjs=
		</data>
		<key>esewa.storyboardc/1eU-5n-j3S-view-eMm-4d-WwG.nib/objects-11.0+.nib</key>
		<data>
		CUpwgdrTDRnFTKpFKQzjd6XJ0Uw=
		</data>
		<key>esewa.storyboardc/1eU-5n-j3S-view-eMm-4d-WwG.nib/runtime.nib</key>
		<data>
		stOKMrZM+ya6rjgfAz3njq70cEY=
		</data>
		<key>esewa.storyboardc/Info.plist</key>
		<data>
		LWs0nAw9yNRB1McVHv+S4IRc0ZQ=
		</data>
		<key>esewa.storyboardc/KOU-XQ-l1G-view-uqb-M3-Q6h.nib/objects-11.0+.nib</key>
		<data>
		yJbLme6587/Glip33tNySkfJN6g=
		</data>
		<key>esewa.storyboardc/KOU-XQ-l1G-view-uqb-M3-Q6h.nib/runtime.nib</key>
		<data>
		4M7Rg5T0zmqQAbQ/oKoYzSnnb9c=
		</data>
		<key>esewa.storyboardc/NewSdkPaymentViewController.nib/objects-11.0+.nib</key>
		<data>
		Pw3CBLzYI8YLVDVY7fNF0wNCY2Y=
		</data>
		<key>esewa.storyboardc/NewSdkPaymentViewController.nib/runtime.nib</key>
		<data>
		Pw3CBLzYI8YLVDVY7fNF0wNCY2Y=
		</data>
		<key>esewa.storyboardc/RequestViewController.nib/objects-11.0+.nib</key>
		<data>
		hqVvJ+VUxmu4uv0k38kBZQltN1s=
		</data>
		<key>esewa.storyboardc/RequestViewController.nib/runtime.nib</key>
		<data>
		hqVvJ+VUxmu4uv0k38kBZQltN1s=
		</data>
		<key>esewa.storyboardc/Rlo-5H-b9b-view-JyD-lH-hR5.nib/objects-11.0+.nib</key>
		<data>
		guP4LUUkfCmaxnXRvOTnKVOCWc8=
		</data>
		<key>esewa.storyboardc/Rlo-5H-b9b-view-JyD-lH-hR5.nib/runtime.nib</key>
		<data>
		n31BzVAsZ53OBppdLRyxY9fozJo=
		</data>
		<key>esewa.storyboardc/SDKSuccessfulPaymentViewController.nib/objects-11.0+.nib</key>
		<data>
		Wzqdt6aElEBQIt3CVLAJqDW9/XA=
		</data>
		<key>esewa.storyboardc/SDKSuccessfulPaymentViewController.nib/runtime.nib</key>
		<data>
		Wzqdt6aElEBQIt3CVLAJqDW9/XA=
		</data>
		<key>esewa.storyboardc/SdkLoginViewController.nib/objects-11.0+.nib</key>
		<data>
		WUw3QckRX75v5nC7jDT4Lcy1MsY=
		</data>
		<key>esewa.storyboardc/SdkLoginViewController.nib/runtime.nib</key>
		<data>
		WUw3QckRX75v5nC7jDT4Lcy1MsY=
		</data>
		<key>esewa.storyboardc/SdkPaymentViewController.nib/objects-11.0+.nib</key>
		<data>
		kLoFSoLs6flGKoLRn3xXwjS8DSQ=
		</data>
		<key>esewa.storyboardc/SdkPaymentViewController.nib/runtime.nib</key>
		<data>
		kLoFSoLs6flGKoLRn3xXwjS8DSQ=
		</data>
		<key>esewa.storyboardc/vqW-EK-dzU-view-VU3-iX-3i7.nib/objects-11.0+.nib</key>
		<data>
		PwOCgqS3RrDLvjnQTRihU460mQY=
		</data>
		<key>esewa.storyboardc/vqW-EK-dzU-view-VU3-iX-3i7.nib/runtime.nib</key>
		<data>
		ZdhZsjEfRsZJcXOYCdIrHz28l/o=
		</data>
		<key>esewa.storyboardc/xhf-ct-UR2-view-Aav-pU-0Lo.nib/objects-11.0+.nib</key>
		<data>
		hVNeemgK3q0dt7nyJxGdNITTH4s=
		</data>
		<key>esewa.storyboardc/xhf-ct-UR2-view-Aav-pU-0Lo.nib/runtime.nib</key>
		<data>
		kn5/C6QC+sr3cJYqPXUEwq5gC/E=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			wcaviRRNvCQPOC6VyqtBnqFapCI=
			</data>
			<key>hash2</key>
			<data>
			Kt7KVXqPzi1Hj6+UznxlHI3qceloRGtNd+5dA1re0xE=
			</data>
		</dict>
		<key>Headers/Encryptor.h</key>
		<dict>
			<key>hash</key>
			<data>
			RCJkF3wK0oiuNiTu96dA2ztrd8M=
			</data>
			<key>hash2</key>
			<data>
			5ymvGMUd1Y+LIpoz6SRmqMKXpJSk3x20X6FwYH2EXK4=
			</data>
		</dict>
		<key>Headers/EsewaSDK-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			xXRTKWdnety1A6fbSiUqFaOwhIU=
			</data>
			<key>hash2</key>
			<data>
			UNjiQHKmgnkOcmvmkZG9wnoGPW6yDFezxSHViv0P5yI=
			</data>
		</dict>
		<key>Headers/EsewaSDK.h</key>
		<dict>
			<key>hash</key>
			<data>
			smWkTfDpDRKkUXo1W+AyAYq8SWs=
			</data>
			<key>hash2</key>
			<data>
			2dUIFFjfwgybZNTCr701RT2/ySWymbgUOvui5RjIXTY=
			</data>
		</dict>
		<key>Headers/NSData+Base64.h</key>
		<dict>
			<key>hash</key>
			<data>
			q3w/tkhv0JkcBZvjUhupC0nKHMo=
			</data>
			<key>hash2</key>
			<data>
			V9lqQz8BS6HfxzaoI51OHfQHhAGz1nJnDNQKsX1uzsE=
			</data>
		</dict>
		<key>Modules/EsewaSDK.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			vPPoHeT5xzKg00sl+Fa2ASpo8KI=
			</data>
			<key>hash2</key>
			<data>
			O9PTNiTv0ywmRDlxySHAMC19MD3YILHmK1KOWYmoFjU=
			</data>
		</dict>
		<key>Modules/EsewaSDK.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			MOuvWgd/f93MvHSC0G2Fb+/NDio=
			</data>
			<key>hash2</key>
			<data>
			N+ydl4eJvLyPQISOILAj5vvXP+42/j06vGEf7p/lvLg=
			</data>
		</dict>
		<key>Modules/EsewaSDK.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			N1K33n6QnL/08lCZWKg6THWmeZ0=
			</data>
			<key>hash2</key>
			<data>
			JQ9ExCluktriyMUBuX8FrjNfkHSm6Pn3fJU9/xAXCfs=
			</data>
		</dict>
		<key>Modules/EsewaSDK.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			SttnfIfzr9BOPZq5d+RQTXDMV/g=
			</data>
			<key>hash2</key>
			<data>
			jz87vZPM4uhERxUlz8i5m4InUyS6HAbenJQzUS48Ckc=
			</data>
		</dict>
		<key>Modules/EsewaSDK.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			YkAjf+pRNx54t62rYFXbJtv38sQ=
			</data>
			<key>hash2</key>
			<data>
			43UWa7ZZCYiS88fux0y9kjV6+Vot0k2vHLSsdvfIeV8=
			</data>
		</dict>
		<key>Modules/EsewaSDK.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			SttnfIfzr9BOPZq5d+RQTXDMV/g=
			</data>
			<key>hash2</key>
			<data>
			jz87vZPM4uhERxUlz8i5m4InUyS6HAbenJQzUS48Ckc=
			</data>
		</dict>
		<key>Modules/EsewaSDK.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			B14sMcwAxZPIKcNz35BDKOim3i8=
			</data>
			<key>hash2</key>
			<data>
			5l7yt/viPIFwJtrA28h+fQffdA+pBeoNbTfEGvl4Uds=
			</data>
		</dict>
		<key>Modules/EsewaSDK.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			N1K33n6QnL/08lCZWKg6THWmeZ0=
			</data>
			<key>hash2</key>
			<data>
			JQ9ExCluktriyMUBuX8FrjNfkHSm6Pn3fJU9/xAXCfs=
			</data>
		</dict>
		<key>Modules/EsewaSDK.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			O2WHSjSTsFFwHi7SH63FOanJlTI=
			</data>
			<key>hash2</key>
			<data>
			5NODXuoWxyfwBlpCWpeooxgiO84zCWLNznZHU0Iy4n8=
			</data>
		</dict>
		<key>Modules/EsewaSDK.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			JiMzrcKc/m6SdzdgfCTPF6W7Z1E=
			</data>
			<key>hash2</key>
			<data>
			Wwy/UOjwaK1qOlEvTbRI3WWB0TL+bILqtZVn2dY62ZQ=
			</data>
		</dict>
		<key>Modules/EsewaSDK.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			O2WHSjSTsFFwHi7SH63FOanJlTI=
			</data>
			<key>hash2</key>
			<data>
			5NODXuoWxyfwBlpCWpeooxgiO84zCWLNznZHU0Iy4n8=
			</data>
		</dict>
		<key>Modules/EsewaSDK.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			Cpd6uy1tJT0rK0wyWoiqa09ifMU=
			</data>
			<key>hash2</key>
			<data>
			2JQYu8yHJIPR/bB6215kVi8T06/NEeiDQe+cCH3ETaw=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			Y6VeyXkX8hE+KGzMk0tM7T1XS8w=
			</data>
			<key>hash2</key>
			<data>
			kkS3tjDO5A6Tcn1xDZIXkyhh+6TSXYwQX/jEkcZ2ZZU=
			</data>
		</dict>
		<key>OFL.txt</key>
		<dict>
			<key>hash</key>
			<data>
			d2AiIA7VW+t7oC7pca0Y1Hsf+ok=
			</data>
			<key>hash2</key>
			<data>
			rsy7OohhG6k5OnzcG8WC1CRXibFg5HgBxMesDC6RjM4=
			</data>
		</dict>
		<key>SourceSansPro-Black.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			Assihp+0O4cRfCeCtTCGfVpi250=
			</data>
			<key>hash2</key>
			<data>
			odMUOD0K6JnhPesoeIMN2ruh/evXHUqQO7nOnH9bqes=
			</data>
		</dict>
		<key>SourceSansPro-BlackItalic.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			ZKTQy2pTxeZpSZjuFZRR0THXCNE=
			</data>
			<key>hash2</key>
			<data>
			SEQz/nFzUzIfEnnjZWQI0j5SL0mxs4PYjGmKlmRd5Fw=
			</data>
		</dict>
		<key>SourceSansPro-Bold.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			ESOLD6scPYhK7DyNjgTfxM50/5E=
			</data>
			<key>hash2</key>
			<data>
			nLq0cnb8BMZax4CY6aIGnFXibyFwGykJJzTOToMPgPs=
			</data>
		</dict>
		<key>SourceSansPro-BoldItalic.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			Y6K3901jywPanf4Q7OwrvP7m4y8=
			</data>
			<key>hash2</key>
			<data>
			jV3S156htbpTxxEBbl27C2WflKRqP2qj9r/QTz5v//Y=
			</data>
		</dict>
		<key>SourceSansPro-ExtraLight.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			dwf3vvpvpLI3UEs37UfygdPw4So=
			</data>
			<key>hash2</key>
			<data>
			GF/13Hl91J3fGxwYPTUmgvsNpm9Mm7ZDD3CPXBcHjxY=
			</data>
		</dict>
		<key>SourceSansPro-ExtraLightItalic.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			wLLDGmFAbZ26MclD8iiMxaXZ4WM=
			</data>
			<key>hash2</key>
			<data>
			t8NXCa2t0zr84Shh8liENaedwTiyJvc6xNS74Nh6GRQ=
			</data>
		</dict>
		<key>SourceSansPro-Italic.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			4sgCsB1UN/OQF3OyGDxGHvpJ6qQ=
			</data>
			<key>hash2</key>
			<data>
			G3i8HIEENI4T/MBDMxkm+BjOKQVWNQsakKc1/gOMc9Q=
			</data>
		</dict>
		<key>SourceSansPro-Light.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			YVA1g5W6DFgOBIQaekY1ugFNu8E=
			</data>
			<key>hash2</key>
			<data>
			PrOthS2YwTnHgGSARSTQ0kcrPvuHLcF2shf7V65qb3g=
			</data>
		</dict>
		<key>SourceSansPro-LightItalic.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			Fqr/PzVtSEvRGsNtwBPIXKspKvA=
			</data>
			<key>hash2</key>
			<data>
			PYRWKbD5wSK6TKOY52at5RkGLZYj5yQEYTph2slWW3w=
			</data>
		</dict>
		<key>SourceSansPro-Regular.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			+k4wOWDNi/N6IXHEvGGGaE8tQXg=
			</data>
			<key>hash2</key>
			<data>
			yYaN5h/yurC1o6bQHEt28plFnwjGri8sA4O0+fa+2/M=
			</data>
		</dict>
		<key>SourceSansPro-SemiBold.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			nhDjfHXhP4ljgvtf8Ede3EVPRYk=
			</data>
			<key>hash2</key>
			<data>
			O6XDgqfuaogxvfkBkq3c6r5ttCeKZ55n/n6cAia3Kc8=
			</data>
		</dict>
		<key>SourceSansPro-SemiBoldItalic.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			qEPYPZVgGTOt4/f1+ZgE53OMCjs=
			</data>
			<key>hash2</key>
			<data>
			rVHjx4gGT8/SukZi31cy3ztXeIzyDHDB8+MTpt3sf5s=
			</data>
		</dict>
		<key>esewa.storyboardc/1eU-5n-j3S-view-eMm-4d-WwG.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			CUpwgdrTDRnFTKpFKQzjd6XJ0Uw=
			</data>
			<key>hash2</key>
			<data>
			m2cNnIdJXqU4fyhZFa5yO+4jnQztJtjZdrzArfk9Jr0=
			</data>
		</dict>
		<key>esewa.storyboardc/1eU-5n-j3S-view-eMm-4d-WwG.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			stOKMrZM+ya6rjgfAz3njq70cEY=
			</data>
			<key>hash2</key>
			<data>
			nn+MhwJSUtkJ+tKWuMjf/MU9O4nnmnGmIw8Je85yPS4=
			</data>
		</dict>
		<key>esewa.storyboardc/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			LWs0nAw9yNRB1McVHv+S4IRc0ZQ=
			</data>
			<key>hash2</key>
			<data>
			Rz3cwDNy/4K+yuU2UyMpWT4DPBxUFUUPqcDMf9aQzcw=
			</data>
		</dict>
		<key>esewa.storyboardc/KOU-XQ-l1G-view-uqb-M3-Q6h.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			yJbLme6587/Glip33tNySkfJN6g=
			</data>
			<key>hash2</key>
			<data>
			Ae2rtgpwBpt0EP5d5vdQhGqmYqB8uEKycjAFLw0jMRY=
			</data>
		</dict>
		<key>esewa.storyboardc/KOU-XQ-l1G-view-uqb-M3-Q6h.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			4M7Rg5T0zmqQAbQ/oKoYzSnnb9c=
			</data>
			<key>hash2</key>
			<data>
			MlwUFEe0trVtkvL27x4QWrR56IHvE9roGuRovO49Xck=
			</data>
		</dict>
		<key>esewa.storyboardc/NewSdkPaymentViewController.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Pw3CBLzYI8YLVDVY7fNF0wNCY2Y=
			</data>
			<key>hash2</key>
			<data>
			4GifdL/qdF7RUB/89SKRPGip9Z7Z0452fe3uZo/2PYE=
			</data>
		</dict>
		<key>esewa.storyboardc/NewSdkPaymentViewController.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Pw3CBLzYI8YLVDVY7fNF0wNCY2Y=
			</data>
			<key>hash2</key>
			<data>
			4GifdL/qdF7RUB/89SKRPGip9Z7Z0452fe3uZo/2PYE=
			</data>
		</dict>
		<key>esewa.storyboardc/RequestViewController.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			hqVvJ+VUxmu4uv0k38kBZQltN1s=
			</data>
			<key>hash2</key>
			<data>
			1SBpbCffP6FtNH0C0PXa/MZQ4CwoT/7zmPHxyK7Qt8Y=
			</data>
		</dict>
		<key>esewa.storyboardc/RequestViewController.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			hqVvJ+VUxmu4uv0k38kBZQltN1s=
			</data>
			<key>hash2</key>
			<data>
			1SBpbCffP6FtNH0C0PXa/MZQ4CwoT/7zmPHxyK7Qt8Y=
			</data>
		</dict>
		<key>esewa.storyboardc/Rlo-5H-b9b-view-JyD-lH-hR5.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			guP4LUUkfCmaxnXRvOTnKVOCWc8=
			</data>
			<key>hash2</key>
			<data>
			uxjijyxJJci+BNuEyXJ5t10wX83P06wlc6kGe0rGXwo=
			</data>
		</dict>
		<key>esewa.storyboardc/Rlo-5H-b9b-view-JyD-lH-hR5.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			n31BzVAsZ53OBppdLRyxY9fozJo=
			</data>
			<key>hash2</key>
			<data>
			hHShfEcwQRy4sqaoMCrBj/C/l5/Y1pNnzd++vhI74+Q=
			</data>
		</dict>
		<key>esewa.storyboardc/SDKSuccessfulPaymentViewController.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Wzqdt6aElEBQIt3CVLAJqDW9/XA=
			</data>
			<key>hash2</key>
			<data>
			z7/kZECXtx26CQQcf8SrP5Kli2gZRvG/h91xOwVuMTM=
			</data>
		</dict>
		<key>esewa.storyboardc/SDKSuccessfulPaymentViewController.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Wzqdt6aElEBQIt3CVLAJqDW9/XA=
			</data>
			<key>hash2</key>
			<data>
			z7/kZECXtx26CQQcf8SrP5Kli2gZRvG/h91xOwVuMTM=
			</data>
		</dict>
		<key>esewa.storyboardc/SdkLoginViewController.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			WUw3QckRX75v5nC7jDT4Lcy1MsY=
			</data>
			<key>hash2</key>
			<data>
			p9s9q1zE6n26nyQeaeMYmVXGGQLm7BXXjMFbzWOowVU=
			</data>
		</dict>
		<key>esewa.storyboardc/SdkLoginViewController.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			WUw3QckRX75v5nC7jDT4Lcy1MsY=
			</data>
			<key>hash2</key>
			<data>
			p9s9q1zE6n26nyQeaeMYmVXGGQLm7BXXjMFbzWOowVU=
			</data>
		</dict>
		<key>esewa.storyboardc/SdkPaymentViewController.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			kLoFSoLs6flGKoLRn3xXwjS8DSQ=
			</data>
			<key>hash2</key>
			<data>
			jhb3++G4bFnriirWJWjHBinox3pxKwv9WlRymCeoo4Q=
			</data>
		</dict>
		<key>esewa.storyboardc/SdkPaymentViewController.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			kLoFSoLs6flGKoLRn3xXwjS8DSQ=
			</data>
			<key>hash2</key>
			<data>
			jhb3++G4bFnriirWJWjHBinox3pxKwv9WlRymCeoo4Q=
			</data>
		</dict>
		<key>esewa.storyboardc/vqW-EK-dzU-view-VU3-iX-3i7.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			PwOCgqS3RrDLvjnQTRihU460mQY=
			</data>
			<key>hash2</key>
			<data>
			piYy+EQYA3SOibt3Pxf0MjQspL886mPXZmc3a91BspI=
			</data>
		</dict>
		<key>esewa.storyboardc/vqW-EK-dzU-view-VU3-iX-3i7.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			ZdhZsjEfRsZJcXOYCdIrHz28l/o=
			</data>
			<key>hash2</key>
			<data>
			t0aiNp7ieRu23CI6on1HUU5DUXwjl6FBU64d58Cu3qw=
			</data>
		</dict>
		<key>esewa.storyboardc/xhf-ct-UR2-view-Aav-pU-0Lo.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			hVNeemgK3q0dt7nyJxGdNITTH4s=
			</data>
			<key>hash2</key>
			<data>
			3jRzp+WBPbfeDJlDUD0TnvioC0YHTNKqIv+tbHELYBg=
			</data>
		</dict>
		<key>esewa.storyboardc/xhf-ct-UR2-view-Aav-pU-0Lo.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			kn5/C6QC+sr3cJYqPXUEwq5gC/E=
			</data>
			<key>hash2</key>
			<data>
			VFejtc9Lfazr2rJSZuaNYthFoLXgcb3ZSuk0eADZ5VE=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
