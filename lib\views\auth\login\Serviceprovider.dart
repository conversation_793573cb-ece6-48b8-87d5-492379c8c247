import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:smartsewa/network/services/authServices/auth_controller.dart';
import 'package:smartsewa/network/services/userdetails/current_user_controller.dart';
import 'package:smartsewa/views/user_screen/approval/approval_screen.dart';

import '../../widgets/buttons/app_buttons.dart';
import '../registration/user_registration.dart';
import 'dart:core';

class Serviceprovider extends StatefulWidget {
  const Serviceprovider({super.key});

  @override
  State<Serviceprovider> createState() => _ServiceproviderState();
}

class _ServiceproviderState extends State<Serviceprovider> {
  final controller = Get.put(AuthController());
  final userController = Get.put(CurrentUserController());

  // bool _showAlert = false;
  //
  @override
  void initState() {
    super.initState();
    userController.getCurrentUser();
    // _checkFirstLogin();
  }

  //
  // Future<void> _checkFirstLogin() async {
  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   bool isFirstLogin = prefs.getBool('firstlogin') ?? true;
  //   if (isFirstLogin) {
  //     setState(() {
  //       _showAlert = true;
  //     });
  //     await prefs.setBool('firstlogin', false);
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;

    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 30),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              'assets/Logo.png',
              height: size.height * 0.25,
            ),
            SizedBox(height: size.height * 0.05),
            const Text(
              'Welcome to Smart Sewa \nSolutions',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontFamily: 'hello',
                fontSize: 23,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
            SizedBox(height: size.height * 0.02),
            const Text(
              'Login Type?',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.white54,
              ),
            ),
            SizedBox(height: size.height * 0.005),
            AppButton(
                name: "Become a User",
                onPressed: () {
                  Get.to(() => UserRegistration());
                  //    Get.to(() => const MainScreen());
                }),
            SizedBox(height: size.height * 0.04),

            AppButton(
                name: 'Become a service provider ',
                onPressed: () async {
                  //first screen navigate
                  var result = await Get.to(() => UserRegistration());

                  //check if the result is null or not
                  if (result != null) {
                    //navigate to second screen
                    Get.to(() => ApprovalScreen);
                  }
                })

            // AppButton(
            //   name: 'Become a Service provider',
            //   onPressed: () async{
            //     //firstscreen
            //     Get.to(() => UserRegistration()).then((result) {
            //       //checking if the first condition is null or not
            //       if (result != null) {
            //         //navigate to second screen
            //         Get.to(() => "/");
            //       }
            //     });
            //   },
            // )

            // AppButton(
            //   name:'Become a service Provider',
            //   onPressed: (){
            //     //first screen
            //     Get.to(()=> UserRegistration()).then((value){
            //       if(value ==true){
            //         //second screen when first scren completed
            //         Get.to(()=> "/");
            //       } else{
            //         //handle the case when the first screen is not completed
            //         printf("firstscreen not completed ");
            //       }
            //     });
            //   },
            // );
            // AppButton(
            //     name: 'Become a Service Provider',
            //     onPressed: () {
            //       Get.to(() => UserRegistration());
            //     }),
          ],
        ),
      ),
    );
  }

  buildAlert() {
    Size size = MediaQuery.of(context).size;

    return Container(
      color: Colors.white,
      margin: EdgeInsets.symmetric(
        vertical: size.aspectRatio * 520,
        horizontal: size.aspectRatio * 60,
      ),
      child: Column(
        children: [
          Container(
            height: size.height * 0.15,
            color: Colors.greenAccent,
            child: const Center(
              child: Icon(
                Icons.check_circle_outline,
                color: Colors.white,
                size: 100,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
