// import 'dart:async';
// import 'dart:convert';
// import 'dart:io';
// import 'package:http/http.dart' as http;

// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:smartsewa/core/development/console.dart';
// import 'package:smartsewa/network/base_client.dart';
// import 'package:smartsewa/network/services/authServices/auth_controller.dart';
// import 'package:smartsewa/views/widgets/custom_toasts.dart';
// import 'package:timer_button_fork/timer_button_fork.dart';

// import '../otpField/otp_field.dart';
// import 'change_password_forgetScreen.dart';

// class ForgetOtpScreen extends StatefulWidget {
//   int number;
//   String phone;
//   ForgetOtpScreen({
//     super.key,
//     required this.number,
//     required this.phone,
//   });

//   @override
//   State<ForgetOtpScreen> createState() => _ForgetOtpScreenState();
// }

// class _ForgetOtpScreenState extends State<ForgetOtpScreen> {
//   final authController = Get.put(AuthController());
//   var tim = DateTime.now().microsecondsSinceEpoch;
//   String baseUrl = BaseClient().baseUrl;
//   var isLoading = false.obs;

//   @override
//   void dispose() {
//     authController.otpController.clear();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     Size size = MediaQuery.of(context).size;
//     consolelog(tim);

//     double buttonHeight = size.height * 0.08;
//     double buttonWidth = size.width * 0.4;

//     return Scaffold(
//       body: SingleChildScrollView(
//         child: Padding(
//           padding: EdgeInsets.all(size.aspectRatio * 68),
//           child: Column(
//             children: [
//               SizedBox(height: size.height * 0.1),
//               Image.asset(
//                 'assets/Logo.png',
//                 height: size.height * 0.2,
//               ),
//               SizedBox(height: size.height * 0.04),
//               Text(
//                 "Enter your OTP sent to the number +977 ${widget.phone}",
//                 textAlign: TextAlign.center,
//                 style: const TextStyle(
//                   fontSize: 24,
//                   fontWeight: FontWeight.w500,
//                   // color: const Color.fromRGBO(0, 131, 143, 1),
//                 ),
//               ),
//               SizedBox(height: size.height * 0.04),
//               OtpField(
//                 controller: authController.otpController,
//               ),
//               SizedBox(height: size.height * 0.07),
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                 children: [
//                   Container(
//                     height: buttonHeight,
//                     width: buttonWidth,
//                     child: TimerButton(
//                       label: 'Resend Otp',
//                       onPressed: () async {
//                         try {
//                           var request = http.MultipartRequest(
//                             'POST',
//                             Uri.parse(
//                                 '$baseUrl/api/v1/auth/${widget.phone}/forget-password'),
//                           );

//                           http.StreamedResponse response =
//                               await request.send().timeout(
//                             const Duration(seconds: 20),
//                             onTimeout: () {
//                               throw TimeoutException('Request timeout');
//                             },
//                           );

//                           String responseBody =
//                               await response.stream.bytesToString();
//                           consolelog(response.statusCode);

//                           if (response.statusCode == 200) {
//                             successToast(
//                                 msg: '${jsonDecode(responseBody)['message']}');
//                           } else {
//                             errorToast(
//                                 msg: '${jsonDecode(responseBody)['message']}');
//                             consolelog(response.reasonPhrase);
//                           }
//                         } catch (e) {
//                           if (e is TimeoutException) {
//                             errorToast(msg: 'Request Timeout: ${e.message}');
//                             isLoading.value = false;
//                           } else if (e is SocketException) {
//                             errorToast(msg: 'Network Error: ${e.message}');
//                             isLoading.value = false;
//                           } else if (e is http.ClientException) {
//                             errorToast(msg: 'Client Error: ${e.message}');
//                             isLoading.value = false;
//                           } else {
//                             errorToast(
//                                 msg: 'Unexpected error: ${e.toString()}');
//                             consolelog('Unexpected error: $e');
//                             isLoading.value = false;
//                           }
//                         }
//                       },
//                       timeOutInSeconds: 120,
//                       color: const Color.fromARGB(255, 0, 131, 143),
//                       disabledColor: const Color.fromARGB(255, 0, 131, 143),
//                       disabledTextStyle: const TextStyle(color: Colors.white),
//                     ),
//                   ),
//                   SizedBox(
//                     width: size.width * 0.02,
//                   ),
//                   Container(
//                     height: buttonHeight,
//                     width: buttonWidth,
//                     child: ElevatedButton(
//                       onPressed: () async {
//                         var request = http.MultipartRequest(
//                             'POST',
//                             Uri.parse(
//                                 '$baseUrl/api/v1/auth/${widget.phone}/verify-otp'));
//                         request.fields.addAll({
//                           'otp':
//                               '${authController.otpController.text.toString()}'
//                         });
//                         http.StreamedResponse response = await request.send();
//                         consolelog(response.statusCode);
//                         String responseBody =
//                             await response.stream.bytesToString();
//                         consolelog(responseBody);

//                         if (response.statusCode == 200) {
//                           successToast(
//                               msg: '${jsonDecode(responseBody)['message']} ');
//                           Navigator.push(
//                             context,
//                             MaterialPageRoute(
//                               builder: (context) => ForgetChangePassword(
//                                 mobileNumber: widget.phone,
//                               ),
//                             ),
//                           );
//                         } else {
//                           errorToast(
//                               msg: '${jsonDecode(responseBody)['message']} ');

//                           consolelog(response.reasonPhrase);
//                         }
//                       },
//                       style: ElevatedButton.styleFrom(
//                         backgroundColor: const Color.fromRGBO(0, 131, 143, 1),
//                       ),
//                       child: const Text('Change Password'),
//                     ),
//                   ),
//                 ],
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

// import 'dart:async';
// import 'dart:convert';
// import 'dart:io';
// import 'package:http/http.dart' as http;
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:smartsewa/core/development/console.dart';
// import 'package:smartsewa/network/base_client.dart';
// import 'package:smartsewa/network/services/authServices/auth_controller.dart';
// import 'package:smartsewa/views/widgets/custom_toasts.dart';
// import 'package:timer_button_fork/timer_button_fork.dart';
// import '../otpField/otp_field.dart';
// import 'change_password_forgetScreen.dart';

// class ForgetOtpScreen extends StatefulWidget {
//   final int number;
//   final String phone;

//   const ForgetOtpScreen({
//     super.key,
//     required this.number,
//     required this.phone,
//   });

//   @override
//   State<ForgetOtpScreen> createState() => _ForgetOtpScreenState();
// }

// class _ForgetOtpScreenState extends State<ForgetOtpScreen> {
//   final authController = Get.put(AuthController());
//   var tim = DateTime.now().microsecondsSinceEpoch;
//   String baseUrl = BaseClient().baseUrl;
//   var isLoading = false.obs;

//   @override
//   void dispose() {
//     authController.otpController.clear();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     Size size = MediaQuery.of(context).size;

//     return Scaffold(
//       body: Stack(
//         children: [
//           // Background Gradient
//           Container(
//             decoration: const BoxDecoration(
//               gradient: LinearGradient(
//                 colors: [
//                   Color.fromARGB(255, 253, 255, 255),
//                   Color.fromARGB(255, 255, 255, 255)
//                 ],
//                 begin: Alignment.topCenter,
//                 end: Alignment.bottomCenter,
//               ),
//             ),
//           ),
//           Center(
//             child: Padding(
//               padding: EdgeInsets.all(size.width * 0.08),
//               child: Container(
//                 padding: const EdgeInsets.all(20),
//                 decoration: BoxDecoration(
//                   color: Colors.white,
//                   borderRadius: BorderRadius.circular(20),
//                   boxShadow: [
//                     BoxShadow(
//                       color: Colors.black.withOpacity(0.2),
//                       blurRadius: 15,
//                       offset: const Offset(0, 5),
//                     ),
//                   ],
//                 ),
//                 child: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     SizedBox(height: size.height * 0.02),
//                     // Logo
//                     Image.asset(
//                       'assets/Logo.png',
//                       height: size.height * 0.15,
//                     ),
//                     SizedBox(height: size.height * 0.03),
//                     // Instruction Text
//                   Text(
//                       "Enter your OTP sent to the number +977 ${widget.phone}",
//                       textAlign: TextAlign.center,
//                       style: TextStyle(
//                         fontSize: MediaQuery.of(context).size.width * 0.05, // Responsive font size
//                         fontWeight: FontWeight.w500,
//                         color: const Color(0xFF00796B),
//                     ),
//                   ),

//                     SizedBox(height: size.height * 0.03),
//                     // OTP Field
//                     OtpField(
//                       controller: authController.otpController,
//                     ),
//                     SizedBox(height: size.height * 0.05),
//                     // Buttons Row
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                       children: [
//                         // Resend OTP Button with Timer
//                         TimerButton(
//                           label: 'Resend OTP',
//                           onPressed: () async {
//                             try {
//                               var request = http.MultipartRequest(
//                                 'POST',
//                                 Uri.parse(
//                                     '$baseUrl/api/v1/auth/${widget.phone}/forget-password'),
//                               );

//                               http.StreamedResponse response =
//                                   await request.send().timeout(
//                                 const Duration(seconds: 20),
//                                 onTimeout: () {
//                                   throw TimeoutException('Request timeout');
//                                 },
//                               );

//                               String responseBody =
//                                   await response.stream.bytesToString();
//                               consolelog(response.statusCode);

//                               if (response.statusCode == 200) {
//                                 successToast(
//                                     msg:
//                                         '${jsonDecode(responseBody)['message']}');
//                               } else {
//                                 errorToast(
//                                     msg:
//                                         '${jsonDecode(responseBody)['message']}');
//                                 consolelog(response.reasonPhrase);
//                               }
//                             } catch (e) {
//                               if (e is TimeoutException) {
//                                 errorToast(
//                                     msg: 'Request Timeout: ${e.message}');
//                                 isLoading.value = false;
//                               } else if (e is SocketException) {
//                                 errorToast(msg: 'Network Error: ${e.message}');
//                                 isLoading.value = false;
//                               } else if (e is http.ClientException) {
//                                 errorToast(msg: 'Client Error: ${e.message}');
//                                 isLoading.value = false;
//                               } else {
//                                 errorToast(
//                                     msg: 'Unexpected error: ${e.toString()}');
//                                 consolelog('Unexpected error: $e');
//                                 isLoading.value = false;
//                               }
//                             }
//                           },
//                           timeOutInSeconds: 120,
//                           color: const Color(0xFF00BCD4),
//                           disabledColor: const Color(0xFF80DEEA),
//                           disabledTextStyle:
//                               const TextStyle(color: Colors.white70),
//                         ),
//                         // Change Password Button
//                         ElevatedButton(
//                           onPressed: () async {
//                             var request = http.MultipartRequest(
//                                 'POST',
//                                 Uri.parse(
//                                     '$baseUrl/api/v1/auth/${widget.phone}/verify-otp'));
//                             request.fields.addAll({
//                               'otp':
//                                   authController.otpController.text.toString()
//                             });
//                             http.StreamedResponse response =
//                                 await request.send();
//                             consolelog(response.statusCode);
//                             String responseBody =
//                                 await response.stream.bytesToString();
//                             consolelog(responseBody);

//                             if (response.statusCode == 200) {
//                               successToast(
//                                   msg:
//                                       '${jsonDecode(responseBody)['message']} ');
//                               Navigator.push(
//                                 context,
//                                 MaterialPageRoute(
//                                   builder: (context) => ForgetChangePassword(
//                                     mobileNumber: widget.phone,
//                                   ),
//                                 ),
//                               );
//                             } else {
//                               errorToast(
//                                   msg:
//                                       '${jsonDecode(responseBody)['message']} ');

//                               consolelog(response.reasonPhrase);
//                             }
//                           },
//                           style: ElevatedButton.styleFrom(
//                             backgroundColor: const Color(0xFF009688),
//                             padding: const EdgeInsets.symmetric(
//                                 vertical: 8.0, horizontal: 3.0),
//                             shape: RoundedRectangleBorder(
//                               borderRadius: BorderRadius.circular(18),
//                             ),
//                           ),
//                           child: const Text(
//                             'Change Password',
//                             style: TextStyle(
//                               color: Colors.white,
//                               fontSize: 14,
//                               fontWeight: FontWeight.w600,
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                     SizedBox(height: size.height * 0.01),
//                   ],
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/network/base_client.dart';
import 'package:smartsewa/network/services/authServices/auth_controller.dart';
import 'package:smartsewa/views/widgets/custom_toasts.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';
import 'package:timer_button_fork/timer_button_fork.dart';
import '../otpField/otp_field.dart';
import 'change_password_forgetScreen.dart';

class ForgetOtpScreen extends StatefulWidget {
  final int number;
  final String phone;

  const ForgetOtpScreen({
    super.key,
    required this.number,
    required this.phone,
  });

  @override
  State<ForgetOtpScreen> createState() => _ForgetOtpScreenState();
}

class _ForgetOtpScreenState extends State<ForgetOtpScreen> {
  final authController = Get.put(AuthController());
  String baseUrl = BaseClient().baseUrl;
  var isLoading = false.obs;

  @override
  void dispose() {
    authController.otpController.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: myAppbar(context, true, ""),
      body: Stack(
        children: [
          // Background Gradient
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color.fromARGB(255, 253, 255, 255),
                  Color.fromARGB(255, 255, 255, 255),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
          Center(
            child: Padding(
              padding: EdgeInsets.all(size.width * 0.08),
              child: SingleChildScrollView(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Back Button
                      // Align(
                      //   alignment: Alignment.topLeft,
                      //   child: Container(
                      //     padding: const EdgeInsets.all(4),
                      //     decoration: const BoxDecoration(
                      //       color: Color(0xFF009688),
                      //       shape: BoxShape.circle,
                      //     ),
                      //     child: IconButton(
                      //       icon: const Icon(
                      //         Icons.arrow_back,
                      //         color: Colors.white,
                      //       ),
                      //       onPressed: () => Navigator.pop(context),
                      //     ),
                      //   ),
                      // ),
                      SizedBox(height: size.height * 0.02),
                      // Logo
                      Image.asset(
                        'assets/Logo.png',
                        height: size.height * 0.15,
                      ),
                      SizedBox(height: size.height * 0.03),
                      // Instruction Text
                      Text(
                        "Enter your OTP sent to the number +977 ${widget.phone}",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: size.width * 0.05, // Responsive font size
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF00796B),
                        ),
                      ),
                      SizedBox(height: size.height * 0.03),
                      // OTP Field
                      OtpField(
                        controller: authController.otpController,
                      ),
                      SizedBox(height: size.height * 0.05),
                      // Buttons Row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Resend OTP Button with Timer
                          Expanded(
                            child: TimerButton(
                              label: 'Resend OTP',
                              onPressed: () async {
                                try {
                                  var request = http.MultipartRequest(
                                    'POST',
                                    Uri.parse(
                                        '$baseUrl/api/v1/auth/${widget.phone}/forget-password'),
                                  );

                                  http.StreamedResponse response =
                                      await request.send().timeout(
                                    const Duration(seconds: 20),
                                    onTimeout: () {
                                      throw TimeoutException('Request timeout');
                                    },
                                  );

                                  String responseBody =
                                      await response.stream.bytesToString();
                                  consolelog(response.statusCode);

                                  if (response.statusCode == 200) {
                                    successToast(
                                        msg:
                                            '${jsonDecode(responseBody)['message']}');
                                  } else {
                                    errorToast(
                                        msg:
                                            '${jsonDecode(responseBody)['message']}');
                                    consolelog(response.reasonPhrase);
                                  }
                                } catch (e) {
                                  errorToast(
                                      msg: 'Unexpected error: ${e.toString()}');
                                  consolelog('Unexpected error: $e');
                                }
                              },
                              timeOutInSeconds: 120,
                              color: const Color(0xFF00BCD4),
                              disabledColor: const Color(0xFF80DEEA),
                              disabledTextStyle:
                                  const TextStyle(color: Colors.white70),
                            ),
                          ),
                          const SizedBox(width: 10),
                          // Change Password Button
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () async {
                                var request = http.MultipartRequest(
                                  'POST',
                                  Uri.parse(
                                      '$baseUrl/api/v1/auth/${widget.phone}/verify-otp'),
                                );
                                request.fields.addAll({
                                  'otp': authController.otpController.text
                                      .toString(),
                                });
                                http.StreamedResponse response =
                                    await request.send();
                                String responseBody =
                                    await response.stream.bytesToString();

                                if (response.statusCode == 200) {
                                  successToast(
                                      msg:
                                          '${jsonDecode(responseBody)['message']}');
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          ForgetChangePassword(
                                        mobileNumber: widget.phone,
                                      ),
                                    ),
                                  );
                                } else {
                                  errorToast(
                                      msg:
                                          '${jsonDecode(responseBody)['message']}');
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor:
                                    Color.fromARGB(240, 0, 131, 143),
                                padding: const EdgeInsets.symmetric(
                                    vertical: 8.0, horizontal: 5.0),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(18),
                                ),
                              ),
                              child: Text(
                                'Next',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: size.width *
                                      0.045, // Responsive font size
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: size.height * 0.01),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}














/*
import 'dart:convert';
import 'package:http/http.dart' as http;

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/network/base_client.dart';
import 'package:smartsewa/network/services/authServices/auth_controller.dart';
import 'package:smartsewa/views/widgets/custom_toasts.dart';
import 'package:timer_button_fork/timer_button_fork.dart';

import '../otpField/otp_field.dart';
import 'change_password_forgetScreen.dart';

class ForgetOtpScreen extends StatefulWidget {
  int number;
  String phone;
  ForgetOtpScreen({
    super.key,
    required this.number,
    required this.phone,
  });

  @override
  State<ForgetOtpScreen> createState() => _ForgetOtpScreenState();
}

class _ForgetOtpScreenState extends State<ForgetOtpScreen> {
  final authController = Get.put(AuthController());
  var tim = DateTime.now().microsecondsSinceEpoch;
  String baseUrl = BaseClient().baseUrl;

  @override
  void dispose() {
    authController.otpController.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    consolelog(tim);
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(size.aspectRatio * 68),
          child: Column(
            children: [
              SizedBox(height: size.height * 0.1),
              Image.asset(
                'assets/Logo.png',
                height: size.height * 0.2,
              ),
              SizedBox(height: size.height * 0.04),
              Text(
                "Enter your OTP sent to the number +977 ${widget.phone}",
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w500,
                  // color: const Color.fromRGBO(0, 131, 143, 1),
                ),
              ),
              SizedBox(height: size.height * 0.04),
              OtpField(
                controller: authController.otpController,
              ),
              SizedBox(height: size.height * 0.07),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: TimerButton(
                      label: 'Resend Otp',
                      onPressed: () async {
                        var request = http.MultipartRequest(
                            'POST',
                            Uri.parse(
                                '$baseUrl/api/v1/auth/${widget.phone}/forget-password'));

                        http.StreamedResponse response = await request.send();
                        consolelog(response.statusCode);
                        String responseBody =
                            await response.stream.bytesToString();

                        if (response.statusCode == 200) {
                          successToast(
                              msg: '${jsonDecode(responseBody)['message']} ');
                        } else {
                          errorToast(
                              msg: '${jsonDecode(responseBody)['message']} ');

                          consolelog(response.reasonPhrase);
                        }
                      },
                      timeOutInSeconds: 120,
                      color: const Color.fromARGB(255, 0, 131, 143),
                      disabledColor: const Color.fromARGB(255, 0, 131, 143),
                      disabledTextStyle: const TextStyle(color: Colors.white),
                    ),
                  ),
                  SizedBox(
                    width: size.width * 0.02,
                  ),
                  ElevatedButton(
                    onPressed: () async {
                      var request = http.MultipartRequest(
                          'POST',
                          Uri.parse(
                              '$baseUrl/api/v1/auth/${widget.phone}/verify-otp'));
                      request.fields.addAll({
                        'otp': '${authController.otpController.text.toString()}'
                      });
                      http.StreamedResponse response = await request.send();
                      consolelog(response.statusCode);
                      String responseBody =
                          await response.stream.bytesToString();
                      consolelog(responseBody);

                      if (response.statusCode == 200) {
                        successToast(
                            msg: '${jsonDecode(responseBody)['message']} ');
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ForgetChangePassword(
                              mobileNumber: widget.phone,
                            ),
                          ),
                        );
                      } else {
                        errorToast(
                            msg: '${jsonDecode(responseBody)['message']} ');

                        consolelog(response.reasonPhrase);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color.fromRGBO(0, 131, 143, 1),
                    ),
                    child: const Text('Change Password'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}



*/