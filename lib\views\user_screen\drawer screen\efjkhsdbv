appBar: isGuest
    ? AppBar(
        backgroundColor: const Color.fromARGB(240, 0, 131, 143),
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () {
            Get.to(() => LoginScreen());
          },
        ),
        title: InkWell(
          child: RichTex<PERSON>(
            text: TextSpan(
              children: [
                TextSpan(
                  text: 'Click on  ',
                  style: TextStyle(
                    fontSize: size.width * 0.036,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                TextSpan(
                  text: 'Sign Up',
                  style: TextStyle(
                    fontSize: size.width * 0.045,
                    fontWeight: FontWeight.bold,
                    color: const Color.fromARGB(255, 247, 170, 102),
                    decoration: TextDecoration.underline,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      Get.to(() => UserRegistration());
                    },
                ),
                TextSpan(
                  text: ' or ',
                  style: TextStyle(
                    fontSize: size.width * 0.036,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                TextSpan(
                  text: 'Login',
                  style: TextStyle(
                    fontSize: size.width * 0.045,
                    fontWeight: FontWeight.bold,
                    color: const Color.fromARGB(255, 102, 204, 247),
                    decoration: TextDecoration.underline,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      Get.to(() => LoginScreen());
                    },
                ),
                TextSpan(
                  text: ' to access all features.',
                  style: TextStyle(
                    fontSize: size.width * 0.036,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
        centerTitle: true,
        elevation: 4,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(10),
          ),
        ),
      )
    : PreferredSize(
        preferredSize: Size.fromHeight(size.height * 0.07),
        child: Container(
          decoration: BoxDecoration(
            color: const Color.fromRGBO(0, 131, 143, 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: SafeArea(
            child: Row(
              children: [
                IconButton(
                  onPressed: () {
                    _scaffoldState.currentState!.openDrawer();
                  },
                  icon: Icon(
                    Icons.menu,
                    size: size.width * 0.065,
                    color: Colors.white,
                  ),
                ),
                SizedBox(width: size.width * 0.02),
                Obx(() {
                  if (controller.isLoading.value) {
                    return Expanded(
                      child: Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          width: size.width * 0.3,
                          height: size.height * 0.02,
                          color: Colors.white,
                        ),
                      ),
                    );
                  }
                  return Expanded(
                    child: Text(
                      controller.currentUserData.value.fullName?.isNotEmpty == true
                          ? "${controller.currentUserData.value.fullName?.toUppercaseFirstLetter()}"
                          : "Loading...",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: size.width * 0.045,
                        fontWeight: FontWeight.w700,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  );
                }),
                _buildHeaderIcon(
                  Icons.person,
                  size.width * 0.07,
                  () => Get.to(() => const Profile()),
                ),
                _buildHeaderIcon(
                  Icons.search,
                  size.width * 0.07,
                  () => showSearch(
                    context: context,
                    delegate: DataSearch(services),
                  ),
                ),
                _buildNotificationIcon(size),
                _buildHeaderIcon(
                  Icons.settings,
                  size.width * 0.07,
                  () => Get.to(() => const SettingPage()),
                ),
              ],
            ),
          ),
        ),
      )