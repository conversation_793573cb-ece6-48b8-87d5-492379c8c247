// import 'package:flutter/material.dart';

// class CreateVacancyPage extends StatefulWidget {
//   const CreateVacancyPage({Key? key}) : super(key: key);

//   @override
//   State<CreateVacancyPage> createState() => _CreateVacancyPageState();
// }

// class _CreateVacancyPageState extends State<CreateVacancyPage> {
//   final _formKey = GlobalKey<FormState>();
//   final themeColor = const Color.fromARGB(240, 0, 131, 143);

//   // Form controllers
//   final _companyNameController = TextEditingController();
//   final _positionController = TextEditingController();
//   final _locationController = TextEditingController();
//   final _categoryController = TextEditingController();
//   final _levelController = TextEditingController();
//   final _vacanciesController = TextEditingController();
//   final _salaryController = TextEditingController();
//   final _deadlineController = TextEditingController();
//   final _educationController = TextEditingController();
//   final _experienceController = TextEditingController();
//   final _requirementsController = TextEditingController();
//   final _responsibilitiesController = TextEditingController();

//   @override
//   void dispose() {
//     _companyNameController.dispose();
//     _positionController.dispose();
//     _locationController.dispose();
//     _categoryController.dispose();
//     _levelController.dispose();
//     _vacanciesController.dispose();
//     _salaryController.dispose();
//     _deadlineController.dispose();
//     _educationController.dispose();
//     _experienceController.dispose();
//     _requirementsController.dispose();
//     _responsibilitiesController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         backgroundColor: themeColor,
//         title: const Text(
//           'Create Vacancy',
//           style: TextStyle(color: Colors.white),
//         ),
//         leading: IconButton(
//           icon: const Icon(Icons.arrow_back, color: Colors.white),
//           onPressed: () => Navigator.pop(context),
//         ),
//       ),
//       body: Form(
//         key: _formKey,
//         child: SingleChildScrollView(
//           padding: const EdgeInsets.all(16.0),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               _buildSection(
//                 'Company Details',
//                 [
//                   _buildTextField(
//                     controller: _companyNameController,
//                     label: 'Company Name',
//                     icon: Icons.business,
//                   ),
//                   _buildTextField(
//                     controller: _positionController,
//                     label: 'Position Title',
//                     icon: Icons.work,
//                   ),
//                   _buildTextField(
//                     controller: _locationController,
//                     label: 'Location',
//                     icon: Icons.location_on,
//                   ),
//                 ],
//               ),
//               _buildSection(
//                 'Basic Information',
//                 [
//                   _buildTextField(
//                     controller: _categoryController,
//                     label: 'Job Category',
//                     icon: Icons.category,
//                   ),
//                   _buildTextField(
//                     controller: _levelController,
//                     label: 'Job Level',
//                     icon: Icons.trending_up,
//                   ),
//                   _buildTextField(
//                     controller: _vacanciesController,
//                     label: 'Number of Vacancies',
//                     icon: Icons.people,
//                     keyboardType: TextInputType.number,
//                   ),
//                   _buildTextField(
//                     controller: _salaryController,
//                     label: 'Offered Salary Range',
//                     icon: Icons.attach_money,
//                   ),
//                   _buildTextField(
//                     controller: _deadlineController,
//                     label: 'Application Deadline',
//                     icon: Icons.calendar_today,
//                     onTap: () async {
//                       final date = await showDatePicker(
//                         context: context,
//                         initialDate: DateTime.now(),
//                         firstDate: DateTime.now(),
//                         lastDate: DateTime.now().add(const Duration(days: 365)),
//                       );
//                       if (date != null) {
//                         _deadlineController.text =
//                             "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
//                       }
//                     },
//                     readOnly: true,
//                   ),
//                 ],
//               ),
//               _buildSection(
//                 'Job Specification',
//                 [
//                   _buildTextField(
//                     controller: _educationController,
//                     label: 'Required Education',
//                     icon: Icons.school,
//                   ),
//                   _buildTextField(
//                     controller: _experienceController,
//                     label: 'Required Experience',
//                     icon: Icons.timeline,
//                   ),
//                 ],
//               ),
//               _buildSection(
//                 'Other Specifications',
//                 [
//                   _buildTextField(
//                     controller: _requirementsController,
//                     label: 'Position Requirements',
//                     icon: Icons.list,
//                     maxLines: 5,
//                     helperText: 'Enter requirements separated by new lines',
//                   ),
//                   _buildTextField(
//                     controller: _responsibilitiesController,
//                     label: 'Roles and Responsibilities',
//                     icon: Icons.assignment,
//                     maxLines: 5,
//                     helperText: 'Enter responsibilities separated by new lines',
//                   ),
//                 ],
//               ),
//               const SizedBox(height: 20),
//               SizedBox(
//                 width: double.infinity,
//                 child: ElevatedButton(
//                   style: ElevatedButton.styleFrom(
//                     backgroundColor: themeColor,
//                     padding: const EdgeInsets.symmetric(vertical: 12),
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(8),
//                     ),
//                   ),
//                   onPressed: () {
//                     if (_formKey.currentState!.validate()) {
//                       // Handle form submission
//                       // You can access all the values using the controllers
//                       // and process them as needed
//                     }
//                   },
//                   child: const Text(
//                     'Create Vacancy',
//                     style: TextStyle(color: Colors.white),
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildSection(String title, List<Widget> children) {
//     return Container(
//       margin: const EdgeInsets.only(bottom: 24),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Container(
//             padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//             color: themeColor,
//             width: double.infinity,
//             child: Text(
//               title,
//               style: const TextStyle(
//                 color: Colors.white,
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//           ),
//           const SizedBox(height: 16),
//           ...children,
//         ],
//       ),
//     );
//   }

//   Widget _buildTextField({
//     required TextEditingController controller,
//     required String label,
//     required IconData icon,
//     int maxLines = 1,
//     String? helperText,
//     TextInputType? keyboardType,
//     bool readOnly = false,
//     VoidCallback? onTap,
//   }) {
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 16),
//       child: TextFormField(
//         controller: controller,
//         decoration: InputDecoration(
//           labelText: label,
//           prefixIcon: Icon(icon),
//           border: OutlineInputBorder(
//             borderRadius: BorderRadius.circular(8),
//           ),
//           helperText: helperText,
//         ),
//         maxLines: maxLines,
//         keyboardType: keyboardType,
//         readOnly: readOnly,
//         onTap: onTap,
//         validator: (value) {
//           if (value == null || value.isEmpty) {
//             return 'Please enter $label';
//           }
//           return null;
//         },
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart'; // Import image_picker package
import 'dart:io';

class CreateVacancyPage extends StatefulWidget {
  const CreateVacancyPage({Key? key}) : super(key: key);

  @override
  State<CreateVacancyPage> createState() => _CreateVacancyPageState();
}

class _CreateVacancyPageState extends State<CreateVacancyPage> {
  final _formKey = GlobalKey<FormState>();
  final themeColor = const Color.fromARGB(240, 0, 131, 143);

  // Form controllers
  final _companyNameController = TextEditingController();
  final _positionController = TextEditingController();
  final _locationController = TextEditingController();
  final _categoryController = TextEditingController();
  final _levelController = TextEditingController();
  final _vacanciesController = TextEditingController();
  final _salaryController = TextEditingController();
  final _deadlineController = TextEditingController();
  final _educationController = TextEditingController();
  final _experienceController = TextEditingController();
  final _requirementsController = TextEditingController();
  final _responsibilitiesController = TextEditingController();

  File? _logoImage;

  final _picker = ImagePicker();

  @override
  void dispose() {
    _companyNameController.dispose();
    _positionController.dispose();
    _locationController.dispose();
    _categoryController.dispose();
    _levelController.dispose();
    _vacanciesController.dispose();
    _salaryController.dispose();
    _deadlineController.dispose();
    _educationController.dispose();
    _experienceController.dispose();
    _requirementsController.dispose();
    _responsibilitiesController.dispose();
    super.dispose();
  }

  Future<void> _pickLogo() async {
    final pickedFile = await _picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      setState(() {
        _logoImage = File(pickedFile.path);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: themeColor,
        title: const Text(
          'Create Vacancy',
          style: TextStyle(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Company Logo Upload Section
              _buildSection(
                'Company Logo',
                [
                  GestureDetector(
                    onTap: _pickLogo,
                    child: Container(
                      height: 100,
                      width: 100,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: _logoImage == null
                          ? const Icon(
                              Icons.add_a_photo,
                              size: 50,
                              color: Colors.grey,
                            )
                          : ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.file(
                                _logoImage!,
                                fit: BoxFit.cover,
                              ),
                            ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
              _buildSection(
                'Company Details',
                [
                  _buildTextField(
                    controller: _companyNameController,
                    label: 'Company Name',
                    icon: Icons.business,
                  ),
                  _buildTextField(
                    controller: _positionController,
                    label: 'Position Title',
                    icon: Icons.work,
                  ),
                  _buildTextField(
                    controller: _locationController,
                    label: 'Location',
                    icon: Icons.location_on,
                  ),
                ],
              ),
              _buildSection(
                'Basic Information',
                [
                  _buildTextField(
                    controller: _categoryController,
                    label: 'Job Category',
                    icon: Icons.category,
                  ),
                  _buildTextField(
                    controller: _levelController,
                    label: 'Job Level',
                    icon: Icons.trending_up,
                  ),
                  _buildTextField(
                    controller: _vacanciesController,
                    label: 'Number of Vacancies',
                    icon: Icons.people,
                    keyboardType: TextInputType.number,
                  ),
                  _buildTextField(
                    controller: _salaryController,
                    label: 'Offered Salary Range',
                    icon: Icons.attach_money,
                  ),
                  _buildTextField(
                    controller: _deadlineController,
                    label: 'Application Deadline',
                    icon: Icons.calendar_today,
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );
                      if (date != null) {
                        _deadlineController.text =
                            "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
                      }
                    },
                    readOnly: true,
                  ),
                ],
              ),
              _buildSection(
                'Job Specification',
                [
                  _buildTextField(
                    controller: _educationController,
                    label: 'Required Education',
                    icon: Icons.school,
                  ),
                  _buildTextField(
                    controller: _experienceController,
                    label: 'Required Experience',
                    icon: Icons.timeline,
                  ),
                ],
              ),
              _buildSection(
                'Other Specifications',
                [
                  _buildTextField(
                    controller: _requirementsController,
                    label: 'Position Requirements',
                    icon: Icons.list,
                    maxLines: 5,
                    helperText: 'Enter requirements separated by new lines',
                  ),
                  _buildTextField(
                    controller: _responsibilitiesController,
                    label: 'Roles and Responsibilities',
                    icon: Icons.assignment,
                    maxLines: 5,
                    helperText: 'Enter responsibilities separated by new lines',
                  ),
                ],
              ),
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: themeColor,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      // Handle form submission and proceed to payment
                      // You can access all the values using the controllers
                      // Proceed to payment screen after form submission
                      _navigateToPaymentScreen();
                    }
                  },
                  child: const Text(
                    'Submit & Proceed to Payment',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToPaymentScreen() {
    // Navigate to the payment screen (implement a new screen for this)
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => PaymentScreen()),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            color: themeColor,
            width: double.infinity,
            child: Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    int maxLines = 1,
    String? helperText,
    TextInputType? keyboardType,
    bool readOnly = false,
    VoidCallback? onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          prefixIcon: Icon(icon),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          helperText: helperText,
        ),
        maxLines: maxLines,
        keyboardType: keyboardType,
        readOnly: readOnly,
        onTap: onTap,
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Please enter $label';
          }
          return null;
        },
      ),
    );
  }
}

class PaymentScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            // Implement payment logic here
          },
          child: const Text('Proceed to Payment'),
        ),
      ),
    );
  }
}
