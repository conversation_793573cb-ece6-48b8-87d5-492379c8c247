// import 'dart:developer';

// import 'package:flutter/material.dart';
// import 'package:smartsewa/network/services/userdetails/current_user_controller.dart';
// import 'package:url_launcher/url_launcher.dart';
// import 'package:get/get.dart';

// import '../../../network/services/contact_services/contact_controller.dart';
// import '../../widgets/my_appbar.dart';

// class ContactScreen extends StatefulWidget {
//   const ContactScreen({Key? key}) : super(key: key);

//   @override
//   State<ContactScreen> createState() => _ContactScreenState();
// }

// class _ContactScreenState extends State<ContactScreen> {
//   final _controller = Get.put(ContactsController());

//   final controller = Get.put(CurrentUserController());

//   final GlobalKey<ScaffoldState> _scaffoldState = GlobalKey<ScaffoldState>();

//   void _launchPhone(String phoneNumber) async {
//     String url = 'tel:$phoneNumber';
//     if (await canLaunch(url)) {
//       await launch(url);
//     } else {
//       throw 'Could not launch $url';
//     }
//   }

//   void openMail() async {
//     String? encodeQueryParameters(Map<String, String> params) {
//       return params.entries
//           .map((MapEntry<String, String> e) =>
//               '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
//           .join('&');
//     }

//     final Uri emailLaunchUri = Uri(
//       scheme: 'mailto',
//       path: '${_controller.feedbackEmail}',
//       query: encodeQueryParameters(<String, String>{
//         'subject': 'Feedback !!!',
//       }),
//     );
//     try {
//       await launchUrl(emailLaunchUri);
//     } catch (e) {
//       log(e.toString());
//     }
//   }

//   void _launchMap() async {
//     const latitude = 27.7173;
//     const longitude = 85.3466;
//     String url =
//         'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';
//     if (await canLaunch(url)) {
//       await launch(url);
//     } else {
//       throw 'Could not launch $url';
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     Size size = MediaQuery.of(context).size;

//     return Scaffold(
//       key: _scaffoldState,
//       backgroundColor: Colors.white,
//       appBar: myAppbar(context, true, "Contact Us"),
//       body: SingleChildScrollView(
//         padding: const EdgeInsets.all(20),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             const SizedBox(height: 20),
//             Image.asset(
//               'assets/Logo.png',
//               height: size.height * 0.2,
//             ),
//             SizedBox(height: size.height * 0.10),
//             Container(
//               height: size.height * 0.3,
//               padding: const EdgeInsets.all(20),
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.circular(15),
//                 gradient: const LinearGradient(
//                   begin: Alignment.topLeft,
//                   end: Alignment.bottomRight,
//                   colors: [
//                     Color.fromARGB(255, 0, 131, 143),
//                     Colors.white,
//                   ],
//                 ),
//                 boxShadow: [
//                   BoxShadow(
//                     color: Colors.grey.withOpacity(0.5),
//                     spreadRadius: 5,
//                     blurRadius: 7,
//                     offset: const Offset(0, 3),
//                   ),
//                 ],
//               ),
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   _buildContactRow(
//                     icon: Icons.phone,
//                     text: '${_controller.contactUs}',
//                     actionText: 'Call',
//                     onPressed: () {
//                       _launchPhone('01-5917785');
//                     },
//                   ),
//                   _buildContactRow(
//                     icon: Icons.email,
//                     text: '${_controller.email}',
//                     actionText: 'Send Email',
//                     onPressed: () async {
//                       openMail();
//                     },
//                   ),
//                   _buildContactRow(
//                     icon: Icons.location_on,
//                     text: '${_controller.address}',
//                     actionText: 'View on Map',
//                     onPressed: () {
//                       _launchMap();
//                     },
//                   ),
//                 ],
//               ),
//             ),
//             SizedBox(height: size.height * 0.08),
//             const AnimatedTextWidget(text: 'Feel free to contact us'),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildContactRow({
//     required IconData icon,
//     required String text,
//     required String actionText,
//     required VoidCallback onPressed,
//   }) {
//     return Column(
//       children: [
//         const SizedBox(height: 10),
//         Row(
//           children: [
//             Icon(icon, color: Colors.black, size: 24),
//             const SizedBox(width: 10),
//             Expanded(
//               child: Text(
//                 text,
//                 style: const TextStyle(
//                   color: Colors.black,
//                   fontSize: 13,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//             ),
//             ConstrainedBox(
//               constraints:
//                   const BoxConstraints.tightFor(width: 120, height: 40),
//               child: TextButton(
//                 onPressed: onPressed,
//                 style: TextButton.styleFrom(
//                   foregroundColor: Colors.black,
//                   backgroundColor: Colors.grey[200],
//                   padding: const EdgeInsets.all(10),
//                   shape: RoundedRectangleBorder(
//                     borderRadius: BorderRadius.circular(10),
//                   ),
//                 ),
//                 child: Text(actionText),
//               ),
//             ),
//           ],
//         ),
//       ],
//     );
//   }
// }

// class AnimatedTextWidget extends StatefulWidget {
//   final String text;

//   const AnimatedTextWidget({Key? key, required this.text}) : super(key: key);

//   @override
//   _AnimatedTextWidgetState createState() => _AnimatedTextWidgetState();
// }

// class _AnimatedTextWidgetState extends State<AnimatedTextWidget>
//     with SingleTickerProviderStateMixin {
//   late AnimationController _controller;
//   late Animation<double> _animation;

//   @override
//   void initState() {
//     super.initState();
//     _controller =
//         AnimationController(vsync: this, duration: const Duration(seconds: 1));
//     _animation = Tween(begin: 0.0, end: 1.0).animate(_controller);
//     _controller.repeat(reverse: true);
//   }

//   @override
//   void dispose() {
//     _controller.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return FadeTransition(
//       opacity: _animation,
//       child: Text(
//         widget.text,
//         style: const TextStyle(
//           color: Color.fromRGBO(0, 131, 143, 1),
//           fontSize: 16,
//           fontWeight: FontWeight.bold,
//         ),
//       ),
//     );
//   }
// }

import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:smartsewa/network/services/userdetails/current_user_controller.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:get/get.dart';
import '../../../network/services/contact_services/contact_controller.dart';
import '../../widgets/my_appbar.dart';

class ContactScreen extends StatefulWidget {
  const ContactScreen({super.key});

  @override
  State<ContactScreen> createState() => _ContactScreenState();
}

class _ContactScreenState extends State<ContactScreen> {
  final _controller = Get.put(ContactsController());
  final controller = Get.put(CurrentUserController());
  final GlobalKey<ScaffoldState> _scaffoldState = GlobalKey<ScaffoldState>();

  void _launchPhone(String phoneNumber) async {
    String url = 'tel:$phoneNumber';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  void openMail() async {
    String? encodeQueryParameters(Map<String, String> params) {
      return params.entries
          .map((MapEntry<String, String> e) =>
              '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
          .join('&');
    }

    final Uri emailLaunchUri = Uri(
      scheme: 'mailto',
      path: '${_controller.feedbackEmail}',
      query: encodeQueryParameters(<String, String>{'subject': 'Feedback !!!'}),
    );
    try {
      await launchUrl(emailLaunchUri);
    } catch (e) {
      log(e.toString());
    }
  }

  void _launchMap() async {
    const latitude = 27.7173;
    const longitude = 85.3466;
    String url =
        'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;

    return Scaffold(
      key: _scaffoldState,
      backgroundColor: Colors.white,
      appBar: myAppbar(context, true, "Contact Us"),
      body: Obx(() {
        if (_controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        } else {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Wrapping Logo and Contact Information in a single Card
                _buildSectionCard(
                  context: context,
                  title: "Contact Information",
                  child: Column(
                    children: [
                      // Logo Section (above contact info)
                      Center(
                        child: Image.asset(
                          'assets/Logo.png',
                          height: size.height * 0.15,
                          fit: BoxFit.contain, // Maintain aspect ratio
                        ),
                      ),
                      const SizedBox(
                          height: 20), // Space between logo and contact info

                      // Contact Info Section
                      _buildContactRow(
                        icon: Icons.phone,
                        text: '${_controller.contactUs}',
                        actionText: 'Call Us',
                        onPressed: () => _launchPhone('01-5917785'),
                      ),
                      const Divider(),
                      _buildContactRow(
                        icon: Icons.email,
                        text: '${_controller.email}',
                        actionText: 'Send Email',
                        onPressed: openMail,
                      ),
                      const Divider(),
                      _buildContactRow(
                        icon: Icons.location_on,
                        text: '${_controller.address}',
                        actionText: 'View on Map',
                        onPressed: _launchMap,
                      ),
                    ],
                  ),
                  isLarge: true,
                ),

                const SizedBox(height: 30),

                // Business Hours Section
                _buildSectionCard(
                  context: context,
                  title: "Business Hours",
                  child: _buildStaticInfo(
                    content:
                        "Sunday - Friday: 9:00 AM - 6:00 PM\nSaturday: Closed",
                  ),
                ),

                const SizedBox(height: 20),

                // Support Section
                _buildSectionCard(
                  context: context,
                  title: "Support",
                  child: _buildStaticInfo(
                    content:
                        "If you face any issues, feel free to reach out through our support. We typically respond within 24 hours.",
                  ),
                ),

                const SizedBox(height: 20),

                // Follow Us Section
                _buildSectionCard(
                  context: context,
                  title: "Follow Us",
                  child: _buildStaticInfo(
                    content: "Stay connected for the latest updates:\n"
                        "Facebook: our_company\n"
                        "Instagram: @our_company",
                  ),
                ),

                const SizedBox(height: 30),

                const AnimatedTextWidget(text: 'We’re here to help you!'),
              ],
            ),
          );
        }
      }),
    );
  }

  // Section Container with Consistent Styling
  Widget _buildSectionCard({
    required BuildContext context,
    required String title,
    required Widget child,
    bool isLarge = false,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      margin: const EdgeInsets.only(bottom: 20), // Added bottom margin
      decoration: BoxDecoration(
        color: isLarge
            ? const Color.fromARGB(255, 255, 255, 255)
            : const Color.fromARGB(255, 255, 255, 255),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: const Color.fromARGB(220, 220, 220, 220).withOpacity(01),
            spreadRadius: 3,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 22, // Increased font size for title
              fontWeight: FontWeight.bold,
              color: Colors.teal,
            ),
          ),
          const SizedBox(height: 15),
          child,
        ],
      ),
    );
  }

  // Contact Row with Icons and Buttons
  Widget _buildContactRow({
    required IconData icon,
    required String text,
    required String actionText,
    required VoidCallback onPressed,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Icon(icon, color: Colors.teal, size: 30), // Slightly larger icon
        const SizedBox(width: 15),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
              letterSpacing: 0.5,
            ),
          ),
        ),
        const SizedBox(width: 10),
        ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color.fromARGB(240, 0, 131, 143),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.symmetric(
                horizontal: 15, vertical: 10), // More padding for the button
          ),
          child: Text(
            actionText,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  // Static Info Text (For Business Hours, Support, Follow Us)
  Widget _buildStaticInfo({required String content}) {
    return Text(
      content,
      style: const TextStyle(
        fontSize: 16, // Adjusted font size for better readability
        fontWeight: FontWeight.w600, // Changed to bold
        color: Color.fromARGB(240, 0, 131, 143), // Updated color to match theme
        height: 1.5,
      ),
    );
  }
}

class AnimatedTextWidget extends StatefulWidget {
  final String text;

  const AnimatedTextWidget({super.key, required this.text});

  @override
  _AnimatedTextWidgetState createState() => _AnimatedTextWidgetState();
}

class _AnimatedTextWidgetState extends State<AnimatedTextWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller =
        AnimationController(vsync: this, duration: const Duration(seconds: 1));
    _animation = Tween(begin: 0.0, end: 1.0).animate(_controller);
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _animation,
      child: Text(
        widget.text,
        style: const TextStyle(
          color: Colors.teal,
          fontSize: 20, // Increased font size for emphasis
          fontWeight: FontWeight.bold,
          letterSpacing: 0.5,
        ),
      ),
    );
  }
}
