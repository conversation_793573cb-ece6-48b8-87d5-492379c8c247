# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Src\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\Desktop\\SmartSewa\\windows" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Src\\flutter"
  "PROJECT_DIR=C:\\Users\\<USER>\\Desktop\\SmartSewa\\windows"
  "FLUTTER_ROOT=C:\\Src\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\Desktop\\SmartSewa\\windows\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\Desktop\\SmartSewa\\windows"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\Desktop\\SmartSewa\\windows\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC9jZjdhOWQwODAwZjJhNWRhMTY2ZGJlMGViOWZiMjQ3NjAxODI2OWIxLw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\Desktop\\SmartSewa\\windows\\.dart_tool\\package_config.json"
)
