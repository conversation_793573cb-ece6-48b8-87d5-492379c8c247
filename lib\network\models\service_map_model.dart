import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/views/utils.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

class ServiceMapModel {
  int id;
  String fullName;
  String? picture;
  String latitude;
  String longitude;
  String? mobileNumber;
  String? serviceProvided;
  double? averageRating;
  int? noOfRatingReceived;
  double? distance;

  String? email;

  ServiceMapModel({
    required this.id,
    required this.fullName,
    required this.picture,
    required this.latitude,
    required this.mobileNumber,
    required this.longitude,
    required this.email,
    required this.serviceProvided,
    required this.averageRating,
    required this.noOfRatingReceived,
    required this.distance,
  });

  factory ServiceMapModel.fromJson(Map<String, dynamic> json) {
    return ServiceMapModel(
        id: json['id'],
        fullName: json['fullName'],
        picture: json['picture'],
        serviceProvided: "all",
        // latitude: json['latitude'],
        // longitude: json['longitude'],
        latitude: (json['latitude']).toString().split(',')[0],
        longitude: (json['latitude']).toString().split(',')[1],
        mobileNumber: json['mobileNumber'],
        email: json['email'],
        averageRating: json['average_rating'],
        noOfRatingReceived: json['no_of_rating_received'], distance: null);
  }

  get location => null;
}

class Comment {
  final int ratingUserId;
  final int userId;
  final double score;
  final String comment;
  final String ratingFullName;
  final String date;

  Comment({
    required this.ratingUserId,
    required this.userId,
    required this.score,
    required this.comment,
    required this.ratingFullName,
    required this.date,
  });

  // Factory constructor to create a Comment object from JSON
  factory Comment.fromJson(Map<String, dynamic> json) {
    return Comment(
      ratingUserId: json['ratingUserid'],
      userId: json['userId'],
      score: (json['score'] as num).toDouble(),
      comment: json['comment'],
      ratingFullName: json['ratingFullName'],
      date: json['date'],
    );
  }

  // Static method to parse list of comments from JSON list
  static List<Comment> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => Comment.fromJson(json)).toList();
  }
}

Future<List<Comment>> fetchComments(int userId, int page, int size) async {
  final url = Uri.parse(
      '$baseUrl/api/v1/rating/comments/$userId?page=$page&size=$size');
  SharedPreferences pref = await SharedPreferences.getInstance();
  String? token = pref.getString('token');

  try {
    final response = await http.get(url, headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    });

    if (response.statusCode == 200) {
      // Decode the response body to get the list of comments
      List<dynamic> responseData = jsonDecode(response.body);
      consolelog(response.body);

      // Parse the list of comments
      List<Comment> comments = Comment.fromJsonList(responseData);
      return comments;
    } else {
      print('Failed to fetch comments: ${response.statusCode}');
      return [];
    }
  } catch (e) {
    print('Error fetching comments: $e');
    return [];
  }
}
