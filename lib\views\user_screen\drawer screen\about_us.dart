import 'package:flutter/material.dart';
import '../../widgets/my_appbar.dart';

class AboutUS extends StatefulWidget {
  const AboutUS({super.key});

  @override
  State<AboutUS> createState() => _AboutUSState();
}

class _AboutUSState extends State<AboutUS> {
  bool _isEnglish = true;

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double textScaleFactor = screenWidth < 600 ? 1 : 1.2;

    // Content based on language selection
    final companyName = _isEnglish
        ? 'Smart Sewa Solutions Nepal Pvt. Ltd'
        : 'स्मार्ट सेवा सोलुसन्स नेपाल प्रा. लि.';
    final description = _isEnglish
        ? 'Smart Sewa is an intermediary service-based application that offers various services via Google Map-based tracking to connect clients with skilled professionals. Our app is designed for web and Android systems, with iOS in development. Services include plumbing, electrical work, cleaning, masonry, carpeting, painting, and more—essential for daily household activities. '
            'The application enables skilled workers to register as service providers, allowing users to easily connect with them. Smart Sewa emphasizes convenience, efficiency, and reliability, simplifying life by linking you with dependable service providers for all your household needs.'
        : "स्मार्ट सेवा एक मध्यस्थ सेवा-आधारित एप्लिकेशन हो, जसले गुगल म्यापमा आधारित ट्र्याकिङ प्रणालीमार्फत विभिन्न घरेलु सेवा प्रदान गर्दछ र ग्राहकहरूलाई दक्ष तथा अनुभवी सेवा प्रदायकहरूसँग जोड्दछ। हाम्रो एप्लिकेशन वेब, एन्ड्रोइड तथा आइओएस प्रयोगकर्ताहरूका लागि उपलब्ध छ। यसमा प्लम्बिङ, इलेक्ट्रिकल कार्य, सरसफाई, घर निर्माण, कार्पेटिङ, रङरोगन लगायत दैनिक जीवनमा आवश्यक पर्ने अन्य सेवाहरू समावेश छन्। यस एप्लिकेशनमार्फत योग्य कामदारहरू सेवा प्रदायकको रूपमा सजिलै दर्ता हुन सक्ने सुविधा उपलब्ध गराइएको छ, जसले प्रयोगकर्ताहरूलाई छिटो र सहज रूपमा आवश्यक सेवा प्राप्त गर्न सहयोग पुर्‍याउँछ। स्मार्ट सेवाले तपाईंलाई भरपर्दो सेवा प्रदायकहरूसँग जोडेर सेवा प्राप्त गर्ने प्रक्रियालाई सरल, सुविधाजनक र विश्वसनीय बनाउने लक्ष्य राख्दछ।";
    final commitmentTitle =
        _isEnglish ? 'Our Commitment to You' : 'तपाईंप्रति हाम्रो प्रतिबद्धता';
    final commitmentText = _isEnglish
        ? 'We are dedicated to providing exceptional customer service, with our support staff always available to assist you with any questions or concerns. The Smart Sewa application is designed to give users maximum flexibility in their interactions with service providers, allowing for free price negotiation without any hidden fees. '
            'We are passionate about improving lives by streamlining domestic chores and freeing up time for what truly matters. Join our community today and experience the ease and effectiveness of our home service application.'
        : 'हामी उत्कृष्ट ग्राहक सेवा प्रदान गर्न पूर्ण रूपले प्रतिबद्ध छौं, हाम्रो समर्थन समूह तपाईंको कुनै पनि प्रश्न वा जिज्ञासाको मद्दत गर्न सधैं उपलब्ध छौं। स्मार्ट सेवा  एप्लिकेशनले प्रयोगकर्ताहरूलाई सेवा प्रदायकहरूसँगको अन्तरक्रियामा अधिकतम लचिलोपन दिन डिजाइन गरिएको छ, कुनै पनि अतिरिक्त शुल्क बिना मूल्य सम्झौताको अनुमति दिन्छ। '
            'हामी घरायसी कामहरूलाई सुव्यवस्थित गरेर र समय बचाएर जीवन सुधार्न उत्साहित छौं। आज नै हाम्रो समुदायमा सामेल हुनुहोस् र हाम्रो घरेलु सेवा उपलब्ध गराउने एप्लिकेशनको सहजता र प्रभावकारिता अनुभव गर्नुहोस्।';
    final closingStatement = _isEnglish
        ? 'Thank you for choosing Smart Sewa Solutions Nepal Pvt. Ltd.'
        : 'स्मार्ट सेवा सोलुसन्स नेपाल प्रा. लि. चयन गर्नुभएकोमा धन्यवाद।';

    return Scaffold(
      backgroundColor: Colors.white,
      appBar:
          myAppbar(context, true, _isEnglish ? "About Us" : "हाम्रो बारेमा"),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: screenWidth * 0.05, vertical: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Language Selection Buttons
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    _buildLanguageButton(
                      flag: 'assets/usa_flag.gif',
                      code: 'EN',
                      isSelected: _isEnglish,
                      onTap: () => setState(() => _isEnglish = true),
                    ),
                    const SizedBox(width: 8),
                    _buildLanguageButton(
                      flag: 'assets/flag_np.png',
                      code: 'NP',
                      isSelected: !_isEnglish,
                      onTap: () => setState(() => _isEnglish = false),
                    ),
                  ],
                ),
              ),

              // Company Name Header
              Text(
                companyName,
                style: TextStyle(
                  fontSize: 20 * textScaleFactor,
                  color: const Color.fromARGB(240, 0, 131, 143),
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Montserrat',
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),

              // Description Section
              Card(
                color: Colors.white,
                elevation: 16,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(18.0),
                  child: Text(
                    description,
                    textAlign: TextAlign.justify,
                    style: TextStyle(
                      fontSize: 16 * textScaleFactor,
                      height: 1.4,
                      color: Colors.black87,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Commitment Section Title
              Text(
                commitmentTitle,
                style: TextStyle(
                  fontSize: 20 * textScaleFactor,
                  fontWeight: FontWeight.bold,
                  color: const Color.fromARGB(240, 0, 131, 143),
                  fontFamily: 'Montserrat',
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),

              // Commitment Section
              Card(
                color: Colors.white,
                elevation: 8,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    commitmentText,
                    textAlign: TextAlign.justify,
                    style: TextStyle(
                      fontSize: 16 * textScaleFactor,
                      height: 1.3,
                      color: Colors.black87,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Closing Statement
              Text(
                closingStatement,
                style: TextStyle(
                  fontSize: 18 * textScaleFactor,
                  fontWeight: FontWeight.bold,
                  color: const Color.fromARGB(240, 0, 131, 143),
                  fontFamily: 'Montserrat',
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 30),

              // Contact Section
              Column(
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.phone,
                          color: const Color.fromARGB(240, 0, 131, 143),
                          size: 28 * textScaleFactor),
                      const SizedBox(width: 10),
                      Text(
                        '01-5917785',
                        style: TextStyle(
                          fontSize: 16 * textScaleFactor,
                          fontFamily: 'Montserrat',
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.email,
                          color: const Color.fromARGB(240, 0, 131, 143),
                          size: 28 * textScaleFactor),
                      const SizedBox(width: 10),
                      Text(
                        '<EMAIL>',
                        style: TextStyle(
                          fontSize: 16 * textScaleFactor,
                          fontFamily: 'Montserrat',
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageButton({
    required String flag,
    required String code,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? const Color(0xFF00838F) : Colors.grey,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          children: [
            Image.asset(
              flag,
              width: 20,
              height: 15,
            ),
            const SizedBox(width: 4),
            Text(code),
          ],
        ),
      ),
    );
  }
}
