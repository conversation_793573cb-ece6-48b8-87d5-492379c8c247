// import 'dart:io';

// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:get/get.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smartsewa/core/development/console.dart';
// import 'package:smartsewa/network/services/authServices/auth_controller.dart';
// import 'package:smartsewa/views/serviceProviderScreen/serviceProfile/service_edit_screen.dart';
// import 'package:smartsewa/views/widgets/custom_toasts.dart';
// import 'package:smartsewa/views/widgets/my_appbar.dart';
// import '../../../network/services/userdetails/current_user_controller.dart';
// import '../../widgets/custom_dialogs.dart';
// import 'package:smartsewa/views/utils.dart';

// class ServiceProfile extends StatefulWidget {
//   const ServiceProfile({super.key});

//   @override
//   State<ServiceProfile> createState() => _ServiceProfileState();
// }

// class _ServiceProfileState extends State<ServiceProfile> {
//   final controller = Get.put(CurrentUserController());
//   final authController = Get.put(AuthController());
//   File? _profileimage;
//   String? token;

//   @override
//   void initState() {
//     getToken();
//     super.initState();
//   }

//   void getToken() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     String? apptoken = prefs.getString("token");
//     setState(() {
//       token = apptoken;
//     });
//   }

//   Future pickprofile(ImageSource source) async {
//     final imagePicker = ImagePicker();
//     final pickedImage = await imagePicker.pickImage(source: source);

//     if (pickedImage != null) {
//       setState(() {
//         _profileimage = File(pickedImage.path);
//         consolelog('picked image: $_profileimage');
//       });
//     } else {
//       consolelog('no profile picture selected');
//     }
//   }

//   void selectSource() {
//     showDialog(
//       context: context,
//       builder: (context) {
//         return AlertDialog(
//           title: const Center(
//             child: Text(
//               "Select",
//               style: TextStyle(fontSize: 21, color: Colors.black),
//             ),
//           ),
//           content: Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               ElevatedButton(
//                 style: ButtonStyle(
//                   backgroundColor: MaterialStateProperty.all(
//                     Theme.of(context).primaryColor,
//                   ),
//                 ),
//                 onPressed: () {
//                   pickprofile(ImageSource.gallery);
//                 },
//                 child: const Row(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     Icon(Icons.image_outlined),
//                     SizedBox(width: 1),
//                     Text("Gallery"),
//                   ],
//                 ),
//               ),
//               const SizedBox(width: 10),
//               ElevatedButton(
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: Theme.of(context).primaryColor,
//                 ),
//                 onPressed: () {
//                   pickprofile(ImageSource.camera);
//                 },
//                 child: const Row(
//                   children: [
//                     Icon(Icons.camera_alt_outlined),
//                     SizedBox(width: 1),
//                     Text("Camera"),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//           actions: [
//             TextButton(
//               onPressed: () {
//                 Navigator.pop(context);
//               },
//               child: const Text("Ok"),
//             ),
//           ],
//         );
//       },
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     Size size = MediaQuery.of(context).size;

//     return Scaffold(
//       appBar: myAppbar(context, true, "Service Provider Profile"),
//       body: Obx(() {
//         if (controller.isLoading.value) {
//           return const Center(
//             child: CircularProgressIndicator(),
//           );
//         } else {
//           return Container(
//             width: double.infinity,
//             decoration: const BoxDecoration(
//               borderRadius: BorderRadius.only(
//                 topRight: Radius.circular(30),
//                 topLeft: Radius.circular(30),
//               ),
//               color: Colors.white,
//             ),
//             child: Padding(
//               padding: EdgeInsets.all(size.aspectRatio * 50),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Align(
//                     alignment: Alignment.centerRight,
//                     child: InkWell(
//                       onTap: () {
//                         Get.to(() => ServiceProviderSettingPage());
//                       },
//                       child: Container(
//                         padding: const EdgeInsets.all(8),
//                         decoration: BoxDecoration(
//                           color: Theme.of(context).primaryColor,
//                           borderRadius: BorderRadius.circular(8),
//                           boxShadow: [
//                             BoxShadow(
//                               color: Colors.grey.withOpacity(0.5),
//                               spreadRadius: 2,
//                               blurRadius: 5,
//                               offset:
//                                   Offset(0, 3), // changes position of shadow
//                             ),
//                           ],
//                         ),
//                         child: const Text(
//                           "Edit Profile",
//                           style: TextStyle(fontSize: 12, color: Colors.white),
//                         ),
//                       ),
//                     ),
//                   ),
//                   const SizedBox(height: 20),

//                   Center(
//                     child: InkWell(
//                       onTap: selectSource,
//                       child: Stack(
//                         children: [
//                           _profileimage != null
//                               ? CircleAvatar(
//                                   radius: 50,
//                                   backgroundImage: FileImage(_profileimage!),
//                                   child: Container(
//                                     decoration: BoxDecoration(
//                                       shape: BoxShape.circle,
//                                       gradient: LinearGradient(
//                                         colors: [Colors.blue, Colors.green],
//                                         begin: Alignment.topLeft,
//                                         end: Alignment.bottomRight,
//                                       ),
//                                     ),
//                                   ),
//                                 )
//                               : CircleAvatar(
//                                   backgroundColor:
//                                       Theme.of(context).primaryColor,
//                                   radius: 50,
//                                   backgroundImage: NetworkImage(
//                                     "http://$baseUrl/api/allimg/image/${controller.currentUserData.value.picture}",
//                                     headers: {
//                                       'Authorization': "Bearer $token",
//                                     },
//                                   ),
//                                 ),
//                           const Positioned(
//                             bottom: 0,
//                             right: 0,
//                             child: Icon(Icons.camera_alt_outlined),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                   const SizedBox(height: 20),

//                   Center(
//                     child: ElevatedButton(
//                       style: ElevatedButton.styleFrom(
//                         backgroundColor: _profileimage != null
//                             ? Theme.of(context).primaryColor
//                             : const Color.fromARGB(240, 0, 131, 143),
//                         elevation: 5,
//                         shape: RoundedRectangleBorder(
//                           borderRadius: BorderRadius.circular(8),
//                         ),
//                       ),
//                       onPressed: () {
//                         if (_profileimage != null) {
//                           CustomDialogs.fullLoadingDialog(
//                               context: context, data: "Image Uploading...");
//                           authController.uploadProfile(
//                             _profileimage,
//                             isFromServiceScreen: true,
//                           );
//                         } else {
//                           errorToast(msg: "Please select the image to upload");
//                         }
//                       },
//                       child: const Text(
//                         'Save',
//                         style: TextStyle(fontSize: 14, color: Colors.white),
//                       ),
//                     ),
//                   ),
//                   const SizedBox(height: 30), // Increased spacing

//                   const Text(
//                     'User Details:',
//                     style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
//                   ),
//                   const SizedBox(height: 10), // Space between title and items

//                   buildItem(
//                     " ${controller.currentUserData.value.fullName.toString()}",
//                     Icons.person,
//                   ),
//                   buildItem(
//                     controller.currentUserData.value.mobileNumber.toString(),
//                     Icons.phone,
//                   ),
//                   buildItem(
//                     controller.currentUserData.value.address.toString(),
//                     Icons.home,
//                   ),
//                 ],
//               ),
//             ),
//           );
//         }
//       }),
//     );
//   }

//   Widget buildItem(String name, dynamic icon) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(vertical: 5.0), // Vertical spacing
//       child: Row(
//         children: [
//           Icon(
//             icon,
//             color: const Color.fromARGB(240, 0, 131, 143),
//             size: 20,
//           ),
//           const SizedBox(width: 10),
//           Expanded(
//             child: Text(
//               name,
//               style: const TextStyle(
//                 fontFamily: 'hello',
//                 fontSize: 15,
//                 fontWeight: FontWeight.bold,
//                 color: Colors.black87,
//                 decoration: TextDecoration.none,
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

// import 'dart:io';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smartsewa/network/services/authServices/auth_controller.dart';
// import 'package:smartsewa/network/services/userdetails/current_user_controller.dart';
// import 'package:smartsewa/views/serviceProviderScreen/serviceProfile/service_edit_screen.dart';
// import '../../utils.dart';
// import '../../widgets/custom_dialogs.dart';
// import '../../widgets/my_appbar.dart';

// class ServiceProfile extends StatefulWidget {
//   const ServiceProfile({Key? key}) : super(key: key);

//   @override
//   State<ServiceProfile> createState() => _ServiceProfileState();
// }

// class _ServiceProfileState extends State<ServiceProfile> {
//   final CurrentUserController controller = Get.put(CurrentUserController());
//   final AuthController authController = Get.put(AuthController());
//   File? _profileImage;
//   String? _token;
//   bool _isServiceProviderActive = false;

//   @override
//   void initState() {
//     super.initState();
//     _loadToken();
//     _loadServiceProviderStatus();
//   }

//   Future<void> _loadToken() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     setState(() {
//       _token = prefs.getString("token");
//     });
//   }

//   Future<void> _loadServiceProviderStatus() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     setState(() {
//       _isServiceProviderActive =
//           prefs.getBool("isServiceProviderActive") ?? false;
//     });
//   }

//   Future<void> _saveServiceProviderStatus(bool status) async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     await prefs.setBool("isServiceProviderActive", status);
//   }

//   Future<void> _pickProfileImage(ImageSource source) async {
//     final imagePicker = ImagePicker();
//     final pickedImage = await imagePicker.pickImage(source: source);

//     if (pickedImage != null) {
//       setState(() {
//         _profileImage = File(pickedImage.path);
//       });
//     }
//   }

//   void _selectImageSource() {
//     showDialog(
//       context: context,
//       builder: (context) {
//         return AlertDialog(
//           title: const Center(child: Text("Select Image")),
//           content: Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               ElevatedButton(
//                 onPressed: () {
//                   _pickProfileImage(ImageSource.gallery);
//                   Navigator.pop(context);
//                 },
//                 child: const Text("Gallery"),
//               ),
//               const SizedBox(width: 10),
//               ElevatedButton(
//                 onPressed: () {
//                   _pickProfileImage(ImageSource.camera);
//                   Navigator.pop(context);
//                 },
//                 child: const Text("Camera"),
//               ),
//             ],
//           ),
//         );
//       },
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: myAppbar(context, true, "Service Provider Profile"),
//       body: SafeArea(
//         child: Obx(() {
//           if (controller.isLoading.value) {
//             return const Center(child: CircularProgressIndicator());
//           } else {
//             return SingleChildScrollView(
//               padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//                   _profileSection(),
//                   const SizedBox(height: 30),
//                   _detailsSection(),
//                 ],
//               ),
//             );
//           }
//         }),
//       ),
//     );
//   }

//   Widget _profileSection() {
//     return Container(
//       padding: const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Color.fromARGB(200, 0, 131, 143),
//         borderRadius: BorderRadius.circular(36),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.grey.withOpacity(0.3),
//             spreadRadius: 3,
//             blurRadius: 7,
//             offset: const Offset(0, 3),
//           ),
//         ],
//       ),
//       child: Column(
//         children: [
//           Stack(
//             alignment: Alignment.center,
//             children: [
//               CircleAvatar(
//                 radius: 70,
//                 backgroundColor: Colors.white,
//                 backgroundImage: _profileImage != null
//                     ? FileImage(_profileImage!)
//                     : NetworkImage(
//                         "http://$baseUrl/api/allimg/image/${controller.currentUserData.value.picture}",
//                         headers: {'Authorization': "Bearer $_token"},
//                       ) as ImageProvider,
//               ),
//               Positioned(
//                 bottom: 30,
//                 right: 0,
//                 child: _profileImage == null
//                     ? InkWell(
//                         onTap: _selectImageSource,
//                         child: _iconContainer(Icons.camera_alt_outlined),
//                       )
//                     : ElevatedButton(
//                         onPressed: () {
//                           if (_profileImage != null) {
//                             CustomDialogs.fullLoadingDialog(
//                                 context: context, data: "Uploading Image...");
//                             authController.uploadProfile(_profileImage,
//                                 isFromServiceScreen: true);
//                           }
//                         },
//                         style: ElevatedButton.styleFrom(
//                           backgroundColor:
//                               const Color.fromARGB(240, 100, 131, 143),
//                           shape: RoundedRectangleBorder(
//                             borderRadius: BorderRadius.circular(15),
//                           ),
//                         ),
//                         child: const Text('Save'),
//                       ),
//               ),
//             ],
//           ),
//           const SizedBox(height: 16),
//           _profileName(),
//         ],
//       ),
//     );
//   }

//   Widget _iconContainer(IconData icon) {
//     return Container(
//       padding: const EdgeInsets.all(6),
//       decoration: BoxDecoration(
//         shape: BoxShape.circle,
//         color: const Color.fromARGB(255, 255, 255, 255),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.5),
//             blurRadius: 10,
//             offset: const Offset(2, 2),
//           ),
//         ],
//       ),
//       child: Icon(icon, color: Theme.of(context).primaryColor),
//     );
//   }

//   Widget _profileName() {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         Text(
//           controller.currentUserData.value.fullName.toString(),
//           style: const TextStyle(
//             fontSize: 24,
//             fontWeight: FontWeight.bold,
//             color: Color.fromARGB(255, 255, 255, 255),
//           ),
//         ),
//         const SizedBox(width: 25),
//         Container(
//           width: 36,
//           height: 36,
//           decoration: BoxDecoration(
//             color: const Color.fromARGB(
//                 255, 255, 255, 255), // Background color of the button
//             shape: BoxShape.circle, // Make it circular
//             boxShadow: [
//               BoxShadow(
//                 color: Colors.black.withOpacity(0.5), // Shadow color
//                 spreadRadius: 2, // Spread radius
//                 blurRadius: 10, // Blur radius
//                 offset: Offset(5, 3), // Offset of the shadow
//               ),
//             ],
//           ),
//           child: IconButton(
//             icon: Icon(Icons.edit, color: Color.fromARGB(240, 0, 131, 143)),
//             onPressed: () {
//               Get.to(() => ServiceProviderSettingPage());
//             },
//           ),
//         )
//       ],
//     );
//   }

//   Widget _detailsSection() {
//     return Container(
//       padding: const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(16),
//         boxShadow: [
//           BoxShadow(
//             color: const Color.fromRGBO(240, 240, 240, 1),
//             spreadRadius: 3,
//             blurRadius: 7,
//             offset: const Offset(0, 3),
//           ),
//         ],
//       ),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           const Text(
//             "User Details",
//             style: TextStyle(
//               fontSize: 22,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//           const Divider(height: 20, thickness: 1.5),
//           _buildDetailItem(
//               controller.currentUserData.value.mobileNumber.toString(),
//               Icons.phone),
//           _buildDetailItem(
//               controller.currentUserData.value.address.toString(), Icons.home),
//           _serviceProviderStatusToggle(),
//         ],
//       ),
//     );
//   }

//   Widget _buildDetailItem(String text, IconData icon) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(vertical: 10.0),
//       child: Row(
//         children: [
//           Icon(icon, color: Theme.of(context).primaryColor, size: 24),
//           const SizedBox(width: 16),
//           Expanded(
//             child: Text(
//               text,
//               style: const TextStyle(
//                 fontSize: 16,
//                 fontWeight: FontWeight.w600,
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _serviceProviderStatusToggle() {
//     return Column(
//       crossAxisAlignment:
//           CrossAxisAlignment.start, // Align children to the start
//       children: [
//         Row(
//           children: [
//             const Icon(
//               Icons.work,
//               color: Color.fromRGBO(0, 131, 143, 1),
//               size: 24,
//             ),
//             const SizedBox(width: 16),
//             Expanded(
//               child: Text(
//                 _isServiceProviderActive
//                     ? 'Status: Visible on Map'
//                     : 'Status: Hidden on Map',
//                 style: const TextStyle(
//                   fontSize: 16,
//                   fontWeight: FontWeight.w600,
//                   // Match color with icon
//                 ),
//               ),
//             ),
//             Container(
//               width: 60, // Adjust to desired width
//               height: 40, // Adjust to desired height
//               child: Switch(
//                 value: _isServiceProviderActive,
//                 onChanged: (value) {
//                   setState(() {
//                     _isServiceProviderActive = value;
//                   });
//                   _saveServiceProviderStatus(value);
//                 },
//                 activeColor: const Color.fromRGBO(0, 131, 143, 1),
//                 activeTrackColor: const Color.fromARGB(237, 223, 231, 231),
//                 inactiveThumbColor: const Color.fromARGB(255, 219, 90, 50),
//                 inactiveTrackColor: const Color.fromARGB(237, 223, 231, 231),
//               ),
//             ),
//           ],
//         ),
//         const SizedBox(height: 12), // Spacing between rows
//         Row(
//           children: [
//             const Icon(
//               Icons.calendar_today, // Choose an appropriate icon
//               color: Color.fromRGBO(0, 131, 143, 1), // Match icon color
//             ),
//             const SizedBox(width: 8), // Spacing between icon and text
//             Text(
//               '  Expiry Date:', // Example expiry date
//               style: const TextStyle(
//                 fontSize: 16,
//                 fontWeight: FontWeight.w600,
//                 // Match text color
//               ),
//             ),
//           ],
//         ),
//       ],
//     );
//   }
// }

// import 'dart:io';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smartsewa/network/services/authServices/auth_controller.dart';
// import 'package:smartsewa/network/services/userdetails/current_user_controller.dart';
// import 'package:smartsewa/views/serviceProviderScreen/serviceProfile/service_edit_screen.dart';
// import '../../utils.dart';
// import '../../widgets/custom_dialogs.dart';
// import '../../widgets/my_appbar.dart';

// class ServiceProfile extends StatefulWidget {
//   const ServiceProfile({Key? key}) : super(key: key);

//   @override
//   State<ServiceProfile> createState() => _ServiceProfileState();
// }

// class _ServiceProfileState extends State<ServiceProfile> {
//   final CurrentUserController controller = Get.put(CurrentUserController());
//   final AuthController authController = Get.put(AuthController());
//   File? _profileImage;
//   String? _token;
//   bool _isServiceProviderActive = false;

//   @override
//   void initState() {
//     super.initState();
//     _loadToken();
//     _loadServiceProviderStatus();
//   }

//   Future<void> _loadToken() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     setState(() {
//       _token = prefs.getString("token");
//     });
//   }

//   Future<void> _loadServiceProviderStatus() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     setState(() {
//       _isServiceProviderActive =
//           prefs.getBool("isServiceProviderActive") ?? false;
//     });
//   }

//   Future<void> _saveServiceProviderStatus(bool status) async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     await prefs.setBool("isServiceProviderActive", status);
//   }

//   Future<void> _pickProfileImage(ImageSource source) async {
//     final imagePicker = ImagePicker();
//     final pickedImage = await imagePicker.pickImage(source: source);

//     if (pickedImage != null) {
//       setState(() {
//         _profileImage = File(pickedImage.path);
//       });
//     }
//   }

//   void _selectImageSource() {
//     showDialog(
//       context: context,
//       builder: (context) {
//         return AlertDialog(
//           title: const Center(child: Text("Select Image")),
//           content: Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               ElevatedButton(
//                 onPressed: () {
//                   _pickProfileImage(ImageSource.gallery);
//                   Navigator.pop(context);
//                 },
//                 child: const Text("Gallery"),
//               ),
//               const SizedBox(width: 10),
//               ElevatedButton(
//                 onPressed: () {
//                   _pickProfileImage(ImageSource.camera);
//                   Navigator.pop(context);
//                 },
//                 child: const Text("Camera"),
//               ),
//             ],
//           ),
//         );
//       },
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: myAppbar(context, true, "Service Provider Profile"),
//       body: SafeArea(
//         child: Obx(() {
//           if (controller.isLoading.value) {
//             return const Center(child: CircularProgressIndicator());
//           } else {
//             return SingleChildScrollView(
//               padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//                   _profileSection(),
//                   const SizedBox(height: 30),
//                   _detailsSection(),
//                 ],
//               ),
//             );
//           }
//         }),
//       ),
//     );
//   }

//   Widget _profileSection() {
//     return Container(
//       padding: const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Color.fromARGB(200, 0, 131, 143),
//         borderRadius: BorderRadius.circular(36),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.grey.withOpacity(0.3),
//             spreadRadius: 3,
//             blurRadius: 7,
//             offset: const Offset(0, 3),
//           ),
//         ],
//       ),
//       child: Column(
//         children: [
//           Stack(
//             alignment: Alignment.center,
//             children: [
//               CircleAvatar(
//                 radius: 70,
//                 backgroundColor: Colors.white,
//                 backgroundImage: _profileImage != null
//                     ? FileImage(_profileImage!)
//                     : NetworkImage(
//                         "http://$baseUrl/api/allimg/image/${controller.currentUserData.value.picture}",
//                         headers: {'Authorization': "Bearer $_token"},
//                       ) as ImageProvider,
//               ),
//               Positioned(
//                 bottom: 30,
//                 right: 0,
//                 child: _profileImage == null
//                     ? InkWell(
//                         onTap: _selectImageSource,
//                         child: _iconContainer(Icons.camera_alt_outlined),
//                       )
//                     : ElevatedButton(
//                         onPressed: () {
//                           if (_profileImage != null) {
//                             CustomDialogs.fullLoadingDialog(
//                                 context: context, data: "Uploading Image...");
//                             authController.uploadProfile(_profileImage,
//                                 isFromServiceScreen: true);
//                           }
//                         },
//                         style: ElevatedButton.styleFrom(
//                           backgroundColor:
//                               const Color.fromARGB(240, 100, 131, 143),
//                           shape: RoundedRectangleBorder(
//                             borderRadius: BorderRadius.circular(15),
//                           ),
//                         ),
//                         child: const Text('Save'),
//                       ),
//               ),
//             ],
//           ),
//           const SizedBox(height: 16),
//           _profileName(),
//         ],
//       ),
//     );
//   }

//   Widget _iconContainer(IconData icon) {
//     return Container(
//       padding: const EdgeInsets.all(6),
//       decoration: BoxDecoration(
//         shape: BoxShape.circle,
//         color: const Color.fromARGB(255, 255, 255, 255),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.5),
//             blurRadius: 10,
//             offset: const Offset(2, 2),
//           ),
//         ],
//       ),
//       child: Icon(icon, color: Theme.of(context).primaryColor),
//     );
//   }

//   Widget _profileName() {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         Text(
//           controller.currentUserData.value.fullName.toString(),
//           style: const TextStyle(
//             fontSize: 24,
//             fontWeight: FontWeight.bold,
//             color: Color.fromARGB(255, 255, 255, 255),
//           ),
//         ),
//         const SizedBox(width: 25),
//         Container(
//           width: 36,
//           height: 36,
//           decoration: BoxDecoration(
//             color: const Color.fromARGB(
//                 255, 255, 255, 255), // Background color of the button
//             shape: BoxShape.circle, // Make it circular
//             boxShadow: [
//               BoxShadow(
//                 color: Colors.black.withOpacity(0.5), // Shadow color
//                 spreadRadius: 2, // Spread radius
//                 blurRadius: 10, // Blur radius
//                 offset: Offset(5, 3), // Offset of the shadow
//               ),
//             ],
//           ),
//           child: IconButton(
//             icon: Icon(Icons.edit, color: Color.fromARGB(240, 0, 131, 143)),
//             onPressed: () {
//               Get.to(() => ServiceProviderSettingPage());
//             },
//           ),
//         )
//       ],
//     );
//   }

//   Widget _detailsSection() {
//     return Container(
//       padding: const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(16),
//         boxShadow: [
//           BoxShadow(
//             color: const Color.fromRGBO(240, 240, 240, 1),
//             spreadRadius: 3,
//             blurRadius: 7,
//             offset: const Offset(0, 3),
//           ),
//         ],
//       ),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           const Text(
//             "User Details",
//             style: TextStyle(
//               fontSize: 22,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//           const Divider(height: 20, thickness: 1.5),
//           _buildDetailItem(
//             controller.currentUserData.value.mobileNumber.toString(),
//             Icons.phone,
//           ),
//           _buildDetailItem(
//             controller.currentUserData.value.address.toString(),
//             Icons.home,
//           ),
//           _serviceProviderStatusToggle(),
//         ],
//       ),
//     );
//   }

//   Widget _buildDetailItem(String text, IconData icon) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(vertical: 10.0),
//       child: Row(
//         children: [
//           Container(
//             decoration: BoxDecoration(
//               shape: BoxShape.circle,
//               color: Theme.of(context).primaryColor.withOpacity(0.1),
//             ),
//             padding: const EdgeInsets.all(8),
//             child: Icon(icon, color: Theme.of(context).primaryColor, size: 24),
//           ),
//           const SizedBox(width: 16),
//           Expanded(
//             child: Text(
//               text,
//               style: const TextStyle(
//                 fontSize: 16,
//                 fontWeight: FontWeight.w600,
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _serviceProviderStatusToggle() {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Row(
//           children: [
//             Container(
//               decoration: BoxDecoration(
//                 shape: BoxShape.circle,
//                 color: Theme.of(context).primaryColor.withOpacity(0.1),
//               ),
//               padding: const EdgeInsets.all(8),
//               child: const Icon(
//                 Icons.work,
//                 color: Color.fromRGBO(0, 131, 143, 1),
//                 size: 24,
//               ),
//             ),
//             const SizedBox(width: 16),
//             Expanded(
//               child: Text(
//                 _isServiceProviderActive
//                     ? 'Status: Visible on Map'
//                     : 'Status: Hidden on Map',
//                 style: const TextStyle(
//                   fontSize: 16,
//                   fontWeight: FontWeight.w600,
//                 ),
//               ),
//             ),
//             Container(
//               width: 60,
//               height: 40,
//               child: Switch(
//                 value: _isServiceProviderActive,
//                 onChanged: (value) {
//                   setState(() {
//                     _isServiceProviderActive = value;
//                   });
//                   _saveServiceProviderStatus(value);
//                 },
//                 activeColor: const Color.fromRGBO(0, 131, 143, 1),
//                 activeTrackColor: const Color.fromARGB(237, 223, 231, 231),
//                 inactiveThumbColor: const Color.fromARGB(255, 219, 90, 50),
//                 inactiveTrackColor: const Color.fromARGB(237, 223, 231, 231),
//               ),
//             ),
//           ],
//         ),
//         const SizedBox(height: 12),
//         Row(
//           children: [
//             Container(
//               decoration: BoxDecoration(
//                 shape: BoxShape.circle,
//                 color: Theme.of(context).primaryColor.withOpacity(0.1),
//               ),
//               padding: const EdgeInsets.all(8),
//               child: const Icon(
//                 Icons.calendar_today,
//                 color: Color.fromRGBO(0, 131, 143, 1),
//               ),
//             ),
//             const SizedBox(width: 8),
//             const Text(
//               '  Expiry Date:',
//               style: TextStyle(
//                 fontSize: 16,
//                 fontWeight: FontWeight.w600,
//               ),
//             ),
//           ],
//         ),
//       ],
//     );
//   }
// }

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartsewa/network/services/authServices/auth_controller.dart';
import 'package:smartsewa/network/services/userdetails/current_user_controller.dart';
import 'package:smartsewa/views/serviceProviderScreen/serviceProfile/service_edit_screen.dart';
import '../../utils.dart';
import '../../widgets/custom_dialogs.dart';
import '../../widgets/my_appbar.dart';

class ServiceProfile extends StatefulWidget {
  const ServiceProfile({super.key});

  @override
  State<ServiceProfile> createState() => _ServiceProfileState();
}

class _ServiceProfileState extends State<ServiceProfile> {
  final CurrentUserController controller = Get.put(CurrentUserController());
  final AuthController authController = Get.put(AuthController());
  File? _profileImage;
  String? _token;
  bool _isServiceProviderActive = false;

  @override
  void initState() {
    super.initState();
    _loadToken();
    _loadServiceProviderStatus();
  }

  Future<void> _loadToken() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      _token = prefs.getString("token");
    });
  }

  Future<void> _loadServiceProviderStatus() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      _isServiceProviderActive =
          prefs.getBool("isServiceProviderActive") ?? false;
    });
  }

  Future<void> _saveServiceProviderStatus(bool status) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool("isServiceProviderActive", status);
  }

  Future<void> _pickProfileImage(ImageSource source) async {
    final imagePicker = ImagePicker();
    final pickedImage = await imagePicker.pickImage(source: source);

    if (pickedImage != null) {
      setState(() {
        _profileImage = File(pickedImage.path);
      });
    }
  }

  void _selectImageSource() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Center(child: Text("Select Image")),
          content: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: () {
                  _pickProfileImage(ImageSource.gallery);
                  Navigator.pop(context);
                },
                child: const Text("Gallery"),
              ),
              const SizedBox(width: 10),
              ElevatedButton(
                onPressed: () {
                  _pickProfileImage(ImageSource.camera);
                  Navigator.pop(context);
                },
                child: const Text("Camera"),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get screen size for responsiveness
    Size size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: myAppbar(context, true, "Service Provider Profile"),
      body: SafeArea(
        child: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          } else {
            return SingleChildScrollView(
              padding: EdgeInsets.symmetric(
                vertical: size.height * 0.02,
                horizontal: size.width * 0.04,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  _profileSection(size),
                  SizedBox(height: size.height * 0.04),
                  _detailsSection(size),
                ],
              ),
            );
          }
        }),
      ),
    );
  }

  Widget _profileSection(Size size) {
    return Container(
      padding: EdgeInsets.all(size.width * 0.04),
      decoration: BoxDecoration(
        color: const Color.fromARGB(200, 0, 131, 143),
        borderRadius: BorderRadius.circular(36),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 3,
            blurRadius: 7,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              CircleAvatar(
                radius: size.width * 0.18,
                backgroundColor: Colors.white,
                backgroundImage: _profileImage != null
                    ? FileImage(_profileImage!)
                    : NetworkImage(
                        "http://$baseUrl/api/allimg/image/${controller.currentUserData.value.picture}",
                        headers: {'Authorization': "Bearer $_token"},
                      ) as ImageProvider,
              ),
              Positioned(
                bottom: size.height * 0.04,
                right: 0,
                child: _profileImage == null
                    ? InkWell(
                        onTap: _selectImageSource,
                        child: _iconContainer(Icons.camera_alt_outlined),
                      )
                    : ElevatedButton(
                        onPressed: () {
                          if (_profileImage != null) {
                            CustomDialogs.fullLoadingDialog(
                                context: context, data: "Uploading Image...");
                            authController.uploadProfile(_profileImage,
                                isFromServiceScreen: true);
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              const Color.fromARGB(240, 100, 131, 143),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                        ),
                        child: const Text('Save'),
                      ),
              ),
            ],
          ),
          SizedBox(height: size.height * 0.02),
          _profileName(),
        ],
      ),
    );
  }

  Widget _iconContainer(IconData icon) {
    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: const Color.fromARGB(255, 255, 255, 255),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.5),
            blurRadius: 10,
            offset: const Offset(2, 2),
          ),
        ],
      ),
      child: Icon(icon, color: Theme.of(context).primaryColor),
    );
  }

  Widget _profileName() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          controller.currentUserData.value.fullName.toString(),
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Color.fromARGB(255, 255, 255, 255),
          ),
        ),
        const SizedBox(width: 25),
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: const Color.fromARGB(
                255, 255, 255, 255), // Background color of the button
            shape: BoxShape.circle, // Make it circular
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.5), // Shadow color
                spreadRadius: 2, // Spread radius
                blurRadius: 10, // Blur radius
                offset: const Offset(5, 3), // Offset of the shadow
              ),
            ],
          ),
          child: IconButton(
            icon: const Icon(Icons.edit, color: Color.fromARGB(240, 0, 131, 143)),
            onPressed: () {
              Get.to(() => ServiceProviderSettingPage());
            },
          ),
        )
      ],
    );
  }

  Widget _detailsSection(Size size) {
    return Container(
      padding: EdgeInsets.all(size.width * 0.04),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [
          BoxShadow(
            color: Color.fromRGBO(240, 240, 240, 1),
            spreadRadius: 3,
            blurRadius: 7,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "User Details",
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Divider(height: 20, thickness: 1.5),
          _buildDetailItem(
            controller.currentUserData.value.mobileNumber.toString(),
            Icons.phone,
          ),
          _buildDetailItem(
            controller.currentUserData.value.address.toString(),
            Icons.home,
          ),
          _serviceProviderStatusToggle(size),
        ],
      ),
    );
  }

  Widget _buildDetailItem(String text, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Theme.of(context).primaryColor.withOpacity(0.1),
            ),
            padding: const EdgeInsets.all(8),
            child: Icon(icon, color: Theme.of(context).primaryColor, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _serviceProviderStatusToggle(Size size) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).primaryColor.withOpacity(0.1),
              ),
              padding: const EdgeInsets.all(8),
              child: const Icon(
                Icons.work,
                color: Color.fromRGBO(0, 131, 143, 1),
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                _isServiceProviderActive
                    ? 'Status: Visible on Map'
                    : 'Status: Hidden on Map',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            SizedBox(
              width: size.width * 0.15,
              height: size.height * 0.06,
              child: Switch(
                value: _isServiceProviderActive,
                onChanged: (value) {
                  setState(() {
                    _isServiceProviderActive = value;
                  });
                  _saveServiceProviderStatus(value);
                },
                activeColor: const Color.fromRGBO(0, 131, 143, 1),
                activeTrackColor: const Color.fromARGB(237, 223, 231, 231),
                inactiveThumbColor: const Color.fromARGB(255, 219, 90, 50),
                inactiveTrackColor: const Color.fromARGB(237, 223, 231, 231),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).primaryColor.withOpacity(0.1),
              ),
              padding: const EdgeInsets.all(8),
              child: const Icon(
                Icons.calendar_today,
                color: Color.fromRGBO(0, 131, 143, 1),
              ),
            ),
            const SizedBox(width: 8),
            const Text(
              '  Expiry Date:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
