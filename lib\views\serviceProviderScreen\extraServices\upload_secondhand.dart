import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:smartsewa/core/dimension.dart';
import 'package:smartsewa/core/route_navigation.dart';
import 'package:smartsewa/core/states.dart';
import 'package:smartsewa/network/models/market_model.dart';
import 'package:smartsewa/network/services/exraServices/market_controller.dart';
import 'package:smartsewa/views/widgets/custom_toasts.dart';
import '../../../core/development/console.dart';
import '../../widgets/buttons/app_buttons.dart';
import '../../widgets/custom_text_form_field.dart';
import '../../widgets/my_appbar.dart';

class UploadSecondHand extends StatefulWidget {
  final bool? isFromServiceScreen;
  final bool? isFromEdit;
  final MarketPlaceResponseModel? market;
  const UploadSecondHand(
      {super.key, this.isFromServiceScreen, this.isFromEdit, this.market});

  @override
  State<UploadSecondHand> createState() => _UploadSecondHandState();
}

class _UploadSecondHandState extends State<UploadSecondHand> {
  final marketController = Get.put(MarketController());

  final titleController = TextEditingController();
  final descriptionController = TextEditingController();
  final priceController = TextEditingController();
  final floorController = TextEditingController();
  final landAreaController = TextEditingController();
  final roadSizeController = TextEditingController();
  final contactPersonController = TextEditingController();

  File? pickedMarketImage;
  String? selectedCategory;
  String? selectedLocation;
  String? selectedRoadType;
  String? selectedWaterSupply;
  String? selectedParking;
  String? selectedNegotiable;

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return WillPopScope(
      onWillPop: () async {
        marketController.clearValue();
        selectedDeliveryMarketPlace.value = null;
        selectedNegotiableMarketPlace.value = null;
        selectedWarrantyMarketPlace.value = null;
        return true;
      },
      child: Scaffold(
        appBar: myAppbar(context, true, "Add New Post"),
        body: Padding(
          padding: screenLeftRightPadding,
          child: SingleChildScrollView(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: const Color.fromRGBO(0, 131, 143, 1), // Border color
                  width: 2, // Border width
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 5,
                    blurRadius: 7,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Form(
                key: marketController.marketFormState,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    vSizedBox2,
                    _buildDropdownField<String>(
                      value: selectedCategory,
                      label: 'Select Category',
                      items: const [
                        'Room on rent',
                        'Flat on rent',
                        'House on rent',
                        'Land on rent',
                        'Flat on sell',
                        'House on sell',
                        'Land on sell',
                        'Business on sell'
                      ],
                      onChanged: (value) {
                        setState(() {
                          selectedCategory = value;
                        });
                      },
                      validator: (value) =>
                          value == null ? 'Please select a category' : null,
                      icon: Icons.category,
                    ),
                    vSizedBox2,
                    _buildInputField(
                      controller: titleController,
                      label: 'Title',
                      icon: Icons.title,
                      validator: (value) => value == null || value.isEmpty
                          ? 'Please enter a title'
                          : null,
                    ),
                    vSizedBox2,
                    _buildDropdownField<String>(
                      value: selectedLocation,
                      label: 'Location',
                      items: const ['Kathmandu', 'Bhaktapur', 'Lalitpur'],
                      onChanged: (value) {
                        setState(() {
                          selectedLocation = value;
                        });
                      },
                      validator: (value) =>
                          value == null ? 'Please select a location' : null,
                      icon: Icons.location_city,
                    ),
                    vSizedBox2,
                    _buildInputField(
                      controller: descriptionController,
                      label: 'Description',
                      icon: Icons.description,
                      maxLines: 3,
                      validator: (value) => value == null || value.isEmpty
                          ? 'Please enter a description'
                          : null,
                    ),
                    vSizedBox2,
                    _buildInputField(
                      controller: priceController,
                      label: 'Price',
                      icon: Icons.attach_money,
                      keyboardType: TextInputType.number,
                      validator: (value) => value == null || value.isEmpty
                          ? 'Please enter a price'
                          : null,
                    ),
                    vSizedBox2,
                    _buildInputField(
                      controller: floorController,
                      label: 'Floor',
                      icon: Icons.house,
                      keyboardType: TextInputType.number,
                      validator: (value) => value == null || value.isEmpty
                          ? 'Please enter the floor number'
                          : null,
                    ),
                    vSizedBox2,
                    _buildInputField(
                      controller: landAreaController,
                      label: 'Land Area',
                      icon: Icons.landscape,
                      keyboardType: TextInputType.number,
                      validator: (value) => value == null || value.isEmpty
                          ? 'Please enter the land area'
                          : null,
                    ),
                    vSizedBox2,
                    _buildInputField(
                      controller: roadSizeController,
                      label: 'Road Size',
                      icon: Icons.add_road,
                      validator: (value) => value == null || value.isEmpty
                          ? 'Please enter the road size'
                          : null,
                    ),
                    vSizedBox2,
                    _buildDropdownField<String>(
                      value: selectedRoadType,
                      label: 'Road Type',
                      items: const ['Gravelled', 'Pitched', 'Offroad'],
                      onChanged: (value) {
                        setState(() {
                          selectedRoadType = value;
                        });
                      },
                      validator: (value) =>
                          value == null ? 'Please select road type' : null,
                      icon: Icons.add_road,
                    ),
                    vSizedBox2,
                    _buildDropdownField<String>(
                      value: selectedWaterSupply,
                      label: 'Water Supply',
                      items: const ['Yes', 'No'],
                      onChanged: (value) {
                        setState(() {
                          selectedWaterSupply = value;
                        });
                      },
                      validator: (value) => value == null
                          ? 'Please select water supply option'
                          : null,
                      icon: Icons.water_drop,
                    ),
                    vSizedBox2,
                    _buildDropdownField<String>(
                      value: selectedParking,
                      label: 'Parking',
                      items: const ['2 Wheeler', '4 Wheeler', 'Both'],
                      onChanged: (value) {
                        setState(() {
                          selectedParking = value;
                        });
                      },
                      validator: (value) =>
                          value == null ? 'Please select parking option' : null,
                      icon: Icons.local_parking,
                    ),
                    vSizedBox2,
                    _buildDropdownField<String>(
                      value: selectedNegotiable,
                      label: 'Negotiable',
                      items: const ['Yes', 'No'],
                      onChanged: (value) {
                        setState(() {
                          selectedNegotiable = value;
                        });
                      },
                      validator: (value) => value == null
                          ? 'Please select negotiable option'
                          : null,
                      icon: Icons.attach_money,
                    ),
                    vSizedBox2,
                    _buildInputField(
                      controller: contactPersonController,
                      label: 'Contact Person (Number)',
                      icon: Icons.person,
                      validator: (value) => value == null || value.isEmpty
                          ? 'Please enter contact person\'s name'
                          : null,
                    ),
                    vSizedBox2,
                    widget.isFromEdit ?? false
                        ? Container()
                        : Column(
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.image,
                                    size: size.aspectRatio * 55,
                                    color: Colors.white,
                                  ),
                                  SizedBox(width: size.width * 0.01),
                                  const Text(
                                    "Image",
                                    style: TextStyle(
                                      fontFamily: 'hello',
                                      fontSize: 18,
                                      fontWeight: FontWeight.w500,
                                      color: Color.fromRGBO(0, 131, 143, 1),
                                    ),
                                  ),
                                ],
                              ),
                              pickedMarketImage != null
                                  ? GestureDetector(
                                      onTap: () {
                                        selectSource();
                                      },
                                      child: Image.file(
                                        pickedMarketImage!,
                                      ),
                                    )
                                  : InkWell(
                                      onTap: () {
                                        selectSource();
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.all(8),
                                        height: size.height * 0.18,
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          border: Border.all(
                                              color: Theme.of(context)
                                                  .primaryColor,
                                              style: BorderStyle.solid),
                                        ),
                                        child: Column(
                                          children: [
                                            Expanded(
                                                child: Icon(
                                              Icons.cloud_upload,
                                              color: const Color.fromRGBO(
                                                  0, 131, 143, 1),
                                              size: size.height * 0.1,
                                            )),
                                            const Text(
                                              "Upload Your Image",
                                              style: TextStyle(
                                                fontSize: 20,
                                                color: Color.fromRGBO(
                                                    0, 131, 143, 1),
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                            ],
                          ),
                    Center(
                      child: Padding(
                        padding: EdgeInsets.all(size.aspectRatio * 75.0),
                        child: Obx(
                          () => marketController.isLoading.value
                              ? const Center(
                                  child: CircularProgressIndicator(),
                                )
                              : AppButton(
                                  name: "Submit",
                                  onPressed: () {},
                                ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? Function(String?)? validator,
    int? maxLines,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        labelText: label,
        labelStyle: const TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
        ),
        prefixIcon: Icon(icon, color: const Color.fromRGBO(0, 131, 143, 1)),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
        ),
      ),
      validator: validator,
    );
  }

  Widget _buildDropdownField<T>({
    required T? value,
    required String label,
    required List<String> items,
    required ValueChanged<T?> onChanged,
    String? Function(T?)? validator,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color.fromRGBO(0, 131, 143, 1)),
      ),
      child: DropdownButtonFormField<T>(
        value: value,
        decoration: InputDecoration(
          labelText: label,
          labelStyle: const TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
          border: InputBorder.none,
          prefixIcon: Icon(icon, color: const Color.fromRGBO(0, 131, 143, 1)),
        ),
        items: items.map((String value) {
          return DropdownMenuItem<T>(
            value: value as T,
            child: Text(value),
          );
        }).toList(),
        onChanged: onChanged,
        validator: validator,
      ),
    );
  }

  Future pickcitizenshipfront(ImageSource source) async {
    final imagePicker = ImagePicker();
    final pickedImage = await imagePicker.pickImage(source: source);

    if (pickedImage != null) {
      setState(() {
        pickedMarketImage = File(pickedImage.path);
        consolelog('picked image: $pickedMarketImage');
      });
    } else {
      consolelog('no profile picture selected');
    }
  }

  void selectSource() {
    showDialog(
      context: context,
      builder: (context) {
        return SizedBox(
          child: AlertDialog(
            title: const Center(
                child: Text(
              "Select",
              style: TextStyle(fontSize: 21, color: Colors.black),
            )),
            content: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ConstrainedBox(
                  constraints: const BoxConstraints(),
                  child: ElevatedButton(
                      style: ButtonStyle(
                          backgroundColor: WidgetStateProperty.all(
                              Theme.of(context).primaryColor)),
                      onPressed: () {
                        pickcitizenshipfront(ImageSource.gallery);
                        back(context);
                      },
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.image_outlined),
                          SizedBox(
                            width: 1,
                          ),
                          Text("Gallery"),
                        ],
                      )),
                ),
                const SizedBox(
                  width: 10,
                ),
                ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                    ),
                    onPressed: () {
                      pickcitizenshipfront(ImageSource.camera);
                      back(context);
                    },
                    child: const Row(
                      children: [
                        Icon(Icons.camera_alt_outlined),
                        SizedBox(
                          width: 1,
                        ),
                        Text("Camera"),
                      ],
                    )),
              ],
            ),
            actions: [
              TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text("Ok"))
            ],
          ),
        );
      },
    );
  }
}

class CustomDropdownField<T> extends StatelessWidget {
  final T? value;
  final String label;
  final List<String> items;
  final ValueChanged<T?>? onChanged;
  final FormFieldValidator<T>? validator;
  final IconData icon;

  const CustomDropdownField({
    super.key,
    required this.value,
    required this.label,
    required this.items,
    this.onChanged,
    this.validator,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        labelStyle: const TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
        ),
        prefixIcon: Icon(icon,
            color: const Color.fromRGBO(0, 131, 143, 1)), // Icon here
      ),
      items: items
          .map((item) => DropdownMenuItem<T>(
                value: item as T,
                child: Text(item),
              ))
          .toList(),
      onChanged: onChanged,
      validator: validator,
    );
  }
}
