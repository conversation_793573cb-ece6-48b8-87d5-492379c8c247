PODS:
  - connectivity_plus (0.0.1):
    - FlutterMacOS
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - geolocator_apple (1.2.0):
    - FlutterMacOS
  - network_info_plus (0.0.1):
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - ReachabilitySwift (5.0.0)
  - share_plus (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - geolocator_apple (from `Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos`)
  - network_info_plus (from `Flutter/ephemeral/.symlinks/plugins/network_info_plus/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

SPEC REPOS:
  trunk:
    - ReachabilitySwift

EXTERNAL SOURCES:
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  geolocator_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos
  network_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/network_info_plus/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  connectivity_plus: 18d3c32514c886e046de60e9c13895109866c747
  device_info_plus: 5401765fde0b8d062a2f8eb65510fb17e77cf07f
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  geolocator_apple: 72a78ae3f3e4ec0db62117bd93e34523f5011d58
  network_info_plus: f4fbc7877ab7b3294500d9441dfa53cd54972d05
  package_info_plus: 02d7a575e80f194102bef286361c6c326e4c29ce
  path_provider_foundation: eaf5b3e458fc0e5fbb9940fb09980e853fe058b8
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  share_plus: 76dd39142738f7a68dd57b05093b5e8193f220f7
  shared_preferences_foundation: 5b919d13b803cadd15ed2dc053125c68730e5126
  url_launcher_macos: 5335912b679c073563f29d89d33d10d459f95451

PODFILE CHECKSUM: 353c8bcc5d5b0994e508d035b5431cfe18c1dea7

COCOAPODS: 1.12.1
