import 'dart:convert';
import 'dart:ffi' as ffi;
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/network/services/authServices/auth_controller.dart';
import 'package:smartsewa/network/services/userdetails/current_user_controller.dart';
import 'package:smartsewa/views/payment_screen.dart';
import 'package:smartsewa/views/serviceProviderScreen/serviceProfile/service_edit_screen.dart';
import 'package:smartsewa/views/user_screen/approval/approval_screen.dart';
import 'package:smartsewa/views/user_screen/profile/edit_profile_user.dart';
import 'package:smartsewa/views/widgets/buttons/app_buttons1_left.dart';
import 'package:smartsewa/views/widgets/custom_dialogs.dart';
import 'package:smartsewa/views/widgets/custom_toasts.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';
import '../../../network/base_client.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';

class PaymentHistory {
  final String addedDate;
  final String paymentDetails;
  final String? expirationDate;
  final double finalAmount;
  final String? paymentDuration;
  final String merchant;

  PaymentHistory({
    required this.addedDate,
    required this.paymentDetails,
    this.expirationDate,
    required this.finalAmount,
    this.paymentDuration,
    required this.merchant,
  });

  factory PaymentHistory.fromJson(Map<String, dynamic> json) {
    return PaymentHistory(
      addedDate: json['addedDate'] ?? '',
      paymentDetails: json['paymentDetails'] ?? '',
      expirationDate: json['expirationDate'],
      finalAmount: (json['finalAmount'] ?? 0.0).toDouble(),
      paymentDuration: json['paymentDuration'],
      merchant: json['merchant'] ?? '',
    );
  }
}

class ServiceModel {
  final String name;

  ServiceModel({
    required this.name,
  });

  factory ServiceModel.fromString(String serviceName) {
    return ServiceModel(
      name: serviceName.trim(),
    );
  }
}

class Profile extends StatefulWidget {
  const Profile({super.key});

  @override
  State<Profile> createState() => _ProfileState();
}

class _ProfileState extends State<Profile> {
  final controller = Get.put(CurrentUserController());
  final _authController = Get.put(AuthController());
  String baseUrl = BaseClient().baseUrl;
  String workStatus = '';
  File? _profileImage;
  bool _isServiceProviderActive = false;
  double? averageRating;
  int? nos;
  bool work = false;
  List<String> serviceIds = [];
  String categoryId = 'N/A';
  bool isLoadingServices = false;
  String error = '';
  List<PaymentHistory> paymentHistory = [];
  bool isLoadingPayments = false;
  String paymentError = '';

  @override
  void initState() {
    super.initState();
    bool kk = _updateWorkStatus();
    // First load any cached values
    _loadServiceProviderStatus();

    if (kk) {
      // Then fetch latest data and update UI when complete
      getRatings(kk).then((_) {
        if (mounted) {
          setState(() {
            // This will trigger a UI refresh with the new rating values
          });
        }
      });
      consolelog("workvalue: $kk");
      fetchProviderServices();
      fetchPaymentHistory();
    }
  }

  Future<void> fetchPaymentHistory() async {
    if (!mounted) return;

    setState(() {
      isLoadingPayments = true;
      paymentError = '';
    });

    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('token');
      int? userId = controller.currentUserData.value.id;

      if (token == null || userId == null) {
        throw Exception('AUTH_REQUIRED');
      }

      var headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      };

      var request =
          http.Request('GET', Uri.parse('$baseUrl/api/v1/user/$userId'));
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (!mounted) return;

      if (response.statusCode == 200) {
        String responseBody = await response.stream.bytesToString();
        List<dynamic> data = json.decode(responseBody);

        setState(() {
          paymentHistory =
              data.map((item) => PaymentHistory.fromJson(item)).toList();
          isLoadingPayments = false;

          // Check if payment history is empty and set appropriate message
          if (paymentHistory.isEmpty) {
            paymentError =
                'No payments made yet. Your payment history will appear here once you make your first payment.';
          } else {
            paymentError = '';
          }
        });
      } else {
        // Special handling for 500 error which might indicate no payment history
        if (response.statusCode == 500) {
          // Try to read the response body to check if it's related to no payments
          String responseBody = await response.stream.bytesToString();

          // Check if the error response indicates no payments or empty history
          if (responseBody.toLowerCase().contains('no payment') ||
              responseBody.toLowerCase().contains('empty') ||
              responseBody.toLowerCase().contains('not found')) {
            // Treat as empty payment history instead of server error
            setState(() {
              paymentHistory = [];
              isLoadingPayments = false;
              paymentError =
                  'No payments made yet. Your payment history will appear here once you make your first payment.';
            });
            return; // Exit the function early
          }
        }

        // Handle other HTTP status codes with specific messages
        String errorMessage =
            _getErrorMessageForStatusCode(response.statusCode);
        throw Exception('HTTP_${response.statusCode}');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          paymentError = _getUserFriendlyErrorMessage(e.toString());
          isLoadingPayments = false;
        });
      }
    }
  }

  String _getUserFriendlyErrorMessage(String error) {
    if (error.contains('AUTH_REQUIRED')) {
      return 'Please log in to view your payment history';
    } else if (error.contains('HTTP_401')) {
      return 'Your session has expired. Please log in again';
    } else if (error.contains('HTTP_403')) {
      return 'You don\'t have permission to access this information';
    } else if (error.contains('HTTP_404')) {
      return 'No payments made yet. Your payment history will appear here once you make your first payment.';
    } else if (error.contains('HTTP_500')) {
      // For 500 errors, provide a more generic message since it could be server-side
      return 'Unable to load payment history right now. Please try again later';
    } else if (error.contains('HTTP_503')) {
      return 'Service temporarily unavailable. Please try again later';
    } else if (error.contains('SocketException') ||
        error.contains('TimeoutException')) {
      return 'Please check your internet connection and try again';
    } else if (error.contains('FormatException')) {
      return 'Unable to process server response. Please try again';
    } else {
      // Generic fallback message
      return 'Unable to load payment history. Please try again';
    }
  }

  String _getErrorMessageForStatusCode(int statusCode) {
    switch (statusCode) {
      case 400:
        return 'Invalid request';
      case 401:
        return 'Authentication failed';
      case 403:
        return 'Access forbidden';
      case 404:
        return 'Payment history not found';
      case 429:
        return 'Too many requests. Please try again later';
      case 500:
        return 'Internal server error';
      case 502:
        return 'Bad gateway';
      case 503:
        return 'Service unavailable';
      case 504:
        return 'Gateway timeout';
      default:
        return 'Request failed';
    }
  }

  Future<void> fetchProviderServices() async {
    if (!mounted) return;

    setState(() {
      isLoadingServices = true;
      error = ''; // Clear previous error
    });

    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('token');
      int? userId = controller.currentUserData.value.id;

      if (token == null) {
        throw Exception('AUTH_TOKEN_MISSING');
      }

      if (userId == null) {
        throw Exception('USER_ID_MISSING');
      }

      final response = await http.get(
        Uri.parse('$baseUrl/api/user/$userId/admin/servicesID'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (!mounted) return;

      if (response.statusCode == 200) {
        var dat = response.body.replaceAll("'", '"');
        Map<String, dynamic> data = json.decode(dat);

        if (mounted) {
          setState(() {
            if (data['Service ID'] != null) {
              String serviceIdsStr = data['Service ID']
                  .toString()
                  .replaceAll(RegExp(r'[\[\]]'), '');
              serviceIds = serviceIdsStr
                  .split(',')
                  .map((e) => e.trim())
                  .where((e) => e.isNotEmpty)
                  .toList();
            } else {
              serviceIds = [];
            }

            categoryId = data['Category Id']?.toString() ?? 'N/A';
            isLoadingServices = false;

            // Handle case where no services are available
            if (serviceIds.isEmpty) {
              error =
                  'No services available at the moment. Please check back later.';
            }
          });
        }
        print("Services fetched: $serviceIds");
      } else {
        // Handle different HTTP status codes
        throw Exception('HTTP_${response.statusCode}');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          error = _getServiceErrorMessage(e.toString());
          isLoadingServices = false;
        });

        // Log error for debugging (you can remove this in production)
        print("Error fetching services: $e");
      }
    }
  }

// Helper method to get user-friendly error messages for services
  String _getServiceErrorMessage(String error) {
    if (error.contains('AUTH_TOKEN_MISSING')) {
      return 'Please log in again to view your services';
    } else if (error.contains('USER_ID_MISSING')) {
      return 'Unable to identify your account. Please try logging in again';
    } else if (error.contains('HTTP_401')) {
      return 'Your session has expired. Please log in again';
    } else if (error.contains('HTTP_403')) {
      return 'You don\'t have permission to access this information';
    } else if (error.contains('HTTP_404')) {
      return 'No services found for your account';
    } else if (error.contains('HTTP_429')) {
      return 'Too many requests. Please wait a moment and try again';
    } else if (error.contains('HTTP_500')) {
      return 'Unable to load services right now. Please try again later';
    } else if (error.contains('HTTP_502') || error.contains('HTTP_503')) {
      return 'Service temporarily unavailable. Please try again in a few minutes';
    } else if (error.contains('HTTP_504')) {
      return 'Request timed out. Please check your connection and try again';
    } else if (error.contains('SocketException') ||
        error.contains('TimeoutException')) {
      return 'Please check your internet connection and try again';
    } else if (error.contains('FormatException')) {
      return 'Unable to process server response. Please try again';
    } else {
      return 'Unable to load services. Please try again';
    }
  }

  Future<void> _loadServiceProviderStatus() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      _isServiceProviderActive =
          prefs.getBool("isServiceProviderActive") ?? false;
      _isServiceProviderActive = !_isServiceProviderActive;
      consolelog("Shared preferences value:$_isServiceProviderActive");

      averageRating = prefs.getDouble("AveRating");
      nos = prefs.getInt("numberRat");
      work = prefs.getBool("workStatus") ?? false;
    });
  }

  Future<void> _saveServiceProviderStatus(bool status) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString('token');
    int? id = prefs.getInt('id');
    consolelog('id=$id');
    updateUserOnlineStatus(id!, apptoken!, status);
  }

  bool _updateWorkStatus() {
    if (controller.currentUserData.value.workStatus == false) {
      workStatus = "Normal User";
      consolelog(workStatus);

      return false;
    } else {
      workStatus = controller.currentUserData.value.approval == false
          ? "Service Provider"
          : "Service Provider (Pending)";
      consolelog(workStatus);
      return true;
    }
  }

  Future<void> updateUserOnlineStatus(
      int uid, String token, bool status) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token'
    };
    status = !status;
    var request =
        http.Request('PUT', Uri.parse('$baseUrl/api/users/$uid/online-status'));
    request.body = json.encode(status); // Use a JSON object for the body
    request.headers.addAll(headers);
    consolelog(request.body);

    try {
      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        String responseBody = await response.stream.bytesToString();
        successToast(msg: "Updated");
        print('Response: $responseBody');

        await prefs.setBool("isServiceProviderActive", status);
        _loadServiceProviderStatus();
      } else {
        print('Error: ${response.reasonPhrase}');
        status = !status;
        errorToast(msg: "Update failed");
        await prefs.setBool("isServiceProviderActive", status);
        _loadServiceProviderStatus();
      }
    } catch (e) {
      status = !status;
      print('An error occurred: $e');
      errorToast(msg: "Update failed");
      await prefs.setBool("isServiceProviderActive", status);
      _loadServiceProviderStatus();
    }
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;

    // Responsive breakpoints
    bool isSmallScreen = size.width < 600;
    bool isMediumScreen = size.width >= 600 && size.width < 1024;
    bool isLargeScreen = size.width >= 1024;

    // Responsive padding and spacing
    double horizontalPadding =
        isSmallScreen ? 16.0 : (isMediumScreen ? 24.0 : 32.0);
    double verticalPadding =
        isSmallScreen ? 20.0 : (isMediumScreen ? 24.0 : 28.0);
    double sectionSpacing =
        isSmallScreen ? 20.0 : (isMediumScreen ? 24.0 : 28.0);

    // Maximum width for content on larger screens
    double maxContentWidth = isLargeScreen ? 800.0 : double.infinity;

    String expText = "";
    if (controller.currentUserData.value.workStatus == true) {
      DateTime? expiryDate = DateTime.parse(
          controller.currentUserData.value.expiryDate.toString());
      Duration difference = expiryDate.difference(DateTime.now());

      if (difference.inDays <= 0) {
        expText = "(Expired)";
      } else {
        expText = "(${difference.inDays} days)";
      }
    }

    return Scaffold(
      appBar: myAppbar(context, true, "Profile"),
      body: SafeArea(
        child: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          } else {
            return LayoutBuilder(
              builder: (context, constraints) {
                return SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Center(
                    child: Container(
                      width: maxContentWidth,
                      padding: EdgeInsets.symmetric(
                        vertical: verticalPadding,
                        horizontal: horizontalPadding,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          _profileInfoSection(context),
                          SizedBox(height: sectionSpacing),
                          _userDetailsSection(expText),
                          if (work) ...[
                            SizedBox(height: sectionSpacing),
                            _buildServicesSection(),
                            SizedBox(
                                height:
                                    sectionSpacing * 0.25), // Smaller spacing
                            _buildPaymentHistorySection(),
                          ],
                          // Add bottom padding for better scrolling experience
                          SizedBox(height: isSmallScreen ? 20.0 : 40.0),
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          }
        }),
      ),
    );
  }

  Widget _serviceProviderStatusToggle(String expText) {
    Color textColor = expText == "(Expired)" ? Colors.red : Colors.black;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).primaryColor.withOpacity(0.1),
              ),
              padding: const EdgeInsets.all(9),
              child: const Icon(
                Icons.work,
                color: Color.fromRGBO(0, 131, 143, 1),
                size: 23,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                _isServiceProviderActive
                    ? 'Status: Visible on Map'
                    : 'Status: Hidden on Map',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            SizedBox(
              width: 60,
              height: 40,
              child: Switch(
                value: _isServiceProviderActive,
                onChanged: (value) {
                  setState(() {
                    if (value == false) {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            title: const Text("Confirmation"),
                            content:
                                const Text("Are you sure to be hidden on map?"),
                            actions: [
                              TextButton(
                                onPressed: () {
                                  value = true;
                                  // Handle "No" action
                                  Navigator.of(context)
                                      .pop(); // Closes the dialog
                                },
                                child: const Text("No"),
                              ),
                              TextButton(
                                onPressed: () {
                                  _isServiceProviderActive = value;
                                  _saveServiceProviderStatus(value);

                                  // Handle "Yes" action
                                  Navigator.of(context)
                                      .pop(); // Closes the dialog
                                  // Perform any action on confirmation
                                },
                                child: const Text("Yes"),
                              ),
                            ],
                          );
                        },
                      );
                    } else {
                      _isServiceProviderActive = value;
                      _saveServiceProviderStatus(value);
                    }
                  });
                },
                activeColor: const Color.fromRGBO(0, 131, 143, 1),
                activeTrackColor: const Color.fromARGB(237, 223, 231, 231),
                inactiveThumbColor: const Color.fromARGB(255, 219, 90, 50),
                inactiveTrackColor: const Color.fromARGB(237, 223, 231, 231),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).primaryColor.withOpacity(0.1),
              ),
              padding: const EdgeInsets.all(8),
              child: const Icon(
                Icons.calendar_today,
                color: Color.fromRGBO(0, 131, 143, 1),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                '  Expiry Date: ${controller.currentUserData.value.expiryDate.toString()} $expText',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: textColor,
                ),
                maxLines: 2, // Allows the text to wrap to a new line if needed
                softWrap: true, // Enables wrapping to prevent overflow
                overflow: TextOverflow.visible, // Ensures no text is cut off
              ),
            ),
          ],
        ),
        const SizedBox(height: 15),
        Row(
          children: [
            // Space above the button
            // if (expText == "(Expired)") // Check condition
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => PaymentScreen(),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color.fromARGB(
                      240, 0, 131, 143), // Set the background color here
                ),
                child: Text(
                  "Extend Account",
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: MediaQuery.of(context).size.width *
                        0.05, // Adjust the multiplier as needed
                  ),
                ),
              ),
            ),
          ],
        )
      ],
    );
  }

  Widget _profileInfoSection(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double padding = screenWidth * 0.04; // ~16 at 400px
    double spacingTop = screenWidth * 0.02; // ~8
    double spacingBelowName = screenWidth * 0.005; // ~2
    double avatarMin = 60;
    double avatarMax = 100;

    return Container(
      decoration: BoxDecoration(
        color: const Color.fromARGB(240, 0, 131, 143),
        borderRadius: BorderRadius.circular(screenWidth * 0.08), // ~32
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 8,
            spreadRadius: 4,
          ),
        ],
      ),
      padding: EdgeInsets.all(padding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              LayoutBuilder(
                builder: (context, constraints) {
                  double avatarRadius = (constraints.maxWidth * 0.20)
                      .clamp(avatarMin, avatarMax); // Keep within range
                  return CircleAvatar(
                    radius: avatarRadius,
                    backgroundColor: Colors.white,
                    backgroundImage: _profileImage != null
                        ? FileImage(_profileImage!)
                        : NetworkImage(
                            "$baseUrl/api/allimg/image/${controller.currentUserData.value.picture}",
                          ) as ImageProvider,
                  );
                },
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: _profileImage == null
                    ? _cameraIconButton()
                    : _saveImageButton(),
              ),
            ],
          ),
          SizedBox(height: spacingTop),
          _userNameRow(context),
          SizedBox(height: spacingBelowName),
          if (work) _buildRatingStars(averageRating, nos),
        ],
      ),
    );
  }

  Future<void> getRatings(bool kk) async {
    final String url =
        '$baseUrl/api/v1/rating/${controller.currentUserData.value.id}';
    consolelog(url);

    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        // If the server returns a 200 OK response, parse the JSON.
        final data = response.body;
        print('Raw Response: $data');

        // Manually parse the plain string response
        List<String> parts = data.split(', ');

        // Extract the average rating
        String avgRatingPart = parts[0]; // "Average Rating: 3.5"
        double avgRating = double.parse(avgRatingPart.split(':')[1]);
        print('Average Rating: $avgRating');

        // Extract the number of ratings
        String noOfRatingsPart = parts[1]; // "No: 5"
        int noOfRatings = int.parse(noOfRatingsPart.split(':')[1]);
        print('Number of Ratings: $noOfRatings');

        // Store the values in SharedPreferences
        SharedPreferences pref = await SharedPreferences.getInstance();
        pref.setDouble("AveRating", avgRating);
        pref.setInt("numberRat", noOfRatings);
        pref.setBool("Work", kk);

        // Update state variables directly
        if (mounted) {
          setState(() {
            averageRating = avgRating;
            nos = noOfRatings;
          });
        }

        print('Ratings: $data');
      } else {
        errorToast(msg: "Failed to fetch rating");
        // If the server did not return a 200 OK response, throw an exception.
        throw Exception('Failed to load ratings: ${response.statusCode}');
      }
    } catch (e) {
      // Handle any exceptions or errors.
      errorToast(msg: "Failed to fetch rating\n Internal Server Error");
      print('Error: $e');
    }
  }

  Widget _buildRatingStars(double? averageRating, int? nos) {
    if (averageRating == null) return const SizedBox.shrink();

    int fullStars = averageRating.floor();
    int halfStars = (averageRating % 1 >= 0.5) ? 1 : 0;
    int emptyStars = 5 - fullStars - halfStars;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Full stars
        for (int i = 0; i < fullStars; i++)
          const Icon(
            Icons.star,
            color: Colors.amber, // Gold color for full stars
            size: 30,
          ),
        // Half stars
        for (int i = 0; i < halfStars; i++)
          const Icon(
            Icons.star_half,
            color: Colors.amber, // Same color for half stars
            size: 30,
          ),
        // Empty stars
        for (int i = 0; i < emptyStars; i++)
          const Icon(
            Icons.star_border,
            color: Colors.white, // White color for empty stars
            size: 30,
          ),
        const SizedBox(width: 8), // Space after stars

        // Rating display with background
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: const Color.fromARGB(
                240, 0, 131, 143), // Custom background color
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            "${averageRating.toStringAsFixed(1)} (${nos ?? 0})", // Display rating and count
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white, // White text color for contrast
            ),
          ),
        ),
      ],
    );
  }

  Widget _cameraIconButton() {
    return InkWell(
      onTap: () {
        _selectSource();
      },
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 6,
              spreadRadius: 3,
            ),
          ],
        ),
        padding: const EdgeInsets.all(6),
        child: Icon(
          Icons.camera_alt_outlined,
          color: Theme.of(context).primaryColor,
          size: 28,
        ),
      ),
    );
  }

  Widget _saveImageButton() {
    return ElevatedButton(
      onPressed: () {
        CustomDialogs.fullLoadingDialog(
          context: context,
          data: "Image Uploading...",
        );
        _authController.uploadProfile(_profileImage);
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color.fromARGB(255, 210, 230, 230),
        side: const BorderSide(
            color: Color.fromARGB(255, 245, 255, 255), width: 1.5),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      ),
      child: const Text(
          style: TextStyle(color: Color.fromRGBO(0, 131, 143, 1)), 'Save'),
    );
  }

  Widget _userNameRow(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    double fontSize = screenWidth * 0.06; // ~24 at 400px
    double iconSize = screenWidth * 0.06; // ~24 at 400px
    double avatarSize = screenWidth * 0.08; // ~32 at 400px
    double spacing = screenWidth * 0.025; // ~10 at 400px

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Flexible(
          child: Text(
            controller.currentUserData.value.fullName.toString(),
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        SizedBox(width: spacing),
        Stack(
          alignment: Alignment.center,
          children: [
            Container(
              width: avatarSize,
              height: avatarSize,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
              ),
            ),
            SizedBox(
              width: avatarSize,
              height: avatarSize,
              child: IconButton(
                padding: EdgeInsets.zero,
                icon: Icon(
                  Icons.edit,
                  color: Theme.of(context).primaryColor,
                  size: iconSize,
                ),
                onPressed: () {
                  Get.to(() => EditProfileUser());
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _userDetailsSection(String expText) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 8,
            spreadRadius: 4,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "User Details",
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Divider(height: 20, thickness: 1.5),
          _buildUserDetailItem(
              controller.currentUserData.value.mobileNumber.toString(),
              Icons.phone,
              false),
          _buildUserDetailItem(
              controller.currentUserData.value.address.toString(),
              Icons.home,
              false),
          // Service Provider Status with Icon and Edit Button
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor:
                    Theme.of(context).primaryColor.withOpacity(0.1),
                child: const Icon(Icons.work,
                    color: Color.fromRGBO(0, 131, 143, 1), size: 25),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  workStatus,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ),
              if (controller.currentUserData.value.workStatus == true)
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () {
                    Get.to(() => ServiceProviderSettingPage());
                    // Add your edit action here
                  },
                  color: Theme.of(context).primaryColor,
                ),
            ],
          ),
          const SizedBox(height: 10),
          // Row(
          //   children: [
          //     CircleAvatar(
          //       radius: 20,
          //       backgroundColor:
          //           Theme.of(context).primaryColor.withOpacity(0.1),
          //       child: const Icon(Icons.directions_run,
          //           color: Color.fromRGBO(0, 131, 143, 1), size: 25),
          //     ),
          //     const SizedBox(width: 16),
          //     const Expanded(
          //       child: Text(
          //         "Calls", // Replace `callCount` with your actual variable
          //         style: const TextStyle(
          //           fontSize: 16,
          //           fontWeight: FontWeight.w600,
          //           color: Colors.black87,
          //         ),
          //       ),
          //     ),
          //   ],
          // ),
          // const SizedBox(height: 10),
          _serviceProviderButton(),
          if (controller.currentUserData.value.workStatus == true)
            _serviceProviderStatusToggle(expText),
        ],
      ),
    );
  }

  Widget _buildServicesSection() {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;

    // Define responsive values
    double padding = screenWidth * 0.03;
    double iconSize = screenWidth * 0.06; // ~24 at 400px width
    double fontSize = screenWidth * 0.045; // ~18 at 400px
    double categoryFontSize = screenWidth * 0.04;
    double servicePadding = screenWidth * 0.035;
    double height = screenHeight * 0.25;
    double sizedBoxWidth = screenWidth * 0.025;

    return Container(
      margin:
          EdgeInsets.symmetric(vertical: 20, horizontal: screenWidth * 0.04),
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.15),
            blurRadius: 8,
            spreadRadius: 4,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.category,
                    color: Colors.blue,
                    size: iconSize,
                  ),
                  SizedBox(width: sizedBoxWidth),
                  Text(
                    "JOBS",
                    style: TextStyle(
                      fontSize: fontSize,
                      fontWeight: FontWeight.bold,
                      color: Color.fromRGBO(0, 131, 143, 1),
                    ),
                  ),
                ],
              ),
              Flexible(
                child: Text(
                  "$categoryId",
                  style: TextStyle(
                    color: Color.fromRGBO(0, 131, 143, 1),
                    fontSize: categoryFontSize,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const Divider(height: 20, thickness: 1.5, color: Colors.grey),
          if (isLoadingServices)
            const Center(child: CircularProgressIndicator())
          else if (error.isNotEmpty)
            Center(
              child: Text(
                error,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            )
          else if (serviceIds.isEmpty)
            const Center(
              child: Text(
                "No services added yet",
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
              ),
            )
          else
            Container(
              height: height,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: serviceIds.map((serviceName) {
                    return Padding(
                      padding:
                          EdgeInsets.symmetric(vertical: screenHeight * 0.012),
                      child: GestureDetector(
                        onTap: () {},
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          padding: EdgeInsets.symmetric(
                            vertical: screenHeight * 0.015,
                            horizontal: servicePadding,
                          ),
                          child: Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(screenWidth * 0.025),
                                decoration: BoxDecoration(
                                  color: Color.fromRGBO(0, 131, 143, 1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.work,
                                  color: Colors.white,
                                  size: iconSize,
                                ),
                              ),
                              SizedBox(width: sizedBoxWidth),
                              Expanded(
                                child: Text(
                                  serviceName,
                                  style: TextStyle(
                                    color: Color.fromRGBO(0, 131, 143, 1),
                                    fontSize: screenWidth * 0.042,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.start,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildUserDetailItem(String text, IconData icon, bool edit) {
    // This method no longer uses the edit parameter
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
            child: Icon(icon, color: Theme.of(context).primaryColor, size: 25),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _serviceProviderButton() {
    return Obx(() {
      bool showButton = !(controller.currentUserData.value.workStatus == true);
      return showButton
          ? AppButton1(
              onPressed: () {
                Get.to(() => ApprovalScreen(
                      mobileNumber: controller
                          .currentUserData.value.mobileNumber
                          .toString(),
                      fullName:
                          controller.currentUserData.value.fullName.toString(),
                    ));
              },
              name: 'Become a Service Provider',
              padding: const EdgeInsets.symmetric(vertical: 16),
            )
          : const SizedBox.shrink();
    });
  }

  Future pickProfileImage(ImageSource source) async {
    final imagePicker = ImagePicker();
    final pickedImage = await imagePicker.pickImage(source: source);

    if (pickedImage != null) {
      setState(() {
        _profileImage = File(pickedImage.path);
      });
    }
  }

  void _selectSource() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Center(child: Text("Select")),
          content: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color.fromRGBO(
                      0, 131, 143, 1), // Set the background color
                ),
                onPressed: () {
                  pickProfileImage(ImageSource.gallery);
                  Navigator.pop(context);
                },
                child: const Text("Gallery"),
              ),
              const SizedBox(width: 10),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color.fromRGBO(
                      0, 131, 143, 1), // Set the background color
                ),
                onPressed: () {
                  pickProfileImage(ImageSource.camera);
                  Navigator.pop(context);
                },
                child: const Text("Camera"),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPaymentHistorySection() {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;
    final textScaleFactor = mediaQuery.textScaleFactor;

    // Define breakpoints
    final isVerySmallScreen = screenWidth < 360;
    final isSmallScreen = screenWidth < 480;
    final isMediumScreen = screenWidth < 768;
    final isTablet = screenWidth >= 768 && screenWidth < 1024;
    final isDesktop = screenWidth >= 1024;

    // Responsive dimensions
    final horizontalMargin = isVerySmallScreen
        ? 12.0
        : (isSmallScreen ? 16.0 : (isMediumScreen ? 20.0 : 24.0));
    final containerPadding = isVerySmallScreen
        ? 12.0
        : (isSmallScreen ? 16.0 : (isMediumScreen ? 20.0 : 24.0));
    final borderRadius = isSmallScreen ? 12.0 : 16.0;
    final iconSize = isVerySmallScreen ? 24.0 : (isSmallScreen ? 26.0 : 28.0);

    // MediaQuery-based text sizes
    final baseFontSize = mediaQuery.textScaleFactor;
    final titleFontSize = (isVerySmallScreen
            ? 18.0
            : (isSmallScreen ? 20.0 : (isMediumScreen ? 22.0 : 24.0))) /
        textScaleFactor;
    final buttonTextSize =
        (isVerySmallScreen ? 12.0 : (isSmallScreen ? 14.0 : 16.0)) /
            textScaleFactor;
    final errorTextSize = (isVerySmallScreen ? 12.0 : 14.0) / textScaleFactor;
    final emptyTextSize = (isVerySmallScreen ? 14.0 : 16.0) / textScaleFactor;
    final refreshButtonTextSize =
        (isSmallScreen ? 13.0 : 14.0) / textScaleFactor;
    final disputeButtonTextSize =
        (isSmallScreen ? 12.0 : 13.0) / textScaleFactor;

    final errorIconSize =
        isVerySmallScreen ? 40.0 : (isSmallScreen ? 44.0 : 48.0);
    final emptyIconSize =
        isVerySmallScreen ? 48.0 : (isSmallScreen ? 56.0 : 64.0);
    final dividerHeight =
        screenWidth * (isSmallScreen ? 0.042 : 0.053); // 16-20px responsive

    // Button dimensions
    final buttonHorizontalPadding =
        isVerySmallScreen ? 12.0 : (isSmallScreen ? 16.0 : 24.0);
    final buttonVerticalPadding =
        isVerySmallScreen ? 6.0 : (isSmallScreen ? 8.0 : 12.0);
    final iconButtonPadding = isVerySmallScreen ? 4.0 : 8.0;

    // List height calculation
    final listHeight = isVerySmallScreen
        ? 240.0
        : (isSmallScreen
            ? 280.0
            : (isMediumScreen ? 320.0 : (isTablet ? 360.0 : 400.0)));

    // Maximum width for desktop
    final maxWidth = isDesktop ? 800.0 : double.infinity;

    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: maxWidth),
        margin: EdgeInsets.symmetric(horizontal: horizontalMargin),
        padding: EdgeInsets.all(containerPadding),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(borderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: isSmallScreen ? 6 : 8,
              spreadRadius: isSmallScreen ? 1 : 2,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            isVerySmallScreen
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.payment,
                            color: Color.fromRGBO(0, 131, 143, 1),
                            size: iconSize,
                          ),
                          SizedBox(
                              width: MediaQuery.of(context).size.width *
                                  0.032), // ~12px responsive
                          Expanded(
                            child: Text(
                              "Payment History",
                              style: TextStyle(
                                fontSize: titleFontSize * textScaleFactor,
                                fontWeight: FontWeight.bold,
                                color: Color.fromRGBO(0, 131, 143, 1),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).size.height *
                              0.01), // ~8px responsive
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Padding(
                            padding: EdgeInsets.all(iconButtonPadding),
                            child: InkWell(
                              onTap: fetchPaymentHistory,
                              borderRadius: BorderRadius.circular(20),
                              child: Padding(
                                padding: EdgeInsets.all(iconButtonPadding),
                                child: Icon(
                                  Icons.refresh,
                                  color: Color.fromRGBO(0, 131, 143, 1),
                                  size: iconSize - 4,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(
                              width: MediaQuery.of(context).size.width *
                                  0.011), // ~4px responsive
                          Padding(
                            padding: EdgeInsets.all(iconButtonPadding),
                            child: InkWell(
                              onTap: _showReportDisputeDialog,
                              borderRadius: BorderRadius.circular(20),
                              child: Padding(
                                padding: EdgeInsets.all(iconButtonPadding),
                                child: Icon(
                                  Icons.report_problem,
                                  color: Colors.red,
                                  size: iconSize - 4,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  )
                : Row(
                    children: [
                      Icon(
                        Icons.payment,
                        color: Color.fromRGBO(0, 131, 143, 1),
                        size: iconSize,
                      ),
                      SizedBox(
                          width: MediaQuery.of(context).size.width *
                              0.032), // ~12px responsive
                      Expanded(
                        child: Text(
                          "Payment History",
                          style: TextStyle(
                            fontSize: titleFontSize * textScaleFactor,
                            fontWeight: FontWeight.bold,
                            color: Color.fromRGBO(0, 131, 143, 1),
                          ),
                        ),
                      ),
                      if (isTablet || isDesktop) ...[
                        TextButton.icon(
                          icon: Icon(
                            Icons.refresh,
                            color: Color.fromRGBO(0, 131, 143, 1),
                            size: iconSize - 4,
                          ),
                          label: Text(
                            "Refresh",
                            style: TextStyle(
                              color: Color.fromRGBO(0, 131, 143, 1),
                              fontWeight: FontWeight.w500,
                              fontSize: refreshButtonTextSize * textScaleFactor,
                            ),
                          ),
                          style: TextButton.styleFrom(
                            padding: EdgeInsets.symmetric(
                              horizontal: buttonHorizontalPadding,
                              vertical: buttonVerticalPadding,
                            ),
                          ),
                          onPressed: fetchPaymentHistory,
                        ),
                        SizedBox(
                            width: MediaQuery.of(context).size.width *
                                0.021), // ~8px responsive
                        TextButton.icon(
                          icon: Icon(
                            Icons.report_problem,
                            color: Colors.red,
                            size: iconSize - 4,
                          ),
                          label: Text(
                            "Report Dispute",
                            style: TextStyle(
                              color: Colors.red,
                              fontWeight: FontWeight.w500,
                              fontSize: disputeButtonTextSize * textScaleFactor,
                            ),
                          ),
                          style: TextButton.styleFrom(
                            padding: EdgeInsets.symmetric(
                              horizontal: buttonHorizontalPadding,
                              vertical: buttonVerticalPadding,
                            ),
                          ),
                          onPressed: _showReportDisputeDialog,
                        ),
                      ] else ...[
                        IconButton(
                          icon: Icon(
                            Icons.refresh,
                            color: Color.fromRGBO(0, 131, 143, 1),
                            size: iconSize - 4,
                          ),
                          padding: EdgeInsets.all(iconButtonPadding),
                          constraints: BoxConstraints(
                            minWidth: isSmallScreen ? 36 : 40,
                            minHeight: isSmallScreen ? 36 : 40,
                          ),
                          onPressed: fetchPaymentHistory,
                          tooltip: "Refresh",
                        ),
                        IconButton(
                          icon: Icon(
                            Icons.report_problem,
                            color: Colors.red,
                            size: iconSize - 4,
                          ),
                          padding: EdgeInsets.all(iconButtonPadding),
                          constraints: BoxConstraints(
                            minWidth: isSmallScreen ? 36 : 40,
                            minHeight: isSmallScreen ? 36 : 40,
                          ),
                          tooltip: "Report Payment Dispute",
                          onPressed: _showReportDisputeDialog,
                        ),
                      ],
                    ],
                  ),

            Divider(
              height: dividerHeight,
              thickness: 1.5,
            ),

            // Content Section
            if (isLoadingPayments)
              Center(
                child: Padding(
                  padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
                  child: CircularProgressIndicator(
                    strokeWidth: isVerySmallScreen ? 2.0 : 3.0,
                  ),
                ),
              )
            else if (paymentError.isNotEmpty)
              Center(
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(vertical: isSmallScreen ? 16 : 20),
                  child: Column(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: errorIconSize,
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).size.height *
                              0.01), // ~8px responsive
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: isSmallScreen ? 16 : 24),
                        child: Text(
                          paymentError,
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: errorTextSize * textScaleFactor,
                            height: 1.4,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).size.height *
                              0.015), // ~12px responsive
                      ElevatedButton(
                        onPressed: fetchPaymentHistory,
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                            horizontal: buttonHorizontalPadding,
                            vertical: buttonVerticalPadding,
                          ),
                          minimumSize: Size(
                            isVerySmallScreen ? 80 : 100,
                            isVerySmallScreen ? 32 : 36,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              isSmallScreen ? 8 : 12,
                            ),
                          ),
                        ),
                        child: Text(
                          'Retry',
                          style: TextStyle(
                            fontSize: buttonTextSize * textScaleFactor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else if (paymentHistory.isEmpty)
              Center(
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(vertical: isSmallScreen ? 16 : 20),
                  child: Column(
                    children: [
                      Icon(
                        Icons.receipt_long,
                        color: Colors.grey,
                        size: emptyIconSize,
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).size.height *
                              0.015), // ~12px responsive
                      Text(
                        "No payment history found",
                        style: TextStyle(
                          fontSize: emptyTextSize * textScaleFactor,
                          color: Colors.grey,
                          fontWeight: FontWeight.w500,
                          height: 1.3,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              )
            else
              Container(
                height: listHeight,
                child: ListView.builder(
                  physics: const BouncingScrollPhysics(),
                  itemCount: paymentHistory.length,
                  padding: EdgeInsets.only(
                    top: MediaQuery.of(context).size.height *
                        (isVerySmallScreen ? 0.005 : 0.01), // 4-8px responsive
                  ),
                  itemBuilder: (context, index) {
                    final payment = paymentHistory[index];
                    return Padding(
                      padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).size.height *
                            (isVerySmallScreen
                                ? 0.01
                                : 0.015), // 8-12px responsive
                      ),
                      child: _buildPaymentCard(payment),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentCard(PaymentHistory payment) {
    Size size = MediaQuery.of(context).size;
    bool isSmallScreen = size.width < 600;

    Color merchantColor =
        payment.merchant == 'ESEWA' ? Colors.green : Colors.purple;

    IconData merchantIcon = payment.merchant == 'ESEWA'
        ? Icons.account_balance_wallet
        : Icons.payment;

    return Container(
      margin: EdgeInsets.only(bottom: isSmallScreen ? 8 : 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, Colors.grey.shade50],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isSmallScreen ? 8 : 10),
                  decoration: BoxDecoration(
                    color: merchantColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    merchantIcon,
                    color: merchantColor,
                    size: isSmallScreen ? 20 : 24,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: Text(
                              payment.merchant,
                              style: TextStyle(
                                fontSize: isSmallScreen ? 14 : 16,
                                fontWeight: FontWeight.bold,
                                color: merchantColor,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          SizedBox(width: 8),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Color.fromRGBO(0, 131, 143, 1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              'Rs. ${payment.finalAmount.toStringAsFixed(0)}',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: isSmallScreen ? 12 : 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 4),
                      Text(
                        payment.paymentDetails,
                        style: TextStyle(
                          fontSize: isSmallScreen ? 12 : 14,
                          color: Colors.grey.shade600,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInfoChip(
                    icon: Icons.calendar_today,
                    label: 'Added',
                    value: _formatDate(payment.addedDate),
                  ),
                ),
                if (payment.expirationDate != null) ...[
                  SizedBox(width: 8),
                  Expanded(
                    child: _buildInfoChip(
                      icon: Icons.schedule,
                      label: 'Expires',
                      value: _formatDate(payment.expirationDate!),
                    ),
                  ),
                ],
              ],
            ),
            if (payment.paymentDuration != null) ...[
              SizedBox(height: 8),
              _buildInfoChip(
                icon: Icons.timer,
                label: 'Duration',
                value:
                    '${payment.paymentDuration} year${(int.tryParse(payment.paymentDuration ?? '1') ?? 1) > 1 ? 's' : ''}',
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required String value,
  }) {
    Size size = MediaQuery.of(context).size;
    bool isSmallScreen = size.width < 600;

    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon,
              size: isSmallScreen ? 14 : 16, color: Colors.grey.shade600),
          SizedBox(width: 4),
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 9 : 10,
                    color: Colors.grey.shade600,
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 11 : 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(String dateStr) {
    try {
      DateTime date = DateTime.parse(dateStr);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateStr;
    }
  }

  Future<void> reportDispute({
    required String refCode,
    required String paymentDuration,
    required String merchant,
    required double amount,
    required int userId,
    required DateTime paymentDate,
  }) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('token');

      if (token == null) {
        errorToast(msg: "Authentication required");
        return;
      }

      var headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      };

      var request =
          http.Request('POST', Uri.parse('$baseUrl/api/disputes/report'));

      request.body = json.encode({
        "refCode": refCode,
        "paymentDuration": paymentDuration,
        "merchant": merchant,
        "amount": amount,
        "userId": userId,
        "paymentDate": paymentDate.toIso8601String()
      });

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        String responseBody = await response.stream.bytesToString();
        successToast(msg: "Dispute reported successfully");
        print('Dispute report response: $responseBody');
      } else {
        errorToast(msg: "Failed to report dispute");
        print('Failed to report dispute: ${response.reasonPhrase}');
      }
    } catch (e) {
      errorToast(msg: "Error reporting dispute: $e");
      print('Error occurred while reporting dispute: $e');
    }
  }

  void _showReportDisputeDialog() {
    final TextEditingController merchantController =
        TextEditingController(text: "Khalti");
    final TextEditingController refCodeController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        final screenWidth = MediaQuery.of(context).size.width;
        final screenHeight = MediaQuery.of(context).size.height;
        final isSmallScreen = screenWidth < 600;
        final isMediumScreen = screenWidth >= 600 && screenWidth < 900;

        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
          ),
          elevation: 16,
          child: Container(
            constraints: BoxConstraints(
              maxWidth: isSmallScreen
                  ? screenWidth * 0.95
                  : isMediumScreen
                      ? screenWidth * 0.85
                      : screenWidth * 0.75,
              maxHeight: screenHeight * 0.8,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.white, Colors.grey.shade50],
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                _buildHeader(isSmallScreen),

                // Content
                Flexible(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 16 : 24,
                      vertical: isSmallScreen ? 16 : 20,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Disclaimer Section
                        _buildDisclaimerSection(isSmallScreen),

                        SizedBox(height: isSmallScreen ? 20 : 24),

                        // Merchant Field (First)
                        _buildMerchantDropdown(
                          controller: merchantController,
                          isSmallScreen: isSmallScreen,
                        ),

                        SizedBox(height: isSmallScreen ? 20 : 24),

                        // Reference Code Field (Second)
                        _buildInputField(
                          controller: refCodeController,
                          label: 'Reference Code',
                          hint:
                              'Enter transaction reference from payment receipt',
                          description:
                              'The unique code you received after successful payment completion',
                          icon: Icons.confirmation_number_outlined,
                          isRequired: true,
                          isSmallScreen: isSmallScreen,
                        ),

                        SizedBox(height: isSmallScreen ? 20 : 24),

                        // Important Notice
                        _buildImportantNotice(isSmallScreen),
                      ],
                    ),
                  ),
                ),

                // Action Buttons
                _buildActionButtons(
                  isSmallScreen: isSmallScreen,
                  onCancel: () => Navigator.of(context).pop(),
                  onSubmit: () {
                    if (_validateForm(merchantController, refCodeController)) {
                      _submitDispute(merchantController, refCodeController);
                      Navigator.of(context).pop();
                    }
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(bool isSmallScreen) {
    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.red.shade400, Colors.red.shade600],
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(isSmallScreen ? 16 : 20),
          topRight: Radius.circular(isSmallScreen ? 16 : 20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.report_problem_outlined,
              color: Colors.white,
              size: isSmallScreen ? 20 : 24,
            ),
          ),
          SizedBox(width: isSmallScreen ? 12 : 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Report Payment Dispute',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: isSmallScreen ? 18 : 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'Help us resolve your payment issue',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: isSmallScreen ? 12 : 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDisclaimerSection(bool isSmallScreen) {
    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.blue.shade700,
                size: isSmallScreen ? 18 : 20,
              ),
              SizedBox(width: 8),
              Text(
                'Important Information',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade800,
                  fontSize: isSmallScreen ? 14 : 16,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            '• Merchant: Select the digital wallet used for payment (Khalti, eSewa, ConnectIPS, etc.)\n'
            '• Reference Code: Found in your payment receipt after successful transaction',
            style: TextStyle(
              color: Colors.blue.shade700,
              fontSize: isSmallScreen ? 12 : 13,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMerchantDropdown({
    required TextEditingController controller,
    required bool isSmallScreen,
  }) {
    final List<String> merchants = [
      'Khalti',
      'eSewa',
      'ConnectIPS',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Payment Merchant',
              style: TextStyle(
                fontSize: isSmallScreen ? 14 : 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
            Text(
              ' *',
              style: TextStyle(
                fontSize: isSmallScreen ? 16 : 18,
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        SizedBox(height: 6),
        Text(
          'Select the digital wallet or payment method you used',
          style: TextStyle(
            fontSize: isSmallScreen ? 11 : 12,
            color: Colors.grey.shade600,
            fontStyle: FontStyle.italic,
          ),
        ),
        SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: DropdownButtonFormField<String>(
            value: controller.text.isNotEmpty ? controller.text : 'Khalti',
            decoration: InputDecoration(
              prefixIcon: Container(
                margin: EdgeInsets.all(12),
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.account_balance_wallet_outlined,
                  size: isSmallScreen ? 18 : 20,
                  color: Colors.grey.shade600,
                ),
              ),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide(color: Colors.grey.shade200),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide(color: Colors.red.shade300, width: 2),
              ),
              contentPadding: EdgeInsets.symmetric(
                vertical: isSmallScreen ? 14 : 16,
                horizontal: 16,
              ),
            ),
            items: merchants.map((String merchant) {
              return DropdownMenuItem<String>(
                value: merchant,
                child: Text(
                  merchant,
                  style: TextStyle(fontSize: isSmallScreen ? 14 : 16),
                ),
              );
            }).toList(),
            onChanged: (String? newValue) {
              if (newValue != null) {
                controller.text = newValue;
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? description,
    required IconData icon,
    TextInputType? keyboardType,
    bool isRequired = false,
    required bool isSmallScreen,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: isSmallScreen ? 14 : 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
            if (isRequired)
              Text(
                ' *',
                style: TextStyle(
                  fontSize: isSmallScreen ? 16 : 18,
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
          ],
        ),
        if (description != null) ...[
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontSize: isSmallScreen ? 11 : 12,
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: controller,
            keyboardType: keyboardType,
            style: TextStyle(fontSize: isSmallScreen ? 14 : 16),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(
                color: Colors.grey.shade400,
                fontSize: isSmallScreen ? 14 : 16,
              ),
              prefixIcon: Container(
                margin: EdgeInsets.all(12),
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: isSmallScreen ? 18 : 20,
                  color: Colors.grey.shade600,
                ),
              ),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide(color: Colors.grey.shade200),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide(color: Colors.red.shade300, width: 2),
              ),
              contentPadding: EdgeInsets.symmetric(
                vertical: isSmallScreen ? 14 : 16,
                horizontal: 16,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImportantNotice(bool isSmallScreen) {
    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning_amber_outlined,
                color: Colors.orange.shade700,
                size: isSmallScreen ? 18 : 20,
              ),
              SizedBox(width: 8),
              Text(
                'Important Notice',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange.shade800,
                  fontSize: isSmallScreen ? 14 : 16,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            'Please ensure all information matches your payment receipt exactly. '
            'False reports may result in account restrictions. Our team will '
            'review your dispute within 24-48 hours.',
            style: TextStyle(
              color: Colors.orange.shade700,
              fontSize: isSmallScreen ? 12 : 13,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons({
    required bool isSmallScreen,
    required VoidCallback onCancel,
    required VoidCallback onSubmit,
    BuildContext? context,
  }) {
    // Use MediaQuery if context is provided, otherwise fall back to isSmallScreen parameter
    bool useSmallLayout = isSmallScreen;
    double horizontalPadding = isSmallScreen ? 16.0 : 24.0;
    double verticalPadding = isSmallScreen ? 16.0 : 24.0;
    double buttonVerticalPadding = 16.0;
    double fontSize = 16.0;
    double buttonSpacing = isSmallScreen ? 12.0 : 16.0;
    double iconSize = 18.0;
    bool isTablet = false;
    bool isVerySmallScreen = false;

    if (context != null) {
      final screenWidth = MediaQuery.of(context).size.width;
      isTablet = screenWidth >= 768;
      useSmallLayout = screenWidth < 600;
      isVerySmallScreen = screenWidth < 400;

      // Dynamic padding based on screen size
      horizontalPadding =
          isVerySmallScreen ? 12.0 : (useSmallLayout ? 16.0 : 24.0);
      verticalPadding =
          isVerySmallScreen ? 12.0 : (useSmallLayout ? 16.0 : 24.0);

      // Dynamic button padding
      buttonVerticalPadding = isVerySmallScreen ? 12.0 : 16.0;

      // Dynamic font size
      fontSize = isVerySmallScreen ? 14.0 : 16.0;

      // Dynamic spacing
      buttonSpacing = useSmallLayout ? 12.0 : 16.0;
      iconSize = isVerySmallScreen ? 16.0 : 18.0;
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: verticalPadding,
      ),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: useSmallLayout
          ? Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: onSubmit,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade400,
                      foregroundColor: Colors.white,
                      padding:
                          EdgeInsets.symmetric(vertical: buttonVerticalPadding),
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.send_outlined,
                          size: iconSize,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Submit Report',
                          style: TextStyle(
                            fontSize: fontSize,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: buttonSpacing),
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: onCancel,
                    style: OutlinedButton.styleFrom(
                      padding:
                          EdgeInsets.symmetric(vertical: buttonVerticalPadding),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      side: BorderSide(color: Colors.grey.shade400),
                    ),
                    child: Text(
                      'Cancel',
                      style: TextStyle(
                        fontSize: fontSize,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ),
                ),
              ],
            )
          : Row(
              children: [
                Expanded(
                  flex: isTablet ? 1 : 1,
                  child: OutlinedButton(
                    onPressed: onCancel,
                    style: OutlinedButton.styleFrom(
                      padding:
                          EdgeInsets.symmetric(vertical: buttonVerticalPadding),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      side: BorderSide(color: Colors.grey.shade400),
                    ),
                    child: Text(
                      'Cancel',
                      style: TextStyle(
                        fontSize: fontSize,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: buttonSpacing),
                Expanded(
                  flex: isTablet ? 1 : 1,
                  child: ElevatedButton(
                    onPressed: onSubmit,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade400,
                      foregroundColor: Colors.white,
                      padding:
                          EdgeInsets.symmetric(vertical: buttonVerticalPadding),
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.send_outlined,
                          size: iconSize,
                        ),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            'Submit Report',
                            style: TextStyle(
                              fontSize: fontSize,
                              fontWeight: FontWeight.w600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  bool _validateForm(
    TextEditingController merchantController,
    TextEditingController refCodeController,
  ) {
    if (merchantController.text.isEmpty || refCodeController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please fill all required fields'),
          backgroundColor: Colors.red.shade400,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
      return false;
    }
    return true;
  }

  void _submitDispute(
    TextEditingController merchantController,
    TextEditingController refCodeController,
  ) {
    // Get the current user ID
    int userId = controller.currentUserData.value.id ?? 0;

    // Provide dummy/default values for required parameters or collect them from the UI as needed
    reportDispute(
      merchant: merchantController.text,
      refCode: refCodeController.text,
      userId: userId,
      paymentDuration: "1",
      amount: 0.0,
      paymentDate: DateTime.now(),
    );
  }
}
