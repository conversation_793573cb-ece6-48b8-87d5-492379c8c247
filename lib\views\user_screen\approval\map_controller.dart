import 'package:get/get.dart';

class MapController extends GetxController {
  final isMapLoading = false.obs;
}

// import 'package:get/get.dart';
// import 'package:flutter/material.dart';
// import 'package:smartsewa/network/services/authServices/auth_controller.dart';
// import 'package:smartsewa/views/auth/login/login_screen.dart';
// // Your auth service

// class MapController extends GetxController {
//   final isMapLoading = false.obs;

//   // Method to check if user is logged in
//   void checkLoginAndShowDetails(BuildContext context) {
//     // Replace this with your actual login check logic
//     bool isLoggedIn =
//         AuthController().isLogged(); // Assume AuthService handles login check

//     if (isLoggedIn) {
//       // If logged in, navigate to the details screen
//       Get.toNamed('/serviceProviderDetails');
//     } else {
//       // If not logged in, prompt to login
//       Get.to(LoginScreen());
//     }
//   }
// }
