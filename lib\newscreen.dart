import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/network/services/orderService/accept_request.dart';

class MyScreen extends StatefulWidget {
  const MyScreen({super.key});

  @override
  State<MyScreen> createState() => _MyScreenState();
}

class _MyScreenState extends State<MyScreen> {
  void onPressed() {}

  final controller = Get.put(AcceptServices());

  getDtae() {
    DateTime dateTime = DateTime.now();
    consolelog(dateTime);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () {
                onPressed();
              },
              child: const Text("get"),
            ),
          ],
        ),
      ),
    );
  }
}
