// import 'dart:convert';
// import 'package:get/get.dart';
// import 'package:http/http.dart' as http;
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smartsewa/core/development/console.dart';
// import '../../base_client.dart';

// class BannerImageController extends GetxController {
//   String baseUrl = BaseClient().baseUrl;
//   RxList<String> topBanners = <String>[].obs;
//   RxList<String> bottomBanners = <String>[].obs;

//   // Future<void> fetchImages() async {
//   //   final url1 = '$baseUrl/api/allimg/images';
//   //   SharedPreferences prefs = await SharedPreferences.getInstance();
//   //   String? apptoken = prefs.getString("token");

//   //   try {
//   //     final response = await http.get(
//   //       Uri.parse(url1),
//   //       headers: {
//   //         'Authorization': 'Bearer $apptoken',
//   //       },
//   //     );
//   //     consolelog('URL ${Uri.parse(url1)}');

//   //     if (response.statusCode == 200) {
//   //       final Map<String, dynamic> jsonData = json.decode(response.body);
//   //       consolelog('feteched:::::::::: Images ${json.decode(response.body)}');

//   //       if (jsonData.containsKey("topBanners")) {
//   //         topBanners.assignAll(
//   //             (jsonData["topBanners"] as List<dynamic>).cast<String>());
//   //       }

//   //       if (jsonData.containsKey("bottomBanners")) {
//   //         bottomBanners.assignAll(
//   //             (jsonData["bottomBanners"] as List<dynamic>).cast<String>());
//   //       }
//   //       saveImagesToSharedPreferences();
//   //     } else {
//   //       consolelog(
//   //           'Failed to load images. Status code: ${response.statusCode}');
//   //     }
//   //   } catch (e) {
//   //     consolelog('Error while fetching images: $e');
//   //   }
//   // }

//   // Enhanced fetchImages with better error handling
//   Future<bool> fetchImages() async {
//     try {
//       SharedPreferences prefs = await SharedPreferences.getInstance();
//       String? apptoken = prefs.getString("token");
//       final response = await http.get(
//         Uri.parse('$baseUrl/api/allimg/images'),
//         headers: {'Authorization': 'Bearer $apptoken'},
//       );

//       if (response.statusCode == 200) {
//         final jsonData = json.decode(response.body);

//         if (jsonData.containsKey("topBanners")) {
//           topBanners.assignAll((jsonData["topBanners"] as List).cast<String>());
//         }

//         if (jsonData.containsKey("bottomBanners")) {
//           bottomBanners
//               .assignAll((jsonData["bottomBanners"] as List).cast<String>());
//         }

//         await saveImagesToSharedPreferences();
//         return true;
//       }
//       return false;
//     } catch (e) {
//       consolelog('Error fetching images: $e');
//       return false;
//     }
//   }

//   Future<void> saveImagesToSharedPreferences() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     consolelog(
//         'Saving topBanners to SharedPreferences: ${topBanners.toList()}');
//     consolelog(
//         'Saving bottomBanners to SharedPreferences: ${bottomBanners.toList()}');
//     // Save topBanners and bottomBanners to SharedPreferences
//     prefs.setStringList('topBanners', topBanners.toList());
//     prefs.setStringList('bottomBanners', bottomBanners.toList());
//   }

//   Future<void> loadImagesFromSharedPreferences() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();

//     // Retrieve topBanners and bottomBanners from SharedPreferences
//     final topBannersFromPrefs = prefs.getStringList('topBanners') ?? [];
//     final bottomBannersFromPrefs = prefs.getStringList('bottomBanners') ?? [];

//     // Update the RxList properties with the loaded data
//     topBanners.assignAll(topBannersFromPrefs);
//     bottomBanners.assignAll(bottomBannersFromPrefs);
//   }
// }
import 'dart:convert';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartsewa/core/development/console.dart';
import '../../base_client.dart';

class BannerImageController extends GetxController {
  String baseUrl = BaseClient().baseUrl;
  RxList<String> topBanners = <String>[].obs;
  RxList<String> bottomBanners = <String>[].obs;
  RxBool isLoading = false.obs;
  RxBool hasError = false.obs;

  // Auto-update method - call this in initState
  Future<void> initializeBanners() async {
    try {
      await loadImagesFromSharedPreferences();
      await fetchImages();
    } catch (e) {
      consolelog('❌ Error initializing banners: $e');
      hasError.value = true;
    }
  }

  Future<void> fetchImages() async {
    final url1 = '$baseUrl/api/allimg/images';
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");

    isLoading.value = true;
    hasError.value = false;

    try {
      final response = await http.get(
        Uri.parse(url1),
        headers: {
          'Authorization': 'Bearer $apptoken',
        },
      );

      consolelog('Fetching banners from: ${Uri.parse(url1)}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        consolelog('Fresh banner data received: ${json.decode(response.body)}');

        // Store previous banners to compare
        final oldTopBanners = List<String>.from(topBanners);
        final oldBottomBanners = List<String>.from(bottomBanners);

        // Update with new data
        if (jsonData.containsKey("topBanners")) {
          topBanners.assignAll(
              (jsonData["topBanners"] as List<dynamic>).cast<String>());
        }

        if (jsonData.containsKey("bottomBanners")) {
          bottomBanners.assignAll(
              (jsonData["bottomBanners"] as List<dynamic>).cast<String>());
        }

        // Save to cache
        await saveImagesToSharedPreferences();

        // Log if banners were updated
        if (!_listsEqual(oldTopBanners, topBanners) ||
            !_listsEqual(oldBottomBanners, bottomBanners)) {
          consolelog('✅ Banners updated with fresh data from server');
        } else {
          consolelog('ℹ️ No banner changes detected');
        }
      } else {
        consolelog(
            '❌ Failed to load images. Status code: ${response.statusCode}');
        hasError.value = true;
      }
    } catch (e) {
      consolelog('❌ Error while fetching images: $e');
      hasError.value = true;
    } finally {
      isLoading.value = false;
    }
  }

  // Helper method to compare lists
  bool _listsEqual(List<String> list1, List<String> list2) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    return true;
  }

  Future<void> saveImagesToSharedPreferences() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    // Save timestamp for cache tracking
    await prefs.setInt(
        'banners_last_updated', DateTime.now().millisecondsSinceEpoch);

    // Save banner data
    await prefs.setStringList('topBanners', topBanners.toList());
    await prefs.setStringList('bottomBanners', bottomBanners.toList());

    consolelog('💾 Banners saved to cache at ${DateTime.now()}');
  }

  Future<void> loadImagesFromSharedPreferences() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    final topBannersFromPrefs = prefs.getStringList('topBanners') ?? [];
    final bottomBannersFromPrefs = prefs.getStringList('bottomBanners') ?? [];

    topBanners.assignAll(topBannersFromPrefs);
    bottomBanners.assignAll(bottomBannersFromPrefs);

    final lastUpdated = prefs.getInt('banners_last_updated') ?? 0;
    final cacheAge = DateTime.now().millisecondsSinceEpoch - lastUpdated;

    consolelog(
        '📱 Loaded ${topBanners.length} top banners and ${bottomBanners.length} bottom banners from cache');
    consolelog(
        '🕐 Cache age: ${Duration(milliseconds: cacheAge).inMinutes} minutes');
  }

  // Manual refresh method
  Future<void> refreshBanners() async {
    consolelog('🔄 Manual refresh triggered');
    await fetchImages();
  }

  // Check if cache is old (optional - for future use)
  Future<bool> isCacheOld() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final lastUpdated = prefs.getInt('banners_last_updated') ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;

    // Consider cache old if it's older than 1 hour
    return (now - lastUpdated) > (60 * 60 * 1000);
  }

  // Clear cache method (useful for debugging)
  Future<void> clearCache() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('topBanners');
    await prefs.remove('bottomBanners');
    await prefs.remove('banners_last_updated');

    topBanners.clear();
    bottomBanners.clear();

    consolelog('🗑️ Banner cache cleared');
  }
}
