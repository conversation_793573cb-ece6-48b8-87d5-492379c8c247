import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/network/services/orderService/request_service.dart';
import 'package:smartsewa/network/services/userdetails/current_user_controller.dart';
import 'package:smartsewa/views/payment_screen.dart';
import 'package:smartsewa/views/user_screen/drawer%20screen/privacypolicyscreen.dart';
import 'package:url_launcher/url_launcher.dart';
import '../user_screen/drawer screen/about_us.dart';
import '../user_screen/drawer screen/contact_screen.dart';
import 'dart:async';

// Now you can use workStatus as needed

List name = [
  //'Request Status',
  'Terms & Conditions',
  'Privacy Policy',
  'About Us',
  'Contact',
  // 'Payment',
  // 'Edit service profile'
];
List icon = [
  Icons.article,
  //Icons.book_outlined,
  Icons.privacy_tip_outlined,
  CupertinoIcons.question_circle,
  CupertinoIcons.phone,
  Icons.payment_outlined,
  //Icons.person_outline,
];
List screen = [
  // const SentRequest(),
  //const ServiceStatus(),
  const PrivacypolicyScreen(),
  null,
  const AboutUS(),
  const ContactScreen(),
  // PaymentScreen(),
  //const ServiceProfile(),
];

Future<void> urlLaunch(String url) async {
  final Uri uri = Uri.parse(url);
  if (!await launchUrl(
    uri,
    mode: LaunchMode.inAppWebView,
  )) {
    throw 'Error launching URL: $url';
  }
}

Drawer myDrawer(context, bool work) {
  Get.put(CurrentUserController());
  final orderController = Get.put(OrderController());
  int sub = work ? 0 : 1;
  consolelog(work);
  consolelog("Prajwal Log");

  return Drawer(
    backgroundColor: const Color.fromARGB(255, 255, 255, 255),
    child: Padding(
      padding: const EdgeInsets.all(10.0),
      child: Column(
        children: [
          Expanded(
            child: ListView.builder(
              itemCount: name.length - sub,
              itemBuilder: (context, index) {
                return Card(
                  color: const Color.fromARGB(240, 0, 131, 143),
                  child: ListTile(
                    onTap: () {
                      Navigator.pop(context);
                      if (index == 0) {
                        orderController.getRequestService();
                      }
                      if (screen[index] != null) {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => screen[index],
                            ));
                      } else {
                        urlLaunch('https://smartsewa.com.np/privacy-policy');
                      }
                    },
                    title: Text(
                      name[index],
                      style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                          color: Colors.white),
                    ),
                    leading: Icon(
                      icon[index],
                      size: 25,
                      color: const Color.fromARGB(255, 255, 255, 255),
                    ),
                    trailing: const Icon(
                      Icons.arrow_forward_ios_outlined,
                      color: Color.fromARGB(255, 255, 255, 255),
                      size: 20,
                    ),
                  ),
                );
              },
            ),
          ),

/*
          Obx(() {
            // logger("drawer :: ${controller.currentUserData.value.workStatus}");
            return (controller.currentUserData.value.workStatus == true &&
                    controller.currentUserData.value.approval == false)
                ? Card(
                    color: const Color.fromARGB(240, 0, 131, 143),
                    child: ListTile(
                      onTap: () {
                        Navigator.of(context).pop();
                        Get.to(() => const ServiceMainScreen());
                      },
                      title: const Text(
                        "Your Service Status",
                        style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                            color: Color.fromARGB(255, 255, 255, 255)),
                      ),
                      leading: const Icon(
                        Icons.work_history_outlined,
                        size: 25,
                        color: Color.fromARGB(255, 255, 255, 255),
                      ),
                      trailing: const Icon(
                        Icons.arrow_forward_ios_outlined,
                        color: Color.fromARGB(255, 255, 255, 255),
                      ),
                    ),
                  )
                : Container();
          })

          */

/*
          Obx(() {
            // logger("drawer :: ${controller.currentUserData.value.workStatus}");
            return controller.currentUserData.value.workStatus == false
                ? Card(
                    color: const Color.fromARGB(240, 0, 131, 143),
                    child: ListTile(
                      onTap: () {
                        Navigator.of(context).pop();
                        Get.to(() => const ServiceMainScreen());
                      },
                      title: const Text(
                        " Your Service Status",
                        style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                            color: Color.fromARGB(255, 255, 255, 255)),
                      ),
                      leading: const Icon(
                        Icons.work,
                        size: 25,
                        color: Color.fromARGB(255, 255, 255, 255),
                      ),
                      trailing: const Icon(
                        Icons.arrow_forward_ios_outlined,
                        color: Color.fromARGB(255, 255, 255, 255),
                      ),
                    ),
                  )
                : Container();
          })

          */
          // Card(
          //   child: ListTile(
          //     onTap: () {
          //       MyDialogs().myAlert(
          //           context, 'Logout!!', 'Are you sure, you want to logout?',
          //           () {
          //         Get.back();
          //       }, () {
          //         Get.offAll(const LoginScreen());
          //       });
          //     },
          //     title: const Text(
          //       "Logout",
          //       style: TextStyle(fontWeight: FontWeight.w500, fontSize: 18),
          //     ),
          //     leading: Icon(
          //       Icons.logout_outlined,
          //       size: 25,
          //       color: Theme.of(context).primaryColor,
          //     ),
          //     trailing: const Icon(
          //       Icons.arrow_forward_ios_outlined,
          //       color: Colors.black,
          //     ),
          //   ),
          // ),
        ],
      ),
    ),
  );
}
