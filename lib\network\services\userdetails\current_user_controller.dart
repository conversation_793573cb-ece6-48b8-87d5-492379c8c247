// import 'dart:convert';
// import 'dart:developer';

// import 'package:get/get.dart';
// import 'package:http/http.dart' as http;
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smartsewa/core/development/console.dart';
// import 'package:smartsewa/network/base_client.dart';

// import '../../models/currentuser_model.dart';
// import '../authServices/auth_controller.dart';

// class CurrentUserController extends GetxController {
//   final controller = Get.put(AuthController());

//   var currentUserData = CurrentUserResponseModel().obs;
//   // int? id;
//   //String? fullName;
//   //String? mobileNumber;
//   // String? address;

//   // String? email;
//   // String? imageUrlCitizenshipFront;
//   // String? imageUrlCitizenshipBack;
//   String? picture;
//   // String? latitude;
//   /// String? serviceProvided;
//   // String? serviceUsed;
//   var workStatus = false.obs;
//   // bool? role;

//   // bool? approval;
//   //bool? onlineStatus;

//   // JobDetailsResponse? jobDetailsResponse;

//   //String? dateOfBirth;
//   //String? jobTitle;
//   //String? jobField;
//   //String? firstName;
//   String? expiryDate;
//   //List<Role>? roles;
//   var isLoading = false.obs;

//   //String? password;

//   String baseUrl = BaseClient().baseUrl;
//   // @override
//   // void onInit() async {
//   //   getCurrentUser();
//   //   super.onInit();
//   // }

//   Future getCurrentUser() async {
//     isLoading.value = true;
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     String? apptoken = prefs.getString("token");
//     int? id = prefs.getInt("id");
//     log(id.toString());
//     log('Get user init $apptoken');
//     // log('get user ${controller.token}');

//     final response = await http.get(
//       Uri.parse('$baseUrl/api/users/$id'),
//       headers: <String, String>{
//         'Content-Type': 'application/json',
//         'Authorization': "Bearer $apptoken"
//       },
//     );
//     log(response.statusCode.toString());
//     if (response.statusCode == 200) {
//       isLoading.value = false;

//       var user = jsonDecode(response.body);
//       consolelog(response.body);

//       log("getCurrentUser :: $baseUrl/api/users/$id");
//       currentUserData.value = currentUserResponseModelFromJson(response.body);

//       //fullName = user['fullName'];

//       // mobileNumber = user['mobileNumber'];
//       // address = user['address'];

//       // role = user['role'];

//       picture = user['picture'];
//       expiryDate = user['expiryDate'];

//       // imageUrlCitizenshipFront = user['imageUrlCitizenshipFront'];
//       //latitude = user['latitude'];
//       workStatus.value = user['workStatus'];

//       // serviceProvided = user['serviceProvided'];
//       // approval = user['approval'];
//       // onlineStatus = user['onlineStatus'];
//       //firstName = user['firstName'];
//       // lastName = user['lastName'];
//     } else {
//       isLoading.value = false;
//     }
//   }
// }

import 'dart:convert';
import 'dart:developer';

import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/network/base_client.dart';

import '../../models/currentuser_model.dart';
import '../authServices/auth_controller.dart';

class CurrentUserController extends GetxController {
  final controller = Get.put(AuthController());

  var currentUserData = CurrentUserResponseModel().obs;
  String? picture;
  var workStatus = false.obs;
  String? expiryDate;
  var isLoading = false.obs;

  String baseUrl = BaseClient().baseUrl;

  Future getCurrentUser() async {
    isLoading.value = true;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");
    int? id = prefs.getInt("id");
    bool isGuest = prefs.getBool('isGuest') ?? false;

    log('Get user init $apptoken');
    log('User ID: $id');
    log('Is Guest: $isGuest');

    // Skip API call for guest users or when no ID is available
    if (isGuest || id == null) {
      log("Guest user or no user ID - skipping getCurrentUser API call");
      isLoading.value = false;
      return;
    }

    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/users/$id'),
        headers: <String, String>{
          'Content-Type': 'application/json',
          'Authorization': "Bearer $apptoken"
        },
      );

      log("getCurrentUser :: $baseUrl/api/users/$id");
      log(response.statusCode.toString());

      if (response.statusCode == 200) {
        var user = jsonDecode(response.body);
        consolelog(response.body);

        currentUserData.value = currentUserResponseModelFromJson(response.body);
        picture = user['picture'];
        expiryDate = user['expiryDate'];
        workStatus.value = user['workStatus'] ?? false;
      } else {
        log("Error fetching user data: ${response.statusCode}");
      }
    } catch (e) {
      log("Exception in getCurrentUser: $e");
    } finally {
      isLoading.value = false;
    }
  }
}
