// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:smartsewa/network/services/userdetails/user_edit.dart';
// import 'package:smartsewa/views/widgets/buttons/app_buttons1_left.dart';

// import '../../widgets/my_appbar.dart';
// import '../../widgets/buttons/app_buttons.dart';
// import '../../widgets/textfield_box/passwordfield.dart';

// class ChangePassword extends StatefulWidget {
//   @override
//   _ChangePasswordState createState() => _ChangePasswordState();
// }

// class _ChangePasswordState extends State<ChangePassword> {
//   final _formkey = GlobalKey<FormState>();

//   final userController = Get.put(UserEditController());
//   final TextEditingController passwordcontroller1 = TextEditingController();
//   final TextEditingController passwordcontroller2 = TextEditingController();
//   final TextEditingController passwordcontroller3 = TextEditingController();

//   bool _passwordVisible1 = false;
//   bool _passwordVisible2 = false;
//   bool _passwordVisible3 = false;

//   @override
//   Widget build(BuildContext context) {
//     Size size = MediaQuery.of(context).size;

//     return Scaffold(
//       appBar: myAppbar(context, true, "Change Password"),
//       body: Form(
//         key: _formkey,
//         child: Padding(
//           padding: EdgeInsets.all(size.aspectRatio * 40),
//           child: ListView(
//             children: [
//               Image.asset(
//                 'assets/Logo.png',
//                 height: size.height * 0.2,
//               ),
//               SizedBox(height: size.height * 0.02),
//               SizedBox(height: size.height * 0.02),
//               TextFormField(
//                 controller: passwordcontroller1,
//                 obscureText: !_passwordVisible1,
//                 decoration: InputDecoration(
//                   labelText: 'Enter Current Password',
//                   labelStyle:
//                       const TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
//                   focusedBorder: const OutlineInputBorder(
//                     borderSide:
//                         BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
//                   ),
//                   prefixIcon: const Icon(
//                     Icons.lock,
//                     color: Color.fromRGBO(0, 131, 143, 1),
//                   ),
//                   suffixIcon: IconButton(
//                     icon: Icon(
//                       _passwordVisible1
//                           ? Icons.visibility
//                           : Icons.visibility_off,
//                       color: Color.fromRGBO(0, 131, 143, 1),
//                     ),
//                     onPressed: () {
//                       setState(() {
//                         _passwordVisible1 = !_passwordVisible1;
//                       });
//                     },
//                   ),
//                 ),
//                 autovalidateMode: AutovalidateMode.onUserInteraction,
//                 validator: (value) {
//                   if (value == null || value.isEmpty) {
//                     return 'Please enter your current password';
//                   }
//                   return null;
//                 },
//               ),
//               SizedBox(height: size.height * 0.02),
//               TextFormField(
//                 controller: passwordcontroller2,
//                 obscureText: !_passwordVisible2,
//                 decoration: InputDecoration(
//                   labelText: 'Enter New Password',
//                   labelStyle:
//                       const TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
//                   focusedBorder: const OutlineInputBorder(
//                     borderSide:
//                         BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
//                   ),
//                   prefixIcon: const Icon(
//                     Icons.lock,
//                     color: Color.fromRGBO(0, 131, 143, 1),
//                   ),
//                   suffixIcon: IconButton(
//                     icon: Icon(
//                       _passwordVisible2
//                           ? Icons.visibility
//                           : Icons.visibility_off,
//                       color: Color.fromRGBO(0, 131, 143, 1),
//                     ),
//                     onPressed: () {
//                       setState(() {
//                         _passwordVisible2 = !_passwordVisible2;
//                       });
//                     },
//                   ),
//                 ),
//                 autovalidateMode: AutovalidateMode.onUserInteraction,
//                 validator: (value) {
//                   if (value == null || value.isEmpty) {
//                     return 'Please enter your new password';
//                   } else if (!RegExp(
//                           r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$')
//                       .hasMatch(value)) {
//                     return 'Include one uppercase&lowercase letter,number,specialcharacter';
//                   }
//                   return null;
//                 },
//               ),
//               SizedBox(height: size.height * 0.02),
//               TextFormField(
//                 controller: passwordcontroller3,
//                 obscureText: !_passwordVisible3,
//                 decoration: InputDecoration(
//                   labelText: 'Confirm New Password',
//                   labelStyle:
//                       const TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
//                   focusedBorder: const OutlineInputBorder(
//                     borderSide:
//                         BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
//                   ),
//                   prefixIcon: const Icon(
//                     Icons.lock,
//                     color: Color.fromRGBO(0, 131, 143, 1),
//                   ),
//                   suffixIcon: IconButton(
//                     icon: Icon(
//                       _passwordVisible3
//                           ? Icons.visibility
//                           : Icons.visibility_off,
//                       color: Color.fromRGBO(0, 131, 143, 1),
//                     ),
//                     onPressed: () {
//                       setState(() {
//                         _passwordVisible3 = !_passwordVisible3;
//                       });
//                     },
//                   ),
//                 ),
//                 autovalidateMode: AutovalidateMode.onUserInteraction,
//                 validator: (value) {
//                   if (value == null || value.isEmpty) {
//                     return 'Please confirm your new password';
//                   } else if (value != passwordcontroller2.text) {
//                     return 'New Password and Confirm New Password do not match';
//                   }
//                   return null;
//                 },
//               ),
//               SizedBox(height: size.height * 0.02),
//               SizedBox(height: size.height * 0.02),
//               SizedBox(height: size.height * 0.02),
//               SizedBox(height: size.height * 0.02),
//               Center(
//                 child: AppButton(
//                   name: 'Continue',
//                   onPressed: () {
//                     String currentPassword = passwordcontroller1.text;
//                     String newPassword = passwordcontroller2.text;
//                     String confirmPassword = passwordcontroller3.text;
//                     if (currentPassword.isEmpty ||
//                         newPassword.isEmpty ||
//                         confirmPassword.isEmpty) {
//                       Get.snackbar(
//                         "Error",
//                         "All fields must not be empty",
//                         snackPosition: SnackPosition.TOP,
//                         backgroundColor: Colors.red,
//                       );
//                     } else if (newPassword != confirmPassword) {
//                       Get.snackbar(
//                         "Error",
//                         "New and Confirm New Password don't match",
//                         snackPosition: SnackPosition.TOP,
//                         backgroundColor: Colors.red,
//                       );
//                     } else {
//                       Get.dialog(
//                         AlertDialog(
//                           title: const Text("Confirmation"),
//                           content: const Text(
//                             "Are you sure you want to change your password? You will be redirected to the Login Screen",
//                           ),
//                           actions: [
//                             TextButton(
//                               child: const Text('No'),
//                               onPressed: () {
//                                 Get.back();
//                               },
//                             ),
//                             TextButton(
//                               child: const Text('Yes'),
//                               onPressed: () {
//                                 userController.userChangePassword(
//                                   currentPassword,
//                                   newPassword,
//                                   confirmPassword,
//                                 );
//                               },
//                             ),
//                           ],
//                         ),
//                       );
//                     }
//                   },
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:smartsewa/network/services/userdetails/user_edit.dart';
import 'package:smartsewa/views/widgets/buttons/app_buttons1_left.dart';

import '../../widgets/my_appbar.dart';
import '../../widgets/buttons/app_buttons.dart';
import '../../widgets/textfield_box/passwordfield.dart';

class ChangePassword extends StatefulWidget {
  @override
  _ChangePasswordState createState() => _ChangePasswordState();
}

class _ChangePasswordState extends State<ChangePassword> {
  final _formkey = GlobalKey<FormState>();

  final userController = Get.put(UserEditController());
  final TextEditingController passwordcontroller1 = TextEditingController();
  final TextEditingController passwordcontroller2 = TextEditingController();
  final TextEditingController passwordcontroller3 = TextEditingController();

  bool _passwordVisible1 = false;
  bool _passwordVisible2 = false;
  bool _passwordVisible3 = false;

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: myAppbar(context, true, "Change Password"),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: size.width * 0.08,
            vertical: size.height * 0.05,
          ),
          child: Container(
            padding: const EdgeInsets.all(20.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(32.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.4),
                  spreadRadius: 4,
                  blurRadius: 10,
                  offset: Offset(0, 3),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Text(
                  "Update Your Password",
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color.fromRGBO(0, 131, 143, 1),
                  ),
                ),
                SizedBox(height: size.height * 0.03),

                // Current Password Field
                _buildPasswordField(
                  controller: passwordcontroller1,
                  label: 'Enter Current Password',
                  isVisible: _passwordVisible1,
                  onVisibilityToggle: () {
                    setState(() {
                      _passwordVisible1 = !_passwordVisible1;
                    });
                  },
                ),
                SizedBox(height: size.height * 0.02),

                // New Password Field
                _buildPasswordField(
                  controller: passwordcontroller2,
                  label: 'Enter New Password',
                  isVisible: _passwordVisible2,
                  onVisibilityToggle: () {
                    setState(() {
                      _passwordVisible2 = !_passwordVisible2;
                    });
                  },
                ),
                SizedBox(height: size.height * 0.02),

                // Confirm New Password Field
                _buildPasswordField(
                  controller: passwordcontroller3,
                  label: 'Confirm New Password',
                  isVisible: _passwordVisible3,
                  onVisibilityToggle: () {
                    setState(() {
                      _passwordVisible3 = !_passwordVisible3;
                    });
                  },
                ),
                SizedBox(height: size.height * 0.04),

                // Continue Button
                SizedBox(
                  width: double.infinity,
                  child: AppButton(
                    name: 'Update Password',
                    onPressed: () {
                      String currentPassword = passwordcontroller1.text;
                      String newPassword = passwordcontroller2.text;
                      String confirmPassword = passwordcontroller3.text;
                      if (currentPassword.isEmpty ||
                          newPassword.isEmpty ||
                          confirmPassword.isEmpty) {
                        Get.snackbar(
                          "Error",
                          "All fields must not be empty",
                          snackPosition: SnackPosition.TOP,
                          backgroundColor: Colors.red,
                        );
                      } else if (newPassword != confirmPassword) {
                        Get.snackbar(
                          "Error",
                          "New and Confirm New Password don't match",
                          snackPosition: SnackPosition.TOP,
                          backgroundColor: Colors.red,
                        );
                      } else {
                        Get.dialog(
                          AlertDialog(
                            title: const Text("Confirmation"),
                            content: const Text(
                              "Are you sure you want to change your password? You will be redirected to the Login Screen.",
                            ),
                            actions: [
                              TextButton(
                                child: const Text('No'),
                                onPressed: () {
                                  Get.back();
                                },
                              ),
                              TextButton(
                                child: const Text('Yes'),
                                onPressed: () {
                                  userController.userChangePassword(
                                    currentPassword,
                                    newPassword,
                                    confirmPassword,
                                  );
                                },
                              ),
                            ],
                          ),
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Password Field Widget
  Widget _buildPasswordField({
    required TextEditingController controller,
    required String label,
    required bool isVisible,
    required VoidCallback onVisibilityToggle,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: !isVisible,
      decoration: InputDecoration(
        labelText: label,
        labelStyle: const TextStyle(
          color: Color.fromRGBO(0, 131, 143, 1),
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.grey.shade400),
        ),
        prefixIcon: const Icon(
          Icons.lock_outline,
          color: Color.fromRGBO(0, 131, 143, 1),
        ),
        suffixIcon: IconButton(
          icon: Icon(
            isVisible ? Icons.visibility : Icons.visibility_off,
            color: Color.fromRGBO(0, 131, 143, 1),
          ),
          onPressed: onVisibilityToggle,
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter your password';
        }
        return null;
      },
    );
  }
}
