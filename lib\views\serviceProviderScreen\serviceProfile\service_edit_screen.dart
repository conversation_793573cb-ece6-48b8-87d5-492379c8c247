import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartsewa/views/serviceProviderScreen/serviceProfile/service_provider_profile.dart';
import 'package:smartsewa/views/user_screen/main_screen.dart';
import 'package:smartsewa/views/user_screen/profile/profile.dart';
import 'package:smartsewa/views/utils.dart';

import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:image_picker/image_picker.dart';

import 'package:smartsewa/network/services/authServices/auth_controller.dart';
import 'package:smartsewa/views/serviceProviderScreen/service_main_screen.dart';
import 'package:smartsewa/views/user_screen/approval/map_controller.dart';
import 'package:smartsewa/views/user_screen/approval/open_map_screen.dart';
import 'package:smartsewa/views/widgets/custom_dialogs.dart';

import 'package:smartsewa/views/widgets/custom_toasts.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';
import '../../widgets/buttons/app_buttons.dart';

import '../../../core/development/console.dart';
import '../../../network/services/userdetails/edit_service.dart';
import '../../../network/services/userdetails/current_user_controller.dart';

class ServiceProviderSettingPage extends StatefulWidget {
  ServiceProviderSettingPage({super.key});
  final mapController = Get.put(MapController());

  @override
  State<ServiceProviderSettingPage> createState() =>
      _ServiceProviderSettingPageState();
}

class _ServiceProviderSettingPageState
    extends State<ServiceProviderSettingPage> {
  final TextEditingController _skillController = TextEditingController();
  final TextEditingController _workingaddressController =
      TextEditingController();

  final userController = Get.put((ServiceProviderController()));
  final authController = Get.put(AuthController());
  CurrentUserController storeController = Get.put(CurrentUserController());

  Position? position;
  int count = 0, count1 = 0;
  String? citizenshipFrontUrl;
  String? citizenshipBackUrl;
  String? drivingLicenseUrl;
  String? nationalIdUrl;
  String? latitude;
  String? categoryID;
  String? ServiceID;
  bool isDocumentApproved = true;
  int a = 0;
  bool isLoading = false;
  bool hasInitialDataLoaded = false;

  @override
  void initState() {
    super.initState();
    checkDocumentStatus();
    _loadInitialData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh data when returning to this screen
    if (hasInitialDataLoaded) {
      _refreshData();
    }
  }

  Future<void> _refreshData() async {
    // Refresh current user data
    await storeController.getCurrentUser();

    // Reload services data
    await _loadExistingServices();

    // Check document status again
    await checkDocumentStatus();
  }

  Future<void> _loadInitialData() async {
    setState(() => isLoading = true);
    try {
      await checkDocumentStatus();

      // First try to load existing services with more robust approach
      bool servicesLoaded = await _loadExistingServices();

      // If services couldn't be loaded, try alternative approach
      if (!servicesLoaded) {
        await _loadExistingServicesAlternative();
      }
    } catch (e) {
      errorToast(msg: 'Error loading initial data: $e');
    } finally {
      setState(() {
        isLoading = false;
        hasInitialDataLoaded = true;
      });
    }
  }

  Future<bool> _loadExistingServices() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? apptoken = prefs.getString("token");
      int? id = prefs.getInt("id");

      if (apptoken == null || id == null) {
        return false;
      }

      try {
        var response = await http.get(
          Uri.parse('$baseUrl/api/user/$id/servicesID'),
          headers: <String, String>{
            'Authorization': 'Bearer $apptoken',
          },
        );

        if (response.statusCode == 200) {
          var data = response.body;
          data = data.replaceAll("'", '"');

          try {
            final Map<String, dynamic> parsedData = json.decode(data);

            if (parsedData.containsKey('Service ID') &&
                parsedData.containsKey('Category Id')) {
              var serviceIds = parsedData['Service ID'];
              var categoryId = parsedData['Category Id'];

              List<int> serviceIdList = [];

              // Parse service IDs
              if (serviceIds is List) {
                serviceIdList = List<int>.from(serviceIds);
              } else {
                String serviceIdsStr = serviceIds.toString();
                serviceIdsStr =
                    serviceIdsStr.replaceAll('[', '').replaceAll(']', '');

                if (serviceIdsStr.isNotEmpty) {
                  serviceIdList = serviceIdsStr
                      .split(',')
                      .map((s) => int.parse(s.trim()))
                      .toList();
                }
              }

              if (serviceIdList.isNotEmpty) {
                // Fetch category name
                String categoryName =
                    await fetchCategoryName(int.parse(categoryId.toString()));

                // Fetch service names - this is the key part
                List<String> serviceNames =
                    await fetchServiceNames(serviceIdList);

                // Debug logs
                consolelog("Category: $categoryName");
                consolelog("Service IDs: $serviceIdList");
                consolelog("Service Names: $serviceNames");

                setState(() {
                  selectedSkills =
                      serviceIdList.map((id) => id.toString()).toList();

                  // Check if we got actual names or just IDs
                  bool hasRealNames = !serviceNames
                      .every((name) => name.startsWith("Service "));

                  if (hasRealNames) {
                    _skillController.text =
                        '$categoryName: ${serviceNames.join(', ')}';
                  } else {
                    // If we couldn't get real names, try to improve the display
                    _skillController.text =
                        '$categoryName: ${serviceNames.join(', ')}';

                    // Schedule an async task to try fetching names again
                    Future.delayed(Duration.zero, () async {
                      try {
                        // Try a different API endpoint or approach
                        // This is just an example - adjust based on your API
                        final response = await http.get(
                          Uri.parse(
                              '$baseUrl/api/categories/$categoryId/all-services'),
                          headers: <String, String>{
                            'Authorization': 'Bearer $apptoken',
                          },
                        );

                        if (response.statusCode == 200) {
                          var data = json.decode(response.body);
                          consolelog("Alternative API response: $data");

                          // Process the data and update UI if successful
                          // ...
                        }
                      } catch (e) {
                        consolelog("Error in delayed service name fetch: $e");
                      }
                    });
                  }

                  selectedJobCategoryId = categoryId.toString();
                });

                return true;
              }
            }
          } catch (e) {
            consolelog("Error parsing service data: $e");
          }
        }

        // Fallback logic if needed

        return false;
      } catch (e) {
        consolelog("Error loading services: $e");
        return false;
      }
    } catch (e) {
      errorToast(msg: 'Error loading services: $e');
      return false;
    }
  }

  // Add an alternative method to load services from user profile
  Future<bool> _loadExistingServicesAlternative() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");
    int? id = prefs.getInt("id");

    if (apptoken == null || id == null) {
      return false;
    }

    try {
      // Try to get user profile data which might contain service info
      var response = await http.get(
        Uri.parse('$baseUrl/api/users/$id'),
        headers: <String, String>{
          'Authorization': 'Bearer $apptoken',
        },
      );

      if (response.statusCode == 200) {
        var userData = json.decode(response.body);

        // Check if user has service provider data
        if (userData.containsKey('categoryId') &&
            userData.containsKey('services')) {
          var categoryId = userData['categoryId'];
          var services = userData['services'];

          if (categoryId != null && services != null) {
            List<String> serviceIds = [];

            // Handle different service formats
            if (services is List) {
              serviceIds = services.map((s) => s['id'].toString()).toList();
            } else if (services is Map) {
              serviceIds = services.values.map((s) => s.toString()).toList();
            } else if (services is String) {
              serviceIds = services.split(',').map((s) => s.trim()).toList();
            }

            if (serviceIds.isNotEmpty) {
              String categoryName =
                  await fetchCategoryName(int.parse(categoryId.toString()));
              List<String> serviceNames = await fetchServiceNames(
                  serviceIds.map((id) => int.parse(id)).toList());

              setState(() {
                selectedSkills = serviceIds;
                _skillController.text =
                    '$categoryName: ${serviceNames.join(', ')}';
                selectedJobCategoryId = categoryId.toString();
              });
              return true;
            }
          }
        }

        // Try another format that might be in the response
        if (userData.containsKey('serviceProvided')) {
          var serviceProvided = userData['serviceProvided'];
          if (serviceProvided != null && serviceProvided is Map) {
            if (serviceProvided.containsKey('categoryId') &&
                serviceProvided.containsKey('serviceIds')) {
              var categoryId = serviceProvided['categoryId'];
              var serviceIds = serviceProvided['serviceIds'];

              if (categoryId != null && serviceIds != null) {
                List<String> serviceIdList = [];

                if (serviceIds is List) {
                  serviceIdList = serviceIds.map((s) => s.toString()).toList();
                } else if (serviceIds is String) {
                  serviceIdList =
                      serviceIds.split(',').map((s) => s.trim()).toList();
                }

                if (serviceIdList.isNotEmpty) {
                  String categoryName =
                      await fetchCategoryName(int.parse(categoryId.toString()));
                  List<String> serviceNames = await fetchServiceNames(
                      serviceIdList.map((id) => int.parse(id)).toList());

                  setState(() {
                    selectedSkills = serviceIdList;
                    _skillController.text =
                        '$categoryName: ${serviceNames.join(', ')}';
                    selectedJobCategoryId = categoryId.toString();
                  });
                  return true;
                }
              }
            }
          }
        }
      }

      // If we couldn't get data from user profile, try the admin endpoint
      response = await http.get(
        Uri.parse('$baseUrl/api/admin/users/$id'),
        headers: <String, String>{
          'Authorization': 'Bearer $apptoken',
        },
      );

      if (response.statusCode == 200) {
        var userData = json.decode(response.body);

        // Process similar to above, checking for service provider data
        if (userData.containsKey('categoryId') &&
            userData.containsKey('services')) {
          // Similar processing as above
          // ...
        }
      }

      return false;
    } catch (e) {
      consolelog("Error in alternative service loading: $e");
      return false;
    }
  }

  Future<String> fetchServiceCategory() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");
    int? id = prefs.getInt("id");

    if (apptoken == null || id == null) {
      return "Error Fetching Data";
    }

    try {
      // Try the regular endpoint first
      var response = await http.get(
        Uri.parse('$baseUrl/api/user/$id/servicesID'),
        headers: <String, String>{
          'Authorization': 'Bearer $apptoken',
        },
      );

      if (response.statusCode == 200) {
        return (response.body);
      }

      // If first endpoint fails, try the admin endpoint as fallback
      response = await http.get(
        Uri.parse('$baseUrl/api/user/$id/admin/servicesID'),
        headers: <String, String>{
          'Authorization': 'Bearer $apptoken',
        },
      );

      if (response.statusCode == 200) {
        return (response.body);
      }

      consolelog('Error: ${response.statusCode} - ${response.reasonPhrase}');
      return "Error Fetching Data";
    } catch (err) {
      if (err.toString().contains('SocketException')) {
        consolelog('No internet connection');
      } else {
        consolelog('Error: $err');
      }
      return "Error Fetching Data";
    }
  }

  Future<void> checkDocumentStatus() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    latitude = prefs.getString("latitude");
    if (latitude != null) {
      _workingaddressController.text = latitude.toString();
    }

    consolelog(latitude);
    String? apptoken = prefs.getString("token");
    int? id = prefs.getInt("id");

    if (apptoken == null || id == null) {
      errorToast(msg: "User not logged in or invalid ID.");
      return;
    }

    try {
      var headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apptoken',
      };

      var response = await http.get(
        Uri.parse('$baseUrl/api/users/$id/documentCheck'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        var responseData = json.decode(response.body);

        // Handle both boolean and object responses
        if (responseData is bool) {
          setState(() {
            isDocumentApproved = responseData;
          });

          if (responseData) {
            successToast(msg: "Documents are approved");
          } else {
            warningToast(msg: "Documents need to be uploaded");
          }
        } else if (responseData is Map<String, dynamic>) {
          setState(() {
            isDocumentApproved = responseData['status'] == 'approved';
          });

          switch (responseData['status']) {
            case 'approved':
              successToast(msg: "Documents are approved");
              break;
            case 'pending':
              warningToast(msg: "Documents are pending verification");
              break;
            case 'rejected':
              errorToast(msg: "Documents were rejected");
              break;
            default:
              warningToast(msg: "Document status: ${responseData['status']}");
          }
        }
      } else {
        throw Exception(
            'Failed to check document status: ${response.statusCode}');
      }
    } catch (e) {
      errorToast(msg: 'Error checking document status: $e');
    }
  }

  Future<void> editServiceProfile(
    File? citizenshipFront,
    File? citizenshipBack,
    File? drivingLicense,
    File? nationalId,
  ) async {
    setState(() => isLoading = true);

    try {
      // Validate required fields
      if (_skillController.text.isEmpty) {
        throw Exception('Please select your services');
      }
      if (_workingaddressController.text.isEmpty) {
        throw Exception('Please select your working address');
      }

      // Extract category ID from stored selectedJobCategoryId
      String categoryId = selectedJobCategoryId ?? '';

      // Use the stored selectedSkills list which contains IDs
      List<int> serviceIds = selectedSkills.map((e) => int.parse(e)).toList();

      // Handle document uploads
      if (!isDocumentApproved) {
        if (count == 0) {
          throw Exception('Please select at least one document');
        }

        if (count == 1 &&
            (citizenshipFront == null || citizenshipBack == null)) {
          throw Exception('Please select both sides of citizenship');
        }

        // Upload documents
        if (citizenshipFront != null && citizenshipBack != null) {
          var frontResult =
              await uploadImage('citizenshipFront', citizenshipFront);
          citizenshipFrontUrl = frontResult['citizenshipFront'];

          var backResult =
              await uploadImage('citizenshipBack', citizenshipBack);
          citizenshipBackUrl = backResult['citizenshipBack'];
        } else if (drivingLicense != null) {
          var result = await uploadImage('drivingLicense', drivingLicense);
          citizenshipFrontUrl = result['drivingLicense'];
          citizenshipBackUrl = null;
        } else if (nationalId != null) {
          var result = await uploadImage('nationalId', nationalId);
          citizenshipFrontUrl = result['nationalId'];
          citizenshipBackUrl = null;
        }
      }

      // Make update API call
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? apptoken = prefs.getString("token");
      int? id = prefs.getInt("id");

      if (apptoken == null || id == null) {
        throw Exception('User not logged in');
      }

      // First, try the standard update endpoint
      final url = Uri.parse(
        '$baseUrl/api/user/update/$id/$categoryId/${Uri.encodeComponent(_workingaddressController.text)}/${citizenshipFrontUrl ?? "null"}/${citizenshipBackUrl ?? "null"}',
      );

      final response = await http.put(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apptoken',
        },
        body: json.encode(serviceIds),
      );

      if (response.statusCode == 200) {
        // Update was successful, now also update the service provider profile
        await _updateServiceProviderProfile(categoryId, serviceIds);

        // Save latitude to preferences
        await prefs.setString("latitude", _workingaddressController.text);

        // Update current user data
        await storeController.getCurrentUser();

        successToast(msg: "Profile updated successfully");
        Get.back();
      } else {
        // If the first endpoint fails, try an alternative endpoint
        final alternativeUrl = Uri.parse(
          '$baseUrl/api/users/$id/update-services',
        );

        final alternativeResponse = await http.put(
          alternativeUrl,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $apptoken',
          },
          body: json.encode({
            'categoryId': categoryId,
            'serviceIds': serviceIds,
            'workingAddress': _workingaddressController.text,
            'documentUrl': citizenshipFrontUrl,
            'documentBackUrl': citizenshipBackUrl,
          }),
        );

        if (alternativeResponse.statusCode == 200) {
          // Update was successful with alternative endpoint
          await _updateServiceProviderProfile(categoryId, serviceIds);

          // Save latitude to preferences
          await prefs.setString("latitude", _workingaddressController.text);

          // Update current user data
          await storeController.getCurrentUser();

          successToast(msg: "Profile updated successfully");
          Get.back();
        } else {
          throw Exception('Failed to update profile. Please try again.');
        }
      }
    } catch (e) {
      errorToast(msg: e.toString());
    } finally {
      setState(() => isLoading = false);
    }
  }

  // Add a helper method to update service provider profile
  Future<void> _updateServiceProviderProfile(
      String categoryId, List<int> serviceIds) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");
    int? id = prefs.getInt("id");

    if (apptoken == null || id == null) {
      return;
    }

    try {
      // Try to update the service provider profile specifically
      final url = Uri.parse('$baseUrl/api/service-providers/$id');

      final response = await http.put(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apptoken',
        },
        body: json.encode({
          'categoryId': categoryId,
          'serviceIds': serviceIds,
        }),
      );

      if (response.statusCode == 200) {
        consolelog("Service provider profile updated successfully");
      } else {
        consolelog(
            "Failed to update service provider profile: ${response.statusCode}");

        // Try alternative endpoint
        final alternativeUrl =
            Uri.parse('$baseUrl/api/users/$id/service-provider');

        final alternativeResponse = await http.put(
          alternativeUrl,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $apptoken',
          },
          body: json.encode({
            'categoryId': categoryId,
            'serviceIds': serviceIds,
          }),
        );

        if (alternativeResponse.statusCode == 200) {
          consolelog(
              "Service provider profile updated with alternative endpoint");
        } else {
          consolelog(
              "Failed to update with alternative endpoint: ${alternativeResponse.statusCode}");
        }
      }
    } catch (e) {
      consolelog("Error updating service provider profile: $e");
    }
  }

//////////////////////////////for documents upload/////////////////////////////
  File? citizenshipFront;
  File? citizenshipBack;
  File? drivingLicense;
  File? nationalId;

  final List<File?> _pickedImages = [];

  Future<void> _pickImage(ImageSource source, String documentType) async {
    final picker = ImagePicker();

    try {
      if (documentType == 'citizenship') {
        final selectedSide = await _showCitizenshipSideDialog();
        if (selectedSide == null) {
          return;
        }
        if (selectedSide == 'front') {
          documentType = 'citizenship_front';
          final selectedSource =
              await _showImageSourceDialog("Select Front Side ");
          if (selectedSource == null) {
            return;
          }
          source = selectedSource;
        } else {
          documentType = 'citizenship_back';
          final selectedSource =
              await _showImageSourceDialog("Select Back Side ");
          if (selectedSource == null) {
            return;
          }
          source = selectedSource;
        }
      } else if (documentType == 'driving_license' ||
          documentType == 'national_id') {
        final selectedSource = await _showImageSourceDialog("Select Image");
        if (selectedSource == null) {
          return;
        }
        source = selectedSource;
      }

      final pickedFile = await picker.pickImage(source: source);

      if (pickedFile != null) {
        final File pickedImage = File(pickedFile.path);
        final File? compressedImage = await compressImage(pickedImage);

        if (compressedImage != null) {
          setState(() {
            switch (documentType) {
              case 'citizenship_front':
                citizenshipFront = compressedImage;
                documentController.text = "Citizenship Front Selected";
                count = 1;
                break;
              case 'citizenship_back':
                citizenshipBack = compressedImage;
                documentController.text = "Citizenship  Selected";
                count = 1;
                break;
              case 'driving_license':
                drivingLicense = compressedImage;
                documentController.text = "Driving License  Selected";
                count = 2;
                break;
              case 'national_id':
                nationalId = compressedImage;
                documentController.text = "National Id  Selected";
                count = 3;

                break;
              default:
                break;
            }

            final side = documentType.split('_').last;
            final message = 'Successfully Selected $side side';
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(message)),
            );

            Future.delayed(const Duration(seconds: 1), () {
              if (documentType == 'citizenship_front') {
                _pickImage(source, 'citizenship');
              }
            });
          });
          consolelog('Selected Document Type: $documentType');
        } else {
          consolelog('Image compression failed');
        }
      } else {
        consolelog('No image selected');
      }
    } catch (error) {
      consolelog('Error picking image: $error');
    }
  }

  Future<ImageSource?> _showImageSourceDialog(String title) async {
    return await showDialog<ImageSource>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          actions: [
            ListTile(
              leading: const Icon(Icons.camera),
              title: const Text('Take a Picture'),
              onTap: () {
                Navigator.pop(context, ImageSource.camera);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo),
              title: const Text('Choose from Gallery'),
              onTap: () {
                Navigator.pop(context, ImageSource.gallery);
              },
            ),
          ],
        );
      },
    );
  }

  Future<String?> _showCitizenshipSideDialog() async {
    bool isBackEnabled = citizenshipFront != null;

    return await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Citizenship Side'),
          actions: [
            ListTile(
              title: const Text('Front'),
              enabled: citizenshipFront == null,
              onTap: () {
                if (citizenshipFront != null) {
                  errorToast(msg: "Already Selected (Front)");
                } else {
                  Navigator.pop(context, 'front');
                }
              },
            ),
            ListTile(
              title: const Text('Back'),
              enabled: isBackEnabled,
              onTap: () async {
                if (isBackEnabled) {
                  final selectedSource =
                      await _showImageSourceDialog("Select Back Side ");
                  if (selectedSource != null) {
                    Navigator.pop(context, 'back');
                  }
                }
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return Scaffold(
      appBar: myAppbar(context, true, "Edit Service Profile"),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(
                  16.0), // General padding for cleaner layout
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(height: size.height * 0.02),

                    // Enhanced Form Container with more contrast
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16.0, vertical: 20.0),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius:
                            BorderRadius.circular(12.0), // Rounded corners
                        border: Border.all(
                          color: Colors.teal[800]!
                              .withOpacity(0.6), // Darker border for contrast
                          width: 1.5,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey
                                .withOpacity(0.2), // More shadow for depth
                            spreadRadius: 2,
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          buildSkillsDropdown(), // Skill Dropdown inside container
                          SizedBox(height: size.height * 0.03),

                          // Working Address Section
                          // Row(
                          //   children: [
                          //     Expanded(
                          //       child: TextFormField(
                          //         controller: _workingaddressController,
                          //         readOnly: true,
                          //         // enabled: false,
                          //         // initialValue: storeController
                          //         //.currentUserData.value.latitude
                          //         //.toString(),
                          //         decoration: InputDecoration(
                          //           labelText:
                          //               'Choose your working address on Map',
                          //           labelStyle: const TextStyle(
                          //             color: Color.fromRGBO(0, 131, 143, 1),
                          //             fontWeight: FontWeight.bold,
                          //             fontSize: 16.0,
                          //           ),
                          //           // initialValue: storeController
                          //           //.currentUserData.value.latitude
                          //           //.toString(),
                          //           focusedBorder: OutlineInputBorder(
                          //             borderSide: const BorderSide(
                          //               color: Color.fromRGBO(0, 131, 143, 1),
                          //               width: 2.0,
                          //             ),
                          //             borderRadius: BorderRadius.circular(10.0),
                          //           ),
                          //           enabledBorder: OutlineInputBorder(
                          //             borderSide: const BorderSide(
                          //               color: Color.fromRGBO(0, 131, 143, 0.5),
                          //               width: 1.5,
                          //             ),
                          //             borderRadius: BorderRadius.circular(10.0),
                          //           ),
                          //         ),
                          //         autovalidateMode:
                          //             AutovalidateMode.onUserInteraction,
                          //         validator: (value) {
                          //           if (value == null || value.isEmpty) {
                          //             return 'Please enter your working address';
                          //           }
                          //           return null;
                          //         },
                          //       ),
                          //     ),
                          //     const SizedBox(width: 12.0),
                          //     ElevatedButton.icon(
                          //       onPressed: () async {
                          //         permission =
                          //             await Geolocator.requestPermission();
                          //         if (permission ==
                          //             LocationPermission.deniedForever) {
                          //           await Geolocator.openAppSettings();
                          //         } else if (permission !=
                          //                 LocationPermission.denied &&
                          //             permission !=
                          //                 LocationPermission.deniedForever) {
                          //           widget.mapController.isMapLoading.value =
                          //               true;
                          //           position =
                          //               await Geolocator.getCurrentPosition(
                          //                   desiredAccuracy:
                          //                       LocationAccuracy.high);

                          //           LatLng selectedLocation =
                          //               await Get.to(() => OpenMapScreen(
                          //                     completeGoogleMapController:
                          //                         completeGoogleMapController,
                          //                     kGoogle: kGoogle,
                          //                     marker: marker,
                          //                     onpressed: onpressed,
                          //                     onLocationSelected:
                          //                         (LatLng location) {
                          //                       String formattedLocation =
                          //                           '${location.latitude},${location.longitude}';
                          //                       _workingaddressController.text =
                          //                           formattedLocation;
                          //                     },
                          //                   ));
                          //         }
                          //       },
                          //       icon: const Icon(Icons.map,
                          //           color: Colors.white, size: 18),
                          //       label: const Text('Open Map'),
                          //       style: ElevatedButton.styleFrom(
                          //         backgroundColor:
                          //             const Color.fromRGBO(0, 131, 143, 1),
                          //         shape: RoundedRectangleBorder(
                          //           borderRadius: BorderRadius.circular(10.0),
                          //         ),
                          //         minimumSize:
                          //             const Size(100, 40), // Reduced size
                          //       ),
                          //     ),
                          //   ],
                          // ),

                          buildWorkingAddressSection(size),
                          SizedBox(height: size.height * 0.03),
                          Row(
                            children: [
                              Expanded(
                                child: _pickedImages.isEmpty
                                    ? TextFormField(
                                        controller: documentController,
                                        enabled:
                                            !isDocumentApproved, // Disable when documents are approved
                                        decoration: InputDecoration(
                                          labelText: isDocumentApproved
                                              ? 'Documents already approved'
                                              : 'Upload your documents',
                                          labelStyle: TextStyle(
                                            color: isDocumentApproved
                                                ? Colors.grey
                                                : const Color.fromRGBO(
                                                    0, 131, 143, 1),
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16.0,
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                              color: isDocumentApproved
                                                  ? Colors.grey
                                                  : const Color.fromRGBO(
                                                      0, 131, 143, 1),
                                              width: 2.0,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(10.0),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                              color: isDocumentApproved
                                                  ? Colors.grey
                                                  : const Color.fromRGBO(
                                                      0, 131, 143, 0.5),
                                              width: 1.5,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(10.0),
                                          ),
                                        ),
                                      )
                                    : Container(),
                              ),
                              const SizedBox(width: 16.0),
                              if (!isDocumentApproved) // Only show upload button if documents aren't approved
                                InkWell(
                                  onTap: () {
                                    count = 0;
                                    count1 = 0;
                                    citizenshipBack = null;
                                    citizenshipBackUrl = null;
                                    citizenshipFrontUrl = null;
                                    drivingLicenseUrl = null;
                                    nationalIdUrl = null;
                                    citizenshipFront = null;
                                    documentController.text = "";
                                    drivingLicense = null;
                                    nationalId = null;

                                    showModalBottomSheet(
                                      context: context,
                                      builder: (BuildContext context) {
                                        return Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: <Widget>[
                                            ListTile(
                                              title: const Text('Citizenship'),
                                              onTap: () {
                                                Navigator.pop(context);
                                                _pickImage(ImageSource.gallery,
                                                    'citizenship');
                                              },
                                            ),
                                            ListTile(
                                              title:
                                                  const Text('Driving License'),
                                              onTap: () {
                                                Navigator.pop(context);
                                                _pickImage(ImageSource.gallery,
                                                    'driving_license');
                                              },
                                            ),
                                            ListTile(
                                              title: const Text('National ID'),
                                              onTap: () {
                                                Navigator.pop(context);
                                                _pickImage(ImageSource.gallery,
                                                    'national_id');
                                              },
                                            ),
                                          ],
                                        );
                                      },
                                    );
                                  },
                                  child: Icon(
                                    Icons.upload_file,
                                    color: Colors.teal[800],
                                    size: 24.0,
                                  ),
                                ),
                            ],
                          ),

                          // Save Button inside the container with reduced width
                          SizedBox(height: size.height * 0.04),
                          SizedBox(
                            width: size.width *
                                0.8, // Reduced width for better fit
                            child: ElevatedButton(
                              onPressed: () async {
                                await editServiceProfile(
                                    citizenshipFront,
                                    citizenshipBack,
                                    drivingLicense,
                                    nationalId);

                                if (count1 != 5 && count1 != 6 && count == 0) {
                                  Navigator.pushReplacement(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          ServiceProviderSettingPage(),
                                    ),
                                  );
                                } else if (count1 == 5) {
                                  if (count != 0) {
                                    errorToast(
                                      msg:
                                          'Document uploading failed. Please try again.',
                                    );
                                  }
                                } else {
                                  successToast(msg: 'Updated successfully');
                                  Navigator.pushReplacement(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => const Profile(),
                                    ),
                                  );
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 14.0),
                                backgroundColor:
                                    const Color.fromRGBO(0, 131, 143, 1),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10.0),
                                ),
                                textStyle: const TextStyle(
                                  fontSize: 18.0,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              child: const Text('Save'),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget buildSkillsDropdown() {
    return TextFormField(
      onTap: () async {
        String data = await fetchServiceCategory();
        // consolelog(data);
        if (data == "Error Fetching Data") {
          errorToast(msg: data);
        } else {
          data = data.replaceAll("'", '"');
          final Map<String, dynamic> data1 = json.decode(data);

          List<int> serviceIDs = List<int>.from(data1['Service ID']);
          int categoryID = data1['Category Id'];

          print('Service IDs: $serviceIDs');
          print('Category ID: $categoryID');
          showSubSkillsDialog(
              categoryID.toString(), categoryID.toString(), serviceIDs);
        }
      },
      controller: _skillController,
      readOnly: true,
      decoration: const InputDecoration(
        labelText: 'Select Your Job Field',
        labelStyle: TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please select your skills';
        }
        return null;
      },
    );
  }

  void showSkillsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Your Skills'),
          content: SizedBox(
            height: 700,
            child: Scrollbar(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    buildSkillsCheckboxList(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget buildSkillsCheckboxList() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: fetchSkills(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const CircularProgressIndicator();
        } else if (snapshot.hasError) {
          return Text('Error: ${snapshot.error}');
        } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Text('No skills available');
        }

        List<Map<String, dynamic>> skills = snapshot.data!;
        return Column(
          children: skills.map((skill) {
            String categoryId = skill['categoryId']!;
            String categoryTitle = skill['categoryTitle']!;
            return ListTile(
              title: Text(categoryTitle),
              onTap: () {
                setState(() {
                  selectedJobCategoryId = categoryId;
                  if (selectedMainSkill != categoryTitle) {
                    selectedSubSkills[selectedMainSkill] = [];
                    selectedSkills = [];
                    _updateTextFieldText('');
                  }
                  selectedMainSkill = categoryTitle;
                  //    Navigator.pop(context);
                  //showSubSkillsDialog(categoryId, categoryTitle);
                });
              },
            );
          }).toList(),
        );
      },
    );
  }

  Widget buildMiniMap() {
    // Parse the coordinates from the controller
    LatLng? userLocation;
    if (_workingaddressController.text.isNotEmpty) {
      try {
        List<String> coordinates = _workingaddressController.text.split(',');
        if (coordinates.length == 2) {
          double lat = double.parse(coordinates[0].trim());
          double lng = double.parse(coordinates[1].trim());
          userLocation = LatLng(lat, lng);
        }
      } catch (e) {
        consolelog("Error parsing coordinates: $e");
      }
    }

    return Container(
      margin: const EdgeInsets.only(top: 8.0),
      height: 180,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: const Color.fromRGBO(0, 131, 143, 0.5),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.0),
        child: Stack(
          children: [
            // If using Google Maps
            userLocation != null
                ? GoogleMap(
                    initialCameraPosition: CameraPosition(
                      target:
                          LatLng(userLocation.latitude, userLocation.longitude),
                      zoom: 15,
                    ),
                    zoomControlsEnabled: false,
                    mapToolbarEnabled: false,
                    myLocationEnabled: true,
                    myLocationButtonEnabled: false,
                    markers: {
                      Marker(
                        markerId: const MarkerId('userLocation'),
                        position: LatLng(
                            userLocation.latitude, userLocation.longitude),
                        infoWindow:
                            const InfoWindow(title: 'Your Working Location'),
                      ),
                    },
                    onMapCreated: (GoogleMapController controller) {
                      // Store controller if needed
                    },
                  )
                : const Center(
                    child: Text(
                      'No location selected',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ),

            // Coordinates display
            if (userLocation != null)
              Positioned(
                bottom: 8,
                left: 8,
                right: 8,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Text(
                    'Location: ${userLocation.latitude.toStringAsFixed(6)}, ${userLocation.longitude.toStringAsFixed(6)}',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Color.fromRGBO(0, 131, 143, 1),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),

            // Expand button
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(
                    Icons.fullscreen,
                    color: Color.fromRGBO(0, 131, 143, 1),
                  ),
                  onPressed: () async {
                    permission = await Geolocator.requestPermission();
                    if (permission == LocationPermission.deniedForever) {
                      await Geolocator.openAppSettings();
                    } else if (permission != LocationPermission.denied &&
                        permission != LocationPermission.deniedForever) {
                      widget.mapController.isMapLoading.value = true;
                      position = await Geolocator.getCurrentPosition(
                          desiredAccuracy: LocationAccuracy.high);

                      LatLng selectedLocation =
                          await Get.to(() => OpenMapScreen(
                                completeGoogleMapController:
                                    completeGoogleMapController,
                                kGoogle: kGoogle,
                                marker: marker,
                                onpressed: onpressed,
                                onLocationSelected: (LatLng location) {
                                  String formattedLocation =
                                      '${location.latitude},${location.longitude}';
                                  _workingaddressController.text =
                                      formattedLocation;
                                },
                              ));
                    }
                  },
                  tooltip: 'Expand Map',
                ),
              ),
            ),

            // Select location button
            Positioned(
              top: 8,
              left: 8,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(
                    Icons.add_location_alt,
                    color: Color.fromRGBO(0, 131, 143, 1),
                  ),
                  onPressed: () async {
                    permission = await Geolocator.requestPermission();
                    if (permission == LocationPermission.deniedForever) {
                      await Geolocator.openAppSettings();
                    } else if (permission != LocationPermission.denied &&
                        permission != LocationPermission.deniedForever) {
                      widget.mapController.isMapLoading.value = true;
                      position = await Geolocator.getCurrentPosition(
                          desiredAccuracy: LocationAccuracy.high);

                      LatLng selectedLocation =
                          await Get.to(() => OpenMapScreen(
                                completeGoogleMapController:
                                    completeGoogleMapController,
                                kGoogle: kGoogle,
                                marker: marker,
                                onpressed: onpressed,
                                onLocationSelected: (LatLng location) {
                                  String formattedLocation =
                                      '${location.latitude},${location.longitude}';
                                  _workingaddressController.text =
                                      formattedLocation;
                                },
                              ));
                    }
                  },
                  tooltip: 'Select Location',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildWorkingAddressSection(Size size) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Working Address',
              style: TextStyle(
                color: Color.fromRGBO(0, 131, 143, 1),
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
            // Open Map button - always visible
            ElevatedButton.icon(
              onPressed: () async {
                permission = await Geolocator.requestPermission();
                if (permission == LocationPermission.deniedForever) {
                  await Geolocator.openAppSettings();
                } else if (permission != LocationPermission.denied &&
                    permission != LocationPermission.deniedForever) {
                  widget.mapController.isMapLoading.value = true;
                  position = await Geolocator.getCurrentPosition(
                      desiredAccuracy: LocationAccuracy.high);

                  await Get.to(() => OpenMapScreen(
                        completeGoogleMapController:
                            completeGoogleMapController,
                        kGoogle: kGoogle,
                        marker: marker,
                        onpressed: onpressed,
                        onLocationSelected: (LatLng location) {
                          String formattedLocation =
                              '${location.latitude},${location.longitude}';
                          _workingaddressController.text = formattedLocation;
                          setState(() {}); // Refresh UI to show map
                        },
                      ));
                }
              },
              icon: const Icon(Icons.map, color: Colors.white, size: 16),
              label: const Text('Open Map'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color.fromRGBO(0, 131, 143, 1),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                minimumSize: const Size(100, 36),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8.0),

        // Hidden field to store coordinates
        Opacity(
          opacity: 0,
          child: Container(
            height: 0,
            child: TextFormField(
              controller: _workingaddressController,
              readOnly: true,
            ),
          ),
        ),

        // Show mini map or placeholder
        _workingaddressController.text.isNotEmpty
            ? buildMiniMap()
            : Container(
                width: double.infinity,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(12.0),
                  border: Border.all(
                    color: Colors.grey.shade400,
                    width: 1.0,
                  ),
                ),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.location_on,
                        color: Color.fromRGBO(0, 131, 143, 1),
                        size: 36,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'No location selected',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
      ],
    );
  }

  void _updateTextFieldText(String mainSkill) {
    if (selectedSkills.isNotEmpty) {
      _skillController.text = '$mainSkill: ${selectedSkills.join(', ')}';
    } else {
      _skillController.text = '';
    }
  }

  void showSubSkillsDialog(
      String categoryId, String mainSkill, List serviceId) {
    selectedSkills = List.from(selectedSubSkills[mainSkill] ?? []);

    // Convert serviceId list to strings
    String skill = (serviceId.toString());
    skill = skill.replaceAll('[', '').replaceAll(']', '');
    List<String> ss = skill.split(',');
    ss = ss.map((e) => e.trim()).toList();
    selectedSkills = ss;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return FutureBuilder<List<Map<String, dynamic>>>(
          future: fetchSubSkills(categoryId),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return AlertDialog(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20)),
                title: const Text(
                  'Loading Sub Skills...',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                ),
                content: const Center(child: CircularProgressIndicator()),
              );
            } else if (snapshot.hasError) {
              return AlertDialog(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20)),
                title: const Text(
                  'Error',
                  style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                      fontSize: 18),
                ),
                content: Text(
                  'Error: ${snapshot.error}',
                  style: const TextStyle(fontSize: 16),
                ),
              );
            } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
              return AlertDialog(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20)),
                title: const Text(
                  'No Sub Skills Available',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                ),
                content: const Text(
                  'No sub skills available for this skill.',
                  style: TextStyle(fontSize: 16),
                ),
              );
            }

            List<Map<String, dynamic>> subSkillsList = snapshot.data!;

            // Create a map for quick lookup of names by ID
            Map<String, String> subSkillsMap = {};
            for (var skill in subSkillsList) {
              subSkillsMap[skill['id'].toString()] = skill['name'].toString();
            }

            return StatefulBuilder(
              builder: (context, setState) {
                return AlertDialog(
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20)),
                  title: const Text(
                    'Select Sub Skills',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      color: Color.fromARGB(240, 0, 131, 143),
                    ),
                  ),
                  content: SizedBox(
                    height: 300,
                    width: 350,
                    child: Scrollbar(
                      thickness: 4,
                      radius: const Radius.circular(10),
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: subSkillsList.map((subSkill) {
                            return Card(
                              color: Colors.white,
                              margin: const EdgeInsets.symmetric(
                                  vertical: 4, horizontal: 2),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12)),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 4, horizontal: 10),
                                child: CheckboxListTile(
                                  title: Text(
                                    subSkill['name'],
                                    style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500),
                                  ),
                                  value: selectedSkills
                                      .contains(subSkill['id'].toString()),
                                  activeColor:
                                      const Color.fromARGB(240, 0, 131, 143),
                                  checkColor: Colors.white,
                                  onChanged: (value) {
                                    setState(() {
                                      if (value != null) {
                                        if (value) {
                                          selectedSkills
                                              .add(subSkill['id'].toString());
                                        } else {
                                          selectedSkills.remove(
                                              subSkill['id'].toString());
                                        }
                                      }
                                    });
                                  },
                                  controlAffinity:
                                      ListTileControlAffinity.leading,
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),
                  actions: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      child: ElevatedButton(
                        onPressed: () async {
                          // Store selected skills
                          selectedSubSkills[mainSkill] =
                              List.from(selectedSkills);

                          // Get the category name instead of using the ID
                          String categoryName =
                              await fetchCategoryName(int.parse(categoryId));

                          // Convert IDs to names for display
                          List<String> selectedNames = [];
                          for (String id in selectedSkills) {
                            if (subSkillsMap.containsKey(id)) {
                              selectedNames.add(subSkillsMap[id]!);
                            } else {
                              // If name not found, try to fetch it
                              String name =
                                  await fetchSingleServiceName(int.parse(id));
                              selectedNames.add(name);
                            }
                          }

                          // Update the text field with category name and service names
                          this.setState(() {
                            _skillController.text =
                                '$categoryName: ${selectedNames.join(', ')}';
                            selectedJobCategoryId = categoryId;
                          });

                          Navigator.pop(context);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              const Color.fromARGB(240, 0, 131, 143),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                          padding: const EdgeInsets.symmetric(
                              vertical: 12, horizontal: 24),
                          elevation: 4,
                        ),
                        child: const Text(
                          'Done',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            );
          },
        );
      },
    );
  }

  Future<String> fetchCategoryName(int categoryId) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");

    if (apptoken == null) {
      return "Unknown Category";
    }

    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/categories/$categoryId'),
        headers: <String, String>{
          'Authorization': 'Bearer $apptoken',
        },
      );

      if (response.statusCode == 200) {
        var data = json.decode(response.body);
        return data['categoryTitle'] ?? "Unknown Category";
      }
      return "Unknown Category";
    } catch (e) {
      consolelog("Error fetching category name: $e");
      return "Unknown Category";
    }
  }

  Future<List<String>> fetchServiceNames(List<int> serviceIds) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");

    if (apptoken == null || serviceIds.isEmpty) {
      return serviceIds.map((id) => id.toString()).toList();
    }

    List<String> serviceNames = [];

    try {
      // Fetch all services to get their names
      final response = await http.get(
        Uri.parse('$baseUrl/api/services'),
        headers: <String, String>{
          'Authorization': 'Bearer $apptoken',
        },
      );

      if (response.statusCode == 200) {
        var allServices = json.decode(response.body);

        // Create a map of id -> name for quick lookup
        Map<int, String> serviceMap = {};
        if (allServices is List) {
          for (var service in allServices) {
            if (service['id'] != null && service['name'] != null) {
              serviceMap[int.parse(service['id'].toString())] =
                  service['name'].toString();
            }
          }
        }

        // Look up each service ID in the map
        for (int id in serviceIds) {
          if (serviceMap.containsKey(id)) {
            serviceNames.add(serviceMap[id]!);
          } else {
            serviceNames.add(id.toString()); // Fallback to ID if name not found
          }
        }

        return serviceNames;
      }

      // If the above fails, try individual requests
      for (int id in serviceIds) {
        final response = await http.get(
          Uri.parse('$baseUrl/api/services/$id'),
          headers: <String, String>{
            'Authorization': 'Bearer $apptoken',
          },
        );

        if (response.statusCode == 200) {
          var data = json.decode(response.body);
          if (data['name'] != null) {
            serviceNames.add(data['name'].toString());
          } else {
            serviceNames.add(id.toString());
          }
        } else {
          serviceNames.add(id.toString());
        }
      }

      return serviceNames;
    } catch (e) {
      consolelog("Error fetching service names: $e");
      return serviceIds.map((id) => id.toString()).toList();
    }
  }

  Future<List<Map<String, dynamic>>> fetchSubSkills(String categoryId) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");

    if (apptoken == null) {
      return [];
    }

    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/categories/$categoryId/services'),
        headers: <String, String>{
          'Authorization': 'Bearer $apptoken',
        },
      );

      consolelog(
          "Fetching sub skills for category $categoryId, status: ${response.statusCode}");

      if (response.statusCode == 200) {
        var data = json.decode(response.body);
        List<Map<String, dynamic>> result = [];

        if (data is List) {
          for (var item in data) {
            if (item is Map &&
                item.containsKey('id') &&
                item.containsKey('name')) {
              result.add({
                'id': item['id'].toString(),
                'name': item['name'].toString(),
              });
            }
          }
        } else if (data is Map && data.containsKey('services')) {
          var services = data['services'];
          if (services is List) {
            for (var item in services) {
              if (item is Map &&
                  item.containsKey('id') &&
                  item.containsKey('name')) {
                result.add({
                  'id': item['id'].toString(),
                  'name': item['name'].toString(),
                });
              }
            }
          }
        }

        consolelog("Fetched ${result.length} sub skills");
        return result;
      }

      // Try alternative endpoint
      final altResponse = await http.get(
        Uri.parse('$baseUrl/api/category/$categoryId/services'),
        headers: <String, String>{
          'Authorization': 'Bearer $apptoken',
        },
      );

      if (altResponse.statusCode == 200) {
        var data = json.decode(altResponse.body);
        List<Map<String, dynamic>> result = [];

        if (data is List) {
          for (var item in data) {
            if (item is Map &&
                item.containsKey('id') &&
                item.containsKey('name')) {
              result.add({
                'id': item['id'].toString(),
                'name': item['name'].toString(),
              });
            }
          }
        } else if (data is Map && data.containsKey('services')) {
          var services = data['services'];
          if (services is List) {
            for (var item in services) {
              if (item is Map &&
                  item.containsKey('id') &&
                  item.containsKey('name')) {
                result.add({
                  'id': item['id'].toString(),
                  'name': item['name'].toString(),
                });
              }
            }
          }
        }

        consolelog(
            "Fetched ${result.length} sub skills from alternative endpoint");
        return result;
      }

      return [];
    } catch (e) {
      consolelog("Error fetching sub skills: $e");
      return [];
    }
  }

  // Add this helper method to fetch a single service name
  Future<String> fetchSingleServiceName(int serviceId) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");

    if (apptoken == null) {
      return "Service $serviceId";
    }

    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/services/$serviceId'),
        headers: <String, String>{
          'Authorization': 'Bearer $apptoken',
        },
      );

      if (response.statusCode == 200) {
        var data = json.decode(response.body);
        if (data is Map && data.containsKey('name')) {
          return data['name'].toString();
        }
      }

      // Try alternative endpoint
      final altResponse = await http.get(
        Uri.parse('$baseUrl/api/service/$serviceId'),
        headers: <String, String>{
          'Authorization': 'Bearer $apptoken',
        },
      );

      if (altResponse.statusCode == 200) {
        var data = json.decode(altResponse.body);
        if (data is Map && data.containsKey('name')) {
          return data['name'].toString();
        }
      }

      return "Service $serviceId";
    } catch (e) {
      consolelog("Error fetching single service name: $e");
      return "Service $serviceId";
    }
  }
}
