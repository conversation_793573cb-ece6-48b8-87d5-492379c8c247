// // ignore_for_file: public_member_api_docs, sort_constructors_first
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:shared_preferences/shared_preferences.dart';

// import 'package:smartsewa/core/development/console.dart';
// import 'package:smartsewa/network/models/notification_model/user_model.dart';
// import 'package:smartsewa/network/services/notification_services/notification_controller.dart';
// import 'package:smartsewa/views/widgets/custom_text.dart';
// import 'package:smartsewa/views/widgets/my_appbar.dart';

// class NotificationScreen extends StatefulWidget {
//   List<UserNotificationModel> notifications;
//   final bool? isFromServiceScreen;
//   NotificationScreen({
//     super.key,
//     required this.notifications,
//     this.isFromServiceScreen,
//   });

//   @override
//   State<NotificationScreen> createState() => _NotificationScreenState();
// }

// class _NotificationScreenState extends State<NotificationScreen> {
//   final notificationsController = Get.put(NotificationController());
//   @override
//   void initState() {
//     super.initState();
//     d();

//     // notificationsController.fetchUserNotifications().then((data) {
//     //   setState(() {
//     //     widget.notifications = [];
//     //   });
//     // }).catchError((error) {
//     //   print('Failed to fetch notifications: $error');
//     // });
//   }

//   d() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     String? apptoken = prefs.getString("token");
//     int? tid = prefs.getInt("id");
//     print(tid);
//     print(apptoken);
//   }

//   void markNotificationAsSeen(int notificationId) {
//     notificationsController
//         .updateNotificationSeenStatus(notificationId)
//         .then((_) {
//       setState(() {
//         // Find the index of the updated notification
//         final index = notificationsController.userNotifications.indexWhere(
//             (notification) => notification['notificationId'] == notificationId);
//         if (index != -1) {
//           // Update the "seen" status of the notification in the list
//           notificationsController.userNotifications[index].seen = true;
//         }
//       });
//     }).catchError((error) {
//       print('Failed to update notification seen status: $error');
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return SafeArea(
//       child: Scaffold(
//         appBar: widget.isFromServiceScreen ?? false
//             ? null
//             : myAppbar(context, true, "Notification"),
//         body: DefaultTabController(
//           length: 2,
//           child: Column(
//             children: [
//               TabBar(
//                 onTap: (index) {
//                   index == 1
//                       ? notificationsController
//                           .fetchUserNotifications()
//                           .then((data) {
//                           notificationsController
//                               .updateUserNotificationsSeenStatus();
//                         }).catchError((error) {
//                           consolelog('Failed to fetch notifications: $error');
//                         })
//                       : null;
//                 },
//                 tabs: [
//                   Tab(
//                     // text: 'Admin',

//                     child: Text(
//                       'Admin',
//                       style: Theme.of(context).textTheme.titleLarge!.copyWith(
//                           color: const Color.fromARGB(240, 0, 131, 143),
//                           fontWeight: FontWeight.w600),
//                     ),
//                   ),
//                   Tab(
//                     child: Text(
//                       'User',
//                       style: Theme.of(context).textTheme.titleLarge!.copyWith(
//                           color: const Color.fromARGB(240, 0, 131, 143),
//                           fontWeight: FontWeight.w600),
//                     ),
//                   )
//                 ],
//               ),
//               Expanded(
//                 child: TabBarView(children: [
//                   _buildAdminNotifications(),
//                   _buildUserNotifications(),
//                 ]),
//               )
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildAdminNotifications() {
//     return Obx(
//       () => notificationsController.adminNotifications.isEmpty
//           ? Center(
//               child: CustomText.ourText("Empty"),
//             )
//           : ListView.builder(
//               shrinkWrap: true,
//               // physics: const NeverScrollableScrollPhysics(),
//               itemCount: notificationsController.adminNotifications.length,
//               itemBuilder: (context, index) {
//                 final notification =
//                     notificationsController.adminNotifications[index];
//                 final date = notification.dateAndTime?.split('T')[0];

//                 return Card(
//                   elevation: 3,
//                   child: ListTile(
//                     title: Text(notification.title),
//                     subtitle: Text(notification.information),
//                     trailing: Text(date.toString()),
//                     onTap: () {},
//                   ),
//                 );
//               },
//             ),
//     );
//   }

//   Widget _buildUserNotifications() {
//     return Obx(
//       () => notificationsController.isLoading.value
//           ? const Center(child: CircularProgressIndicator())
//           : notificationsController.userNotifications.isEmpty
//               ? Center(
//                   child: CustomText.ourText("Empty"),
//                 )
//               : ListView.builder(
//                   shrinkWrap: true,
//                   // physics: const NeverScrollableScrollPhysics(),
//                   itemCount: notificationsController.userNotifications.length,
//                   itemBuilder: (context, index) {
//                     final notification =
//                         notificationsController.userNotifications[index];
//                     return Card(
//                       elevation: 3,
//                       child: ListTile(
//                         title: Text(notification.notificationType),
//                         subtitle: Text(notification.information),
//                         trailing: Text(notification.dateAndTime),
//                         onTap: () {
//                           // markNotificationAsSeen(notification.notificationId);
//                         },
//                       ),
//                     );
//                   },
//                 ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/network/models/notification_model/user_model.dart';
import 'package:smartsewa/network/services/notification_services/notification_controller.dart';
import 'package:smartsewa/views/widgets/custom_text.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';

class NotificationScreen extends StatefulWidget {
  List<UserNotificationModel> notifications;
  final bool? isFromServiceScreen;
  NotificationScreen({
    super.key,
    required this.notifications,
    this.isFromServiceScreen,
  });

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  final notificationsController = Get.put(NotificationController());

  @override
  void initState() {
    super.initState();
    // Set status bar color to match your app bar
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Color.fromARGB(240, 0, 131, 143),
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.dark,
    ));
    d();
  }

  d() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");
    int? tid = prefs.getInt("id");
    print(tid);
    print(apptoken);
  }

  void markNotificationAsSeen(int notificationId) {
    notificationsController
        .updateNotificationSeenStatus(notificationId)
        .then((_) {
      setState(() {
        final index = notificationsController.userNotifications.indexWhere(
            (notification) => notification['notificationId'] == notificationId);
        if (index != -1) {
          notificationsController.userNotifications[index].seen = true;
        }
      });
    }).catchError((error) {
      print('Failed to update notification seen status: $error');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.isFromServiceScreen ?? false
          ? null
          : myAppbar(context, true, "Notifications"),
      body: DefaultTabController(
        length: 2,
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: TabBar(
                onTap: (index) {
                  index == 1
                      ? notificationsController
                          .fetchUserNotifications()
                          .then((data) {
                          notificationsController
                              .updateUserNotificationsSeenStatus();
                        }).catchError((error) {
                          consolelog('Failed to fetch notifications: $error');
                        })
                      : null;
                },
                indicator: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color:
                      const Color.fromARGB(240, 0, 131, 143).withOpacity(0.1),
                ),
                labelColor: const Color.fromARGB(240, 0, 131, 143),
                unselectedLabelColor: Colors.grey,
                tabs: [
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.admin_panel_settings),
                        const SizedBox(width: 8),
                        Text(
                          'Admin',
                          style: Theme.of(context)
                              .textTheme
                              .titleMedium!
                              .copyWith(fontWeight: FontWeight.w600),
                        ),
                      ],
                    ),
                  ),
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.person),
                        const SizedBox(width: 8),
                        Text(
                          'User',
                          style: Theme.of(context)
                              .textTheme
                              .titleMedium!
                              .copyWith(fontWeight: FontWeight.w600),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
            Expanded(
              child: TabBarView(children: [
                _buildAdminNotifications(),
                _buildUserNotifications(),
              ]),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildAdminNotifications() {
    return Obx(
      () => notificationsController.adminNotifications.isEmpty
          ? _buildEmptyState("No admin notifications", Icons.notifications_off)
          : Padding(
              padding: const EdgeInsets.all(12.0),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: notificationsController.adminNotifications.length,
                itemBuilder: (context, index) {
                  final notification =
                      notificationsController.adminNotifications[index];
                  final date = notification.dateAndTime?.split('T')[0];

                  return _buildNotificationCard(
                    title: notification.title,
                    subtitle: notification.information,
                    date: date.toString(),
                    isRead: true,
                    icon: Icons.admin_panel_settings,
                  );
                },
              ),
            ),
    );
  }

  Widget _buildUserNotifications() {
    return Obx(
      () => notificationsController.isLoading.value
          ? const Center(
              child: CircularProgressIndicator(
                color: Color.fromARGB(240, 0, 131, 143),
              ),
            )
          : notificationsController.userNotifications.isEmpty
              ? _buildEmptyState(
                  "No user notifications", Icons.notifications_off)
              : Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: notificationsController.userNotifications.length,
                    itemBuilder: (context, index) {
                      final notification =
                          notificationsController.userNotifications[index];
                      return _buildNotificationCard(
                        title: notification.notificationType,
                        subtitle: notification.information,
                        date: notification.dateAndTime,
                        isRead: notification.seen ?? false,
                        icon: Icons.notifications,
                      );
                    },
                  ),
                ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey.withOpacity(0.6),
          ),
          const SizedBox(height: 16),
          CustomText.ourText(message),
        ],
      ),
    );
  }

  Widget _buildNotificationCard({
    required String title,
    required String subtitle,
    required String date,
    required bool isRead,
    required IconData icon,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: isRead
            ? Colors.white
            : const Color.fromARGB(240, 0, 131, 143).withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        leading: CircleAvatar(
          backgroundColor:
              const Color.fromARGB(240, 0, 131, 143).withOpacity(0.1),
          child: Icon(
            icon,
            color: const Color.fromARGB(240, 0, 131, 143),
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: isRead ? FontWeight.normal : FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 6),
            Text(
              subtitle,
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: Colors.grey[500],
                ),
                const SizedBox(width: 4),
                Text(
                  date,
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: !isRead
            ? Container(
                width: 12,
                height: 12,
                decoration: const BoxDecoration(
                  color: Color.fromARGB(240, 0, 131, 143),
                  shape: BoxShape.circle,
                ),
              )
            : null,
        onTap: () {
          // Functionality unchanged
        },
      ),
    );
  }
}
