import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CustomToasts {
  static void showToast({
    String? msg,
    Color? color = const Color.fromARGB(240, 0, 131, 143),
  }) {
    Fluttertoast.cancel();
    Fluttertoast.showToast(
        msg: msg.toString(),
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: color,
        textColor: Colors.white,
        fontSize: 14.0);
  }
}

errorToast({String msg = "Please fill"}) {
  CustomToasts.showToast(msg: msg, color: Colors.red);
}

successToast({String? msg}) {
  CustomToasts.showToast(
      msg: msg, color: const Color.fromARGB(240, 0, 131, 143));
}

warningToast({String? msg}) {
  CustomToasts.showToast(msg: msg, color: Colors.orange);
}
