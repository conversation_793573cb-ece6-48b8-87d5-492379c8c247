import 'dart:async';
import 'dart:io';
import 'package:flutter/gestures.dart';
import 'package:upgrader/upgrader.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_offline/flutter_offline.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shimmer/shimmer.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/core/dimension.dart';
import 'package:smartsewa/network/base_client.dart';
import 'package:smartsewa/network/services/authServices/auth_controller.dart';
import 'package:smartsewa/network/services/contact_services/contact_controller.dart';
import 'package:smartsewa/network/services/image_services/image_controller.dart';
import 'package:smartsewa/network/services/notification_services/notification_controller.dart';
import 'package:smartsewa/utils/double_tap_back.dart';
import 'package:smartsewa/utils/string_extension.dart';
import 'package:smartsewa/views/auth/login/login_screen.dart';
import 'package:smartsewa/views/auth/registration/user_registration.dart';
import 'package:smartsewa/views/user_screen/categories/services.dart';
import 'package:smartsewa/views/user_screen/drawer%20screen/map_Screen.dart';
import 'package:smartsewa/views/user_screen/setting/setting.dart';
import 'package:smartsewa/views/user_screen/showExtraServices/RealEstate.dart';
import 'package:smartsewa/views/user_screen/showExtraServices/Vacancyfinal.dart';
import 'package:smartsewa/views/user_screen/showExtraServices/market_place_screen.dart';
import 'package:smartsewa/views/user_screen/showExtraServices/offer.dart';
import 'package:smartsewa/views/user_screen/showExtraServices/offerui.dart';
import 'package:smartsewa/views/user_screen/showExtraServices/vacancy.dart';
import 'package:smartsewa/views/utils.dart';
import '../../network/services/banner_repo/banner_repo.dart';
import '../../network/services/userdetails/current_user_controller.dart';
import '../widgets/custom_toasts.dart';
import '../widgets/my_drawer.dart';
import 'notification.dart';
import 'profile/profile.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});
  final indexes = 0;

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  final AuthController authController = Get.find<AuthController>();
  final int myTime = 3;
  Timer? _dailyUpdateCheckTimer;
  bool hasAgreedToDisclaimer = false;
  late Upgrader _upgrader;
  final List<String> urgentMessages = [
    "Urgent: Need plumber in Kathmandu!",
    "Vacancy: Software Engineer position open",
    "Urgent: Looking for part-time cook",
    "Special offer: 50% off on cleaning services"
  ];

  final List<String> topImages = [];
  final List<String> bottomImages = [];
  final List<String> services = [];
  // final int _currentIndex = 0;
  late List<Widget> screens;

  final GlobalKey<ScaffoldState> _scaffoldState = GlobalKey<ScaffoldState>();

  // final filterController = Get.put(FilterController());
  // final serController = Get.put(ServicesController());
  // final imageController = Get.put(ImageController());
  // final catController = Get.put(CatController());

  final controller = Get.put(CurrentUserController());
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  final notificationsController = Get.put(NotificationController());
  final BannerImageController bannerController =
      Get.put(BannerImageController());

  File? image;

  String? token;
  bool? work;

  int index = 0;
  final scrollController = ScrollController();
  final homeScreenKey = GlobalKey();
  List<dynamic> notifications = [];

//   @override
//   void initState() {
//     super.initState();
//     _startDailyUpdateCheck();
//     hasAgreedToDisclaimer = false;

//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       SystemChrome.setSystemUIOverlayStyle(
//         const SystemUiOverlayStyle(
//           statusBarColor: Color.fromARGB(240, 0, 96, 100), // Set your color
//           statusBarIconBrightness: Brightness.light, // Light icons (Android)
//           statusBarBrightness: Brightness.dark, // Light icons (iOS)
//         ),
//       );
//     });

//     getToken();
//     controller.getCurrentUser();

//     bannerController.loadImagesFromSharedPreferences();

//     // bannerController.loadImagesFromSharedPreferences().then((_) {
//     //   if (bannerController.topBanners.isEmpty ||
//     //       bannerController.bottomBanners.isEmpty) {
//     //     bannerController.fetchImages();
//     //   }

//     //   // Debugging: Print whether savedTopBanners and savedBottomBanners are empty
//     //   consolelog(
//     //       'savedTopBanners is empty: ${bannerController.topBanners.isEmpty}');
//     //   consolelog(
//     //       'savedBottomBanners is empty: ${bannerController.bottomBanners.isEmpty}');
//     // });
//   bannerController.loadImagesFromSharedPreferences().then((_) {
//   bannerController.fetchImages();
//   consolelog('savedTopBanners is empty: ${bannerController.topBanners.isEmpty}');
//   consolelog('savedBottomBanners is empty: ${bannerController.bottomBanners.isEmpty}');
// });

//     // WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
//     //   controller.getCurrentUser();
//     // });

//     // imageController.fetchImages(controller.currentUserData.value.picture);
//     //
//     // catController.getCategories();
//     // serController.fetchServices();
//     // notificationsController.fetchUserNotificationsSeenStatus();

//     // notificationsController.fetchUserNotifications().then((data) {
//     //   setState(() {
//     //     notifications = data!;
//     //   });
//     // }).catchError((error) {
//     //   print(error);
//     // });
//   }
  // @override
  // void initState() {
  //   super.initState();
  //   _startDailyUpdateCheck();
  //   hasAgreedToDisclaimer = false;
  //   Upgrader.clearSavedSettings();
  //   WidgetsBinding.instance.addPostFrameCallback((_) {
  //     SystemChrome.setSystemUIOverlayStyle(
  //       const SystemUiOverlayStyle(
  //         statusBarColor: Color.fromARGB(240, 0, 96, 100),
  //         statusBarIconBrightness: Brightness.light,
  //         statusBarBrightness: Brightness.dark,
  //       ),
  //     );
  //   });

  //   getToken();
  //   controller.getCurrentUser();
  //   bannerController.initializeBanners().then((_) {
  //     consolelog('Banner initialization completed');
  //     consolelog(
  //         'Current top banners count: ${bannerController.topBanners.length}');
  //     consolelog(
  //         'Current bottom banners count: ${bannerController.bottomBanners.length}');
  //   });
  // }
  @override
  void initState() {
    super.initState();

    // Initialize daily update check
    _startDailyUpdateCheck();

    // Reset disclaimer state
    hasAgreedToDisclaimer = false;

    // Clear any saved upgrade settings to force fresh check

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Set system UI overlay style
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Color.fromARGB(240, 0, 96, 100),
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
        ),
      );

      // Initialize Upgrader - no need to store in variable as UpgradeAlert will handle it
    });

    // Load user token and data
    getToken();
    controller.getCurrentUser();

    // Initialize banners
    bannerController.initializeBanners().then((_) {
      consolelog('Banner initialization completed');
      consolelog(
          'Current top banners count: ${bannerController.topBanners.length}');
      consolelog(
          'Current bottom banners count: ${bannerController.bottomBanners.length}');

      if (bannerController.topBanners.isEmpty ||
          bannerController.bottomBanners.isEmpty) {
        bannerController.fetchImages();
      }
    });
  }

  void _showUpgradeDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Update Available'),
        content: const Text(
            'A new version of the app is available. Please update to continue.'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              consolelog('LATER CALLBACK FIRED');
            },
            child: const Text('Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              consolelog('UPDATE CALLBACK FIRED');
              _upgrader.sendUserToAppStore();
            },
            child: const Text('Update'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  void listenScrolling() {
    if (scrollController.position.atEdge) {
      final isTop = scrollController.position.pixels == 0;

      if (isTop) {}
    }
  }

  void _startDailyUpdateCheck() {
    // Set up a periodic check every 24 hours
    _dailyUpdateCheckTimer =
        Timer.periodic(const Duration(days: 1), (timer) async {
      _checkForUpdates();
      await Upgrader.clearSavedSettings();
    });

    // Also check immediately when the app starts
    _checkForUpdates();
  }

  void _checkForUpdates() {}

  @override
  void dispose() {
    // Cancel the timer when the app is disposed
    _dailyUpdateCheckTimer?.cancel();
    super.dispose();
  }

  // Future<void> _refreshData() async {
  //   await Future.delayed(const Duration(seconds: 5)); //prajwal change 1 to 5
  //   controller.getCurrentUser(); //prajwal Change
  //   //imageController.fetchImages(controller.currentUserData.value.picture);
  //   // catController.getCategories();
  //   // serController.fetchServices();
  //   notificationsController.fetchUserNotificationsSeenStatus();
  //   notificationsController.fetchAdminNotification();
  //   bannerController.loadImagesFromSharedPreferences();
  // }

  Future<void> _refreshData() async {
    await Future.delayed(const Duration(seconds: 5)); //prajwal change 1 to 5
    controller.getCurrentUser(); //prajwal Change
    //imageController.fetchImages(controller.currentUserData.value.picture);
    // catController.getCategories();
    // serController.fetchServices();
    notificationsController.fetchUserNotificationsSeenStatus();
    notificationsController.fetchAdminNotification();
    bannerController.loadImagesFromSharedPreferences();

    // Add banner initialization on refresh
    bannerController.initializeBanners().then((_) {
      consolelog('Banner initialization completed on refresh');
      consolelog(
          'Current top banners count: ${bannerController.topBanners.length}');
      consolelog(
          'Current bottom banners count: ${bannerController.bottomBanners.length}');
    });
  }

  // getPicture() async {
  //   await controller.getCurrentUser();
  // }

  void getToken() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");
    bool workStatus = await prefs.getBool("workStatus") ?? false;
    // int? tid = prefs.getInt("id");
    setState(() {
      token = apptoken;
      work = workStatus;
    });
  }

  void scrollUp() {
    // const double start = 0;
    // scrollController.jumpTo(start);
    scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
    _refreshData();
  }

  String baseUrl = BaseClient().baseUrl;

  final List<Service> myServices = [
    Service(1, "Electrical", "assets/services_images/electrical.png"),
    Service(2, "Plumbing", "assets/services_images/plumbing.png"),
    Service(3, "Masonry Works", "assets/services_images/masonry_works.png"),
    Service(4, "Cleaning", "assets/services_images/cleaning.png"),
    Service(5, "Carpentry", "assets/services_images/carpentry.png"),
    Service(6, "Metal Works", "assets/services_images/metal_works.png"),
    Service(7, "Paint and Painting",
        "assets/services_images/paint_and_painting.png"),
    Service(8, "Cook and Waiter", "assets/services_images/cook_and_waiter.png"),
    Service(9, "Home Care Staff", "assets/services_images/home_care_staff.png"),
    Service(
        10, "Healthcare and Medicine", "assets/services_images/healthcare.png"),
    Service(11, "Veterinary and Pet Care", "assets/services_images/vet.png"),
    Service(12, "Tuition and Languages", "assets/services_images/tution.png"),
    Service(
        13, "Music and Dance", "assets/services_images/music_and_dance.png"),
    Service(14, "Gardener and Agriculture Works",
        "assets/services_images/garderner_and_agriculture_works.png"),
    Service(15, "Catering and Rent", "assets/services_images/catering.png"),
    Service(
        16, "Event and Party", "assets/services_images/event_and_party.png"),
    Service(17, "Furniture and Home Decor",
        "assets/services_images/furniture_and_homedecor.png"),
    Service(
        18, "Travel and Tours", "assets/services_images/air_and_travels.png"),
    Service(19, "Vehicle", "assets/services_images/vehicle.png"),
    Service(
        20, "Fitness and Yoga", "assets/services_images/fitness_and_yoga.png"),
    Service(
        21, "Waste Management", "assets/services_images/waste_management.png"),
    Service(22, "Training and Skill Program",
        "assets/services_images/trainning_and_skill_program.png"),
    Service(23, "Office Staff", "assets/services_images/office_staff.png"),
    Service(24, "Advertisement and Promotion",
        "assets/services_images/advertisement.png"),
    Service(25, "Printing and Press",
        "assets/services_images/printing_and_press.png"),
    Service(26, "Books and Stationery",
        "assets/services_images/books_and_stationery.png"),
    Service(27, "Engineering", "assets/services_images/engineering.png"),
    Service(28, "Dharmik Karyakram",
        "assets/services_images/dharmik_karyakram.png"),
    Service(29, "Many More", "assets/services_images/others.png"),
  ];
  Future<void> logout() async {
    final preferences = await SharedPreferences.getInstance();
    await preferences.remove('token');
    await preferences.remove('id');
    await preferences.remove("isAuthenticated");
    Get.offAll(() => const LoginScreen());
  }

  // @override
  // Widget build(BuildContext context) {
  //   final authController = Get.find<AuthController>();
  //   bool isGuest = authController.isUserGuest();
  //   Size size = MediaQuery.of(context).size;

  //   return UpgradeAlert(
  //     navigatorKey: navigatorKey,
  //      showIgnore: false,
  //       showLater: true,
  //     upgrader: Upgrader(
  //       debugLogging: true,
  //       debugDisplayAlways: true,
  //       countryCode: 'NP',
  //       durationUntilAlertAgain: const Duration(minutes: 1),
  //       minAppVersion: '1.0.61',
  //       willDisplayUpgrade: ({
  //         required bool display,
  //         String? installedVersion,
  //         UpgraderVersionInfo? versionInfo,
  //       }) {
  //         if (display) {
  //           debugPrint('Upgrade dialog WILL be shown');
  //         } else {
  //           debugPrint('App is up to date');
  //         }
  //       },
  //     ),
  //     child: WillPopScope(
  //       onWillPop: onWillPop,
  //       child: OfflineBuilder(
  //         connectivityBuilder: (BuildContext context,
  //             ConnectivityResult connectivity, Widget child) {
  //           final bool connected = true;
  //           consolelog("connection log");
  //           consolelog(connected);

  //           if (connected) {
  //             return Scaffold(
  //               backgroundColor: const Color.fromARGB(255, 255, 255, 255),
  //               key: _scaffoldState,
  //               drawer: isGuest ? null : myDrawer(context, work!),
  //               drawerEdgeDragWidth: isGuest ? 0 : 150,
  //               appBar: isGuest
  //                   ? AppBar(
  //                       backgroundColor: const Color.fromARGB(240, 0, 131, 143),
  //                       leading: IconButton(
  //                         icon:
  //                             const Icon(Icons.arrow_back, color: Colors.white),
  //                         onPressed: () {
  //                           Get.to(() => LoginScreen());
  //                         },
  //                       ),
  //                       title: InkWell(
  //                         child: RichText(
  //                           text: TextSpan(
  //                             children: [
  //                               TextSpan(
  //                                 text: 'Click on  ',
  //                                 style: TextStyle(
  //                                   fontSize: size.width * 0.036,
  //                                   fontWeight: FontWeight.bold,
  //                                   color: Colors.white,
  //                                 ),
  //                               ),
  //                               TextSpan(
  //                                 text: 'Sign Up',
  //                                 style: TextStyle(
  //                                   fontSize: size.width * 0.045,
  //                                   fontWeight: FontWeight.bold,
  //                                   color: const Color.fromARGB(
  //                                       255, 247, 170, 102),
  //                                   decoration: TextDecoration.underline,
  //                                 ),
  //                                 recognizer: TapGestureRecognizer()
  //                                   ..onTap = () {
  //                                     Get.to(() => UserRegistration());
  //                                   },
  //                               ),
  //                               TextSpan(
  //                                 text: ' or ',
  //                                 style: TextStyle(
  //                                   fontSize: size.width * 0.036,
  //                                   fontWeight: FontWeight.bold,
  //                                   color: Colors.white,
  //                                 ),
  //                               ),
  //                               TextSpan(
  //                                 text: 'Login',
  //                                 style: TextStyle(
  //                                   fontSize: size.width * 0.045,
  //                                   fontWeight: FontWeight.bold,
  //                                   color: const Color.fromARGB(
  //                                       255, 102, 204, 247),
  //                                   decoration: TextDecoration.underline,
  //                                 ),
  //                                 recognizer: TapGestureRecognizer()
  //                                   ..onTap = () {
  //                                     Get.to(() => LoginScreen());
  //                                   },
  //                               ),
  //                               TextSpan(
  //                                 text: ' to access all features.',
  //                                 style: TextStyle(
  //                                   fontSize: size.width * 0.036,
  //                                   fontWeight: FontWeight.bold,
  //                                   color: Colors.white,
  //                                 ),
  //                               ),
  //                             ],
  //                           ),
  //                         ),
  //                       ),
  //                       centerTitle: true,
  //                       elevation: 4,
  //                       shape: const RoundedRectangleBorder(
  //                         borderRadius: BorderRadius.vertical(
  //                           bottom: Radius.circular(10),
  //                         ),
  //                       ),
  //                     )
  //                   : PreferredSize(
  //                       preferredSize: Size.fromHeight(size.height * 0.07),
  //                       child: Container(
  //                         decoration: BoxDecoration(
  //                           color: const Color.fromRGBO(0, 131, 143, 1),
  //                           boxShadow: [
  //                             BoxShadow(
  //                               color: Colors.black.withOpacity(0.1),
  //                               blurRadius: 4,
  //                               offset: const Offset(0, 2),
  //                             ),
  //                           ],
  //                         ),
  //                         child: SafeArea(
  //                           child: Row(
  //                             children: [
  //                               IconButton(
  //                                 onPressed: () {
  //                                   _scaffoldState.currentState!.openDrawer();
  //                                 },
  //                                 icon: Icon(
  //                                   Icons.menu,
  //                                   size: size.width * 0.065,
  //                                   color: Colors.white,
  //                                 ),
  //                               ),
  //                               SizedBox(width: size.width * 0.02),
  //                               Expanded(
  //                                 child: Obx(() {
  //                                   // Get the user name
  //                                   String userName = controller
  //                                           .currentUserData.value.fullName
  //                                           ?.toUppercaseFirstLetter() ??
  //                                       "";

  //                                   // Only display if username is valid (not loading, null, or "NULL")
  //                                   if (!controller.isLoading.value &&
  //                                       userName.isNotEmpty &&
  //                                       userName != "NULL") {
  //                                     return Text(
  //                                       userName,
  //                                       style: TextStyle(
  //                                         color: Colors.white,
  //                                         fontSize: size.width * 0.045,
  //                                         fontWeight: FontWeight.w700,
  //                                       ),
  //                                       overflow: TextOverflow.ellipsis,
  //                                     );
  //                                   }

  //                                   // Return empty container when loading or null
  //                                   return SizedBox.shrink();
  //                                 }),
  //                               ),
  //                               _buildHeaderIcon(
  //                                 Icons.person,
  //                                 size.width * 0.07,
  //                                 () => Get.to(() => const Profile()),
  //                               ),
  //                               _buildHeaderIcon(
  //                                 Icons.search,
  //                                 size.width * 0.07,
  //                                 () => showSearch(
  //                                   context: context,
  //                                   delegate: DataSearch(services),
  //                                 ),
  //                               ),
  //                               _buildNotificationIcon(size),
  //                               _buildHeaderIcon(
  //                                 Icons.settings,
  //                                 size.width * 0.07,
  //                                 () => Get.to(() => const SettingPage()),
  //                               ),
  //                             ],
  //                           ),
  //                         ),
  //                       ),
  //                     ),
  //               body: Obx(() {
  //                 if (controller.isLoading.value) {
  //                   return const Center(
  //                     child: CircularProgressIndicator(
  //                       valueColor: AlwaysStoppedAnimation<Color>(
  //                         Color.fromARGB(240, 0, 131, 143),
  //                       ),
  //                     ),
  //                   );
  //                 }

  //                 // Check for null or invalid user data
  //                 String userName = controller.currentUserData.value.fullName
  //                         ?.toUppercaseFirstLetter() ??
  //                     "";
  //                 if (userName == "NULL" || userName.isEmpty) {
  //                   // Add a small delay before logout to prevent immediate logout on page load
  //                   Future.delayed(Duration(seconds: 1), () {
  //                     if (userName == "NULL" || userName.isEmpty) {
  //                       // logout();
  //                     }
  //                   });
  //                 }

  //                 return SafeArea(
  //                   child: Column(
  //                     children: [
  //                       Container(
  //                         height: size.height * 0.20,
  //                         width: double.infinity,
  //                         margin: EdgeInsets.only(
  //                           left: size.width * 0.04,
  //                           right: size.width * 0.04,
  //                           top: size.height * 0.001,
  //                           bottom: size.height * 0.01,
  //                         ),
  //                         decoration: BoxDecoration(
  //                           borderRadius: BorderRadius.circular(12),
  //                           boxShadow: [
  //                             BoxShadow(
  //                               color: Colors.black.withOpacity(0.08),
  //                               blurRadius: 6,
  //                               spreadRadius: 1,
  //                               offset: const Offset(0, 3),
  //                             ),
  //                           ],
  //                         ),
  //                         child: _buildTopBannerCarousel(size),
  //                       ),
  //                       SizedBox(height: size.height * 0.0),
  //                       Container(
  //                         width: double.infinity,
  //                         margin: EdgeInsets.symmetric(
  //                           horizontal: size.width * 0.04,
  //                         ),
  //                         padding: EdgeInsets.all(size.width * 0.03),
  //                         decoration: BoxDecoration(
  //                           color: Colors.white,
  //                           border: Border.all(
  //                             color: const Color.fromARGB(240, 0, 131, 143),
  //                             width: 2.5,
  //                           ),
  //                           borderRadius: BorderRadius.circular(5),
  //                         ),
  //                         child: Column(
  //                           children: [
  //                             SizedBox(height: size.height * 0.01),
  //                             Row(
  //                               mainAxisAlignment:
  //                                   MainAxisAlignment.spaceEvenly,
  //                               children: [
  //                                 buildContainer(
  //                                   context,
  //                                   "Real Estate",
  //                                   () {
  //                                     showComingSoonDialog(context);
  //                                   },
  //                                   "assets/market1.png",
  //                                 ),
  //                                 buildContainer(
  //                                   context,
  //                                   "Vacancy",
  //                                   () {
  //                                     showComingSoonDialog(context);
  //                                   },
  //                                   "assets/vacancy.png",
  //                                 ),
  //                                 buildContainer(
  //                                   context,
  //                                   "Offers",
  //                                   () {
  //                                     showComingSoonDialog(context);
  //                                   },
  //                                   "assets/offer1.png",
  //                                 ),
  //                               ],
  //                             ),
  //                           ],
  //                         ),
  //                       ),
  //                       SizedBox(width: size.width * 0.02),
  //                       Container(
  //                         margin: EdgeInsets.only(
  //                           top: size.height * 0.02,
  //                           bottom: size.height * 0.01,
  //                         ),
  //                         child: Text(
  //                           'Our Services',
  //                           style: TextStyle(
  //                             fontFamily: 'hello',
  //                             fontSize: size.width * 0.06,
  //                             fontWeight: FontWeight.w900,
  //                             color: const Color.fromARGB(240, 0, 131, 143),
  //                             letterSpacing: 0.5,
  //                           ),
  //                         ),
  //                       ),
  //                       Expanded(
  //                         child: NotificationListener<ScrollNotification>(
  //                           onNotification: (ScrollNotification scrollInfo) {
  //                             return false;
  //                           },
  //                           child: RefreshIndicator(
  //                             onRefresh: _refreshData,
  //                             color: const Color.fromARGB(240, 0, 131, 143),
  //                             child: CustomScrollView(
  //                               controller: scrollController,
  //                               physics: const AlwaysScrollableScrollPhysics(
  //                                 parent: BouncingScrollPhysics(),
  //                               ),
  //                               slivers: [
  //                                 SliverPadding(
  //                                   padding: EdgeInsets.symmetric(
  //                                     horizontal: size.width * 0.04,
  //                                     vertical: size.height * 0.01,
  //                                   ),
  //                                   sliver: SliverToBoxAdapter(
  //                                     child: categories(),
  //                                   ),
  //                                 ),
  //                                 SliverToBoxAdapter(
  //                                   child: SizedBox(height: size.height * 0.02),
  //                                 ),
  //                                 SliverToBoxAdapter(
  //                                   child: Container(
  //                                     height: size.height * 0.18,
  //                                     margin: EdgeInsets.symmetric(
  //                                       horizontal: size.width * 0.04,
  //                                     ),
  //                                     decoration: BoxDecoration(
  //                                       borderRadius: BorderRadius.circular(12),
  //                                       boxShadow: [
  //                                         BoxShadow(
  //                                           color:
  //                                               Colors.black.withOpacity(0.08),
  //                                           blurRadius: 6,
  //                                           spreadRadius: 1,
  //                                           offset: const Offset(0, 3),
  //                                         ),
  //                                       ],
  //                                     ),
  //                                     child: _buildBottomBannerCarousel(size),
  //                                   ),
  //                                 ),
  //                                 SliverToBoxAdapter(
  //                                   child: SizedBox(height: size.height * 0.02),
  //                                 ),
  //                               ],
  //                             ),
  //                           ),
  //                         ),
  //                       ),
  //                     ],
  //                   ),
  //                 );
  //               }),
  //             );
  //           } else {
  //             return Scaffold(
  //               appBar: AppBar(
  //                 backgroundColor: Colors.red,
  //                 title: const Text('Connection Lost',
  //                     style: TextStyle(color: Colors.white)),
  //                 centerTitle: true,
  //               ),
  //               body: Container(
  //                 decoration: const BoxDecoration(
  //                   gradient: LinearGradient(
  //                     begin: Alignment.topCenter,
  //                     end: Alignment.bottomCenter,
  //                     colors: [
  //                       Color.fromARGB(255, 255, 245, 245),
  //                       Color.fromARGB(255, 255, 235, 235),
  //                     ],
  //                   ),
  //                 ),
  //                 child: Center(
  //                   child: Padding(
  //                     padding: EdgeInsets.all(size.width * 0.08),
  //                     child: Column(
  //                       mainAxisAlignment: MainAxisAlignment.center,
  //                       children: <Widget>[
  //                         Icon(Icons.wifi_off,
  //                             size: size.width * 0.2,
  //                             color: Colors.red.shade400),
  //                         SizedBox(height: size.height * 0.03),
  //                         Text(
  //                           'Oops! No Internet Connection',
  //                           textAlign: TextAlign.center,
  //                           style: TextStyle(
  //                             fontSize: size.width * 0.055,
  //                             fontWeight: FontWeight.bold,
  //                             color: Colors.red.shade700,
  //                           ),
  //                         ),
  //                         SizedBox(height: size.height * 0.02),
  //                         Text(
  //                           'Please check your connection and try again.',
  //                           textAlign: TextAlign.center,
  //                           style: TextStyle(
  //                             fontSize: size.width * 0.04,
  //                             color: Colors.grey.shade600,
  //                           ),
  //                         ),
  //                         SizedBox(height: size.height * 0.04),
  //                         ElevatedButton(
  //                           onPressed: () async {
  //                             var connectivityResult = ConnectivityResult.none;
  //                             if (connectivityResult !=
  //                                 ConnectivityResult.none) {
  //                               _refreshData();
  //                             } else {
  //                               errorToast(msg: 'No Internet');
  //                             }
  //                           },
  //                           style: ElevatedButton.styleFrom(
  //                             backgroundColor: Colors.red,
  //                             foregroundColor: Colors.white,
  //                             padding: EdgeInsets.symmetric(
  //                               horizontal: size.width * 0.08,
  //                               vertical: size.height * 0.015,
  //                             ),
  //                             shape: RoundedRectangleBorder(
  //                               borderRadius: BorderRadius.circular(25),
  //                             ),
  //                           ),
  //                           child: Text(
  //                             'Retry',
  //                             style: TextStyle(
  //                               fontSize: size.width * 0.045,
  //                               fontWeight: FontWeight.w600,
  //                             ),
  //                           ),
  //                         ),
  //                       ],
  //                     ),
  //                   ),
  //                 ),
  //               ),
  //             );
  //           }
  //         },
  //         child: Container(),
  //       ),
  //     ),
  //   );
  // }

  // Widget _buildHeaderIcon(IconData icon, double size, VoidCallback onTap) {
  //   return Container(
  //     margin: const EdgeInsets.symmetric(horizontal: 4),
  //     child: InkWell(
  //       onTap: onTap,
  //       borderRadius: BorderRadius.circular(20),
  //       child: Padding(
  //         padding: const EdgeInsets.all(8),
  //         child: Icon(
  //           icon,
  //           color: Colors.white,
  //           size: size,
  //         ),
  //       ),
  //     ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    final authController = Get.find<AuthController>();
    bool isGuest = authController.isUserGuest();
    Size size = MediaQuery.of(context).size;

    return WillPopScope(
      onWillPop: onWillPop,
      child: OfflineBuilder(
        connectivityBuilder: (BuildContext context,
            ConnectivityResult connectivity, Widget child) {
          final bool connected = true;
          consolelog("connection log");
          consolelog(connected);

          if (connected) {
            return Scaffold(
              backgroundColor: const Color.fromARGB(255, 255, 255, 255),
              key: _scaffoldState,
              drawer: isGuest ? null : myDrawer(context, work!),
              drawerEdgeDragWidth: isGuest ? 0 : 150,
              appBar: isGuest
                  ? AppBar(
                      backgroundColor: const Color.fromARGB(240, 0, 131, 143),
                      leading: IconButton(
                        icon: const Icon(Icons.arrow_back, color: Colors.white),
                        onPressed: () {
                          Get.to(() => LoginScreen());
                        },
                      ),
                      title: InkWell(
                        child: RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: 'Click on  ',
                                style: TextStyle(
                                  fontSize: size.width * 0.036,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              TextSpan(
                                text: 'Sign Up',
                                style: TextStyle(
                                  fontSize: size.width * 0.045,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      const Color.fromARGB(255, 247, 170, 102),
                                  decoration: TextDecoration.underline,
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    Get.to(() => UserRegistration());
                                  },
                              ),
                              TextSpan(
                                text: ' or ',
                                style: TextStyle(
                                  fontSize: size.width * 0.036,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              TextSpan(
                                text: 'Login',
                                style: TextStyle(
                                  fontSize: size.width * 0.045,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      const Color.fromARGB(255, 102, 204, 247),
                                  decoration: TextDecoration.underline,
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    Get.to(() => LoginScreen());
                                  },
                              ),
                              TextSpan(
                                text: ' to access all features.',
                                style: TextStyle(
                                  fontSize: size.width * 0.036,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      centerTitle: true,
                      elevation: 4,
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.vertical(
                          bottom: Radius.circular(10),
                        ),
                      ),
                    )
                  : PreferredSize(
                      preferredSize: Size.fromHeight(size.height * 0.07),
                      child: Container(
                        decoration: BoxDecoration(
                          color: const Color.fromRGBO(0, 131, 143, 1),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: SafeArea(
                          child: Row(
                            children: [
                              IconButton(
                                onPressed: () {
                                  _scaffoldState.currentState!.openDrawer();
                                },
                                icon: Icon(
                                  Icons.menu,
                                  size: size.width * 0.065,
                                  color: Colors.white,
                                ),
                              ),
                              SizedBox(width: size.width * 0.02),
                              Expanded(
                                child: Obx(() {
                                  // Get the user name
                                  String userName = controller
                                          .currentUserData.value.fullName
                                          ?.toUppercaseFirstLetter() ??
                                      "";

                                  // Only display if username is valid (not loading, null, or "NULL")
                                  if (!controller.isLoading.value &&
                                      userName.isNotEmpty &&
                                      userName != "NULL") {
                                    return Text(
                                      userName,
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: size.width * 0.045,
                                        fontWeight: FontWeight.w700,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    );
                                  }

                                  // Return empty container when loading or null
                                  return SizedBox.shrink();
                                }),
                              ),
                              _buildHeaderIcon(
                                Icons.person,
                                size.width * 0.07,
                                () => Get.to(() => const Profile()),
                              ),
                              _buildHeaderIcon(
                                Icons.search,
                                size.width * 0.07,
                                () => showSearch(
                                  context: context,
                                  delegate: DataSearch(services),
                                ),
                              ),
                              _buildNotificationIcon(size),
                              _buildHeaderIcon(
                                Icons.settings,
                                size.width * 0.07,
                                () => Get.to(() => const SettingPage()),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
              body: Obx(() {
                if (controller.isLoading.value) {
                  return const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Color.fromARGB(240, 0, 131, 143),
                      ),
                    ),
                  );
                }

                // Check for null or invalid user data
                String userName = controller.currentUserData.value.fullName
                        ?.toUppercaseFirstLetter() ??
                    "";
                if (userName == "NULL" || userName.isEmpty) {
                  // Add a small delay before logout to prevent immediate logout on page load
                  Future.delayed(Duration(seconds: 1), () {
                    if (userName == "NULL" || userName.isEmpty) {
                      // logout();
                    }
                  });
                }

                return SafeArea(
                  child: Column(
                    children: [
                      Container(
                        height: size.height * 0.20,
                        width: double.infinity,
                        margin: EdgeInsets.only(
                          left: size.width * 0.04,
                          right: size.width * 0.04,
                          top: size.height * 0.001,
                          bottom: size.height * 0.01,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.08),
                              blurRadius: 6,
                              spreadRadius: 1,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: _buildTopBannerCarousel(size),
                      ),
                      SizedBox(height: size.height * 0.0),
                      Container(
                        width: double.infinity,
                        margin: EdgeInsets.symmetric(
                          horizontal: size.width * 0.04,
                        ),
                        padding: EdgeInsets.all(size.width * 0.03),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(
                            color: const Color.fromARGB(240, 0, 131, 143),
                            width: 2.5,
                          ),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: Column(
                          children: [
                            SizedBox(height: size.height * 0.01),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                buildContainer(
                                  context,
                                  "Real Estate",
                                  () {
                                    showComingSoonDialog(context);
                                  },
                                  "assets/market1.png",
                                ),
                                buildContainer(
                                  context,
                                  "Vacancy",
                                  () {
                                    showComingSoonDialog(context);
                                  },
                                  "assets/vacancy.png",
                                ),
                                buildContainer(
                                  context,
                                  "Offers",
                                  () {
                                    showComingSoonDialog(context);
                                  },
                                  "assets/offer1.png",
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: size.width * 0.02),
                      Container(
                        margin: EdgeInsets.only(
                          top: size.height * 0.02,
                          bottom: size.height * 0.01,
                        ),
                        child: Text(
                          'Our Services',
                          style: TextStyle(
                            fontFamily: 'hello',
                            fontSize: size.width * 0.06,
                            fontWeight: FontWeight.w900,
                            color: const Color.fromARGB(240, 0, 131, 143),
                            letterSpacing: 0.5,
                          ),
                        ),
                      ),
                      Expanded(
                        child: NotificationListener<ScrollNotification>(
                          onNotification: (ScrollNotification scrollInfo) {
                            return false;
                          },
                          child: RefreshIndicator(
                            onRefresh: _refreshData,
                            color: const Color.fromARGB(240, 0, 131, 143),
                            child: CustomScrollView(
                              controller: scrollController,
                              physics: const AlwaysScrollableScrollPhysics(
                                parent: BouncingScrollPhysics(),
                              ),
                              slivers: [
                                SliverPadding(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: size.width * 0.04,
                                    vertical: size.height * 0.01,
                                  ),
                                  sliver: SliverToBoxAdapter(
                                    child: categories(),
                                  ),
                                ),
                                SliverToBoxAdapter(
                                  child: SizedBox(height: size.height * 0.02),
                                ),
                                SliverToBoxAdapter(
                                  child: Container(
                                    height: size.height * 0.18,
                                    margin: EdgeInsets.symmetric(
                                      horizontal: size.width * 0.04,
                                    ),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.08),
                                          blurRadius: 6,
                                          spreadRadius: 1,
                                          offset: const Offset(0, 3),
                                        ),
                                      ],
                                    ),
                                    child: _buildBottomBannerCarousel(size),
                                  ),
                                ),
                                SliverToBoxAdapter(
                                  child: SizedBox(height: size.height * 0.02),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }),
            );
          } else {
            return Scaffold(
              appBar: AppBar(
                backgroundColor: Colors.red,
                title: const Text('Connection Lost',
                    style: TextStyle(color: Colors.white)),
                centerTitle: true,
              ),
              body: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color.fromARGB(255, 255, 245, 245),
                      Color.fromARGB(255, 255, 235, 235),
                    ],
                  ),
                ),
                child: Center(
                  child: Padding(
                    padding: EdgeInsets.all(size.width * 0.08),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Icon(Icons.wifi_off,
                            size: size.width * 0.2, color: Colors.red.shade400),
                        SizedBox(height: size.height * 0.03),
                        Text(
                          'Oops! No Internet Connection',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: size.width * 0.055,
                            fontWeight: FontWeight.bold,
                            color: Colors.red.shade700,
                          ),
                        ),
                        SizedBox(height: size.height * 0.02),
                        Text(
                          'Please check your connection and try again.',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: size.width * 0.04,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        SizedBox(height: size.height * 0.04),
                        ElevatedButton(
                          onPressed: () async {
                            var connectivityResult = ConnectivityResult.none;
                            if (connectivityResult != ConnectivityResult.none) {
                              _refreshData();
                            } else {
                              errorToast(msg: 'No Internet');
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            padding: EdgeInsets.symmetric(
                              horizontal: size.width * 0.08,
                              vertical: size.height * 0.015,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                          ),
                          child: Text(
                            'Retry',
                            style: TextStyle(
                              fontSize: size.width * 0.045,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }
        },
        child: Container(),
      ),
    );
  }

  Widget _buildHeaderIcon(IconData icon, double size, VoidCallback onTap) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Icon(
            icon,
            color: Colors.white,
            size: size,
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationIcon(Size size) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: InkWell(
        onTap: () {
          Get.to(() => NotificationScreen(notifications: const []));
        },
        borderRadius: BorderRadius.circular(20),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Stack(
            children: [
              Icon(
                Icons.notifications,
                size: size.width * 0.07,
                color: Colors.white,
              ),
              notificationsController.seenStatus.value
                  ? const SizedBox()
                  : Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        width: size.width * 0.025,
                        height: size.width * 0.025,
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopBannerCarousel(Size size) {
    return Obx(() {
      final sharedPreferenceTopBanner = bannerController.topBanners;

      return ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: CarouselSlider(
          items: sharedPreferenceTopBanner.isEmpty
              ? [_buildDefaultBanner(size)]
              : sharedPreferenceTopBanner
                  .map((imageUrl) => _buildNetworkBanner(imageUrl, size))
                  .toList(),
          options: CarouselOptions(
            autoPlay: true,
            autoPlayInterval: const Duration(seconds: 5),
            autoPlayAnimationDuration: const Duration(milliseconds: 800),
            enlargeCenterPage: false,
            viewportFraction: 1.0,
            height: double.infinity,
            enableInfiniteScroll: sharedPreferenceTopBanner.length > 1,
            pauseAutoPlayOnTouch: true,
            scrollPhysics: const BouncingScrollPhysics(),
            autoPlayCurve: Curves.easeInOutCubic,
          ),
        ),
      );
    });
  }

  Widget _buildBottomBannerCarousel(Size size) {
    return Obx(() {
      final savedBottomBanners = bannerController.bottomBanners;

      return ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: savedBottomBanners.isEmpty
            ? _buildDefaultBanner(size)
            : CarouselSlider.builder(
                itemCount: savedBottomBanners.length,
                options: CarouselOptions(
                  enableInfiniteScroll: savedBottomBanners.length > 1,
                  autoPlay: true,
                  autoPlayInterval: const Duration(seconds: 4),
                  enlargeCenterPage: false,
                  viewportFraction: 1.0,
                  height: double.infinity,
                  autoPlayCurve: Curves.easeInOut,
                ),
                itemBuilder: (context, index, realIndex) {
                  return _buildNetworkBanner(savedBottomBanners[index], size);
                },
              ),
      );
    });
  }

  Widget _buildDefaultBanner(Size size) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color.fromARGB(240, 0, 131, 143),
            const Color.fromARGB(240, 0, 131, 143).withOpacity(0.8),
          ],
        ),
      ),
      child: Center(
        child: Image.asset(
          'assets/Logo.png',
          width: size.width * 0.4,
          height: size.width * 0.4,
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  Widget _buildNetworkBanner(String imageUrl, Size size) {
    return Container(
      width: double.infinity,
      child: Image.network(
        imageUrl,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.grey.shade300,
                  Colors.grey.shade100,
                  Colors.grey.shade300,
                ],
                stops: const [0.0, 0.5, 1.0],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: const Center(
              child: CircularProgressIndicator(
                valueColor: const AlwaysStoppedAnimation<Color>(
                  Color.fromARGB(240, 0, 131, 143),
                ),
                strokeWidth: 3,
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) => Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.grey.shade200,
                Colors.grey.shade300,
              ],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.broken_image_outlined,
                  size: size.width * 0.15,
                  color: Colors.grey.shade500,
                ),
                SizedBox(height: size.height * 0.01),
                Text(
                  "Image not available",
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: size.width * 0.035,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void showFormPopup(BuildContext context,
      {String? mobileNumber, String? address}) {
    final formKey = GlobalKey<FormState>();
    final Color primaryColor = Color.fromARGB(240, 0, 131, 143);
    final Color accentColor = Color.fromARGB(255, 255, 145, 0);
    final Color backgroundColor = Colors.white;
    final Map<String, IconData> serviceTypes = {
      "Electrical": Icons.electrical_services,
      "Plumbing": Icons.plumbing,
      "Masonry Works": Icons.construction,
      "Metal Works": Icons.construction,
      "Cleaning": Icons.cleaning_services,
      "Carpentry": Icons.handyman,
      "Tuition and Languages": Icons.school,
      "Music and Dance": Icons.music_note,
      "Paint and Painting": Icons.format_paint,
      "Gardener and Agriculture Works": Icons.grass,
      "Healthcare and Medicine": Icons.medical_services,
      "Veterinary and Pet Care": Icons.pets,
      "Fitness and Yoga": Icons.fitness_center,
      "Cook and Waiter": Icons.restaurant,
      "Home Care Staff": Icons.home,
      "Books and Stationery": Icons.book,
      "Printing and Press": Icons.print,
      "Waste Management": Icons.delete,
      "Catering and Rent": Icons.restaurant_menu,
      "Furniture and Home Decor": Icons.chair,
      "Vehicle": Icons.directions_car,
      "Travel and Tours": Icons.card_travel,
      "Training and Skill Program": Icons.school,
      "Event and Party": Icons.celebration,
      "Engineering": Icons.build,
      "Office Staff": Icons.business_center,
      "Advertisement and Promotion": Icons.campaign,
      "Dharmik Karyakram": Icons.temple_buddhist,
      "Many More": Icons.miscellaneous_services,
    };

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            bool hasAgreedToDisclaimer = false;
            String selectedService = "Plumbing";
            IconData serviceIcon =
                serviceTypes["Plumbing"] ?? Icons.miscellaneous_services;
            final mediaQuery = MediaQuery.of(context);
            final screenSize = mediaQuery.size;
            final screenWidth = screenSize.width;
            final screenHeight = screenSize.height;
            final bool isSmallScreen = screenWidth < 600;
            final bool isMediumScreen = screenWidth >= 600 && screenWidth < 900;
            final bool isLargeScreen = screenWidth >= 900;
            final dialogWidth = isSmallScreen
                ? screenWidth * 0.95
                : isMediumScreen
                    ? screenWidth * 0.8
                    : 550.0;
            final dialogHeight = isSmallScreen
                ? screenHeight * 0.8
                : isMediumScreen
                    ? screenHeight * 0.7
                    : screenHeight * 0.6;
            final titleFontSize = isSmallScreen
                ? 20.0
                : isMediumScreen
                    ? 22.0
                    : 24.0;
            final subtitleFontSize = isSmallScreen
                ? 12.0
                : isMediumScreen
                    ? 13.0
                    : 14.0;
            final labelFontSize = isSmallScreen ? 14.0 : 15.0;
            final buttonFontSize = isSmallScreen ? 14.0 : 16.0;
            final horizontalPadding = isSmallScreen
                ? 16.0
                : isMediumScreen
                    ? 20.0
                    : 24.0;
            final verticalPadding = isSmallScreen
                ? 16.0
                : isMediumScreen
                    ? 18.0
                    : 20.0;
            final fieldSpacing = isSmallScreen
                ? 16.0
                : isMediumScreen
                    ? 20.0
                    : 24.0;
            final iconSize = isSmallScreen
                ? 22.0
                : isMediumScreen
                    ? 24.0
                    : 28.0;
            final headerPadding = isSmallScreen
                ? EdgeInsets.symmetric(vertical: 12, horizontal: 16)
                : EdgeInsets.symmetric(vertical: 16, horizontal: 20);
            final buttonPadding = isSmallScreen
                ? EdgeInsets.symmetric(horizontal: 16, vertical: 10)
                : EdgeInsets.symmetric(horizontal: 20, vertical: 12);
            final serviceTypeController =
                TextEditingController(text: selectedService);
            final titleController = TextEditingController();
            final locationController = TextEditingController();
            final contactController = TextEditingController();
            final descriptionController = TextEditingController();
            contactController.text = mobileNumber ?? '';
            locationController.text = address ?? '';
            final bool isContactFieldEnabled = mobileNumber == null;
            addControllerDisposer() {
              if (!ModalRoute.of(context)!.isCurrent) {
                serviceTypeController.dispose();
                titleController.dispose();
                locationController.dispose();
                contactController.dispose();
                descriptionController.dispose();
              }
            }

            WidgetsBinding.instance.addPostFrameCallback((_) {
              addControllerDisposer();
            });
            final borderRadius = isSmallScreen ? 16.0 : 20.0;
            final fieldBorderRadius = isSmallScreen ? 8.0 : 12.0;

            return OrientationBuilder(
              builder: (context, orientation) {
                final isLandscape = orientation == Orientation.landscape;
                final effectiveDialogHeight = isLandscape && isSmallScreen
                    ? screenHeight * 0.9
                    : dialogHeight;

                return Theme(
                  data: Theme.of(context).copyWith(
                    colorScheme: Theme.of(context).colorScheme.copyWith(
                          primary: primaryColor,
                          secondary: accentColor,
                        ),
                    inputDecorationTheme: InputDecorationTheme(
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: primaryColor, width: 2.0),
                        borderRadius: BorderRadius.circular(fieldBorderRadius),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(fieldBorderRadius),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.red.shade300),
                        borderRadius: BorderRadius.circular(fieldBorderRadius),
                      ),
                      focusedErrorBorder: OutlineInputBorder(
                        borderSide:
                            BorderSide(color: Colors.red.shade500, width: 2.0),
                        borderRadius: BorderRadius.circular(fieldBorderRadius),
                      ),
                      fillColor: Colors.grey.shade50,
                      filled: true,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: horizontalPadding * 0.8,
                        vertical: verticalPadding * 0.6,
                      ),
                      hintStyle: TextStyle(
                        color: Colors.grey.shade500,
                        fontSize: isSmallScreen ? 14.0 : 15.0,
                      ),
                      labelStyle: TextStyle(
                        color: primaryColor,
                        fontSize: labelFontSize,
                      ),
                      errorStyle: TextStyle(
                        fontSize: isSmallScreen ? 12.0 : 13.0,
                      ),
                    ),
                    textButtonTheme: TextButtonThemeData(
                      style: TextButton.styleFrom(
                        foregroundColor: primaryColor,
                        textStyle: TextStyle(
                          fontSize: buttonFontSize,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    elevatedButtonTheme: ElevatedButtonThemeData(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: primaryColor,
                        foregroundColor: Colors.white,
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(fieldBorderRadius),
                        ),
                        textStyle: TextStyle(
                          fontSize: buttonFontSize,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  child: Dialog(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(borderRadius),
                    ),
                    elevation: 8,
                    backgroundColor: backgroundColor,
                    insetPadding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen
                          ? 10
                          : isMediumScreen
                              ? 24
                              : 40,
                      vertical: isSmallScreen ? (isLandscape ? 8 : 16) : 24,
                    ),
                    child: AnimatedContainer(
                      duration: Duration(milliseconds: 300),
                      width: dialogWidth,
                      constraints: BoxConstraints(
                        maxHeight: effectiveDialogHeight,
                        maxWidth: isLargeScreen ? 600 : double.infinity,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  primaryColor,
                                  primaryColor.withOpacity(0.8)
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(borderRadius),
                                topRight: Radius.circular(borderRadius),
                              ),
                            ),
                            padding: headerPadding,
                            child: Row(
                              children: [
                                Container(
                                  padding:
                                      EdgeInsets.all(isSmallScreen ? 8 : 10),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(
                                        fieldBorderRadius),
                                  ),
                                  child: Icon(
                                    serviceIcon,
                                    color: Colors.white,
                                    size: iconSize,
                                  ),
                                ),
                                SizedBox(width: isSmallScreen ? 12 : 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Post Urgent Need',
                                        style: TextStyle(
                                          fontSize: titleFontSize,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                      SizedBox(height: isSmallScreen ? 2 : 4),
                                      Text(
                                        'Get quick help from service providers',
                                        style: TextStyle(
                                          fontSize: subtitleFontSize,
                                          color: Colors.white.withOpacity(0.9),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                IconButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  icon: Icon(
                                    Icons.close,
                                    color: Colors.white,
                                    size: isSmallScreen ? 20 : 24,
                                  ),
                                  style: ButtonStyle(
                                    backgroundColor: MaterialStateProperty.all(
                                      const Color.fromARGB(240, 0, 131, 143),
                                    ),
                                    shape: MaterialStateProperty.all(
                                      RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(32),
                                      ),
                                    ),
                                    padding: MaterialStateProperty.all(
                                      const EdgeInsets.all(12),
                                    ),
                                  ),
                                  tooltip: 'Close',
                                  padding:
                                      EdgeInsets.all(isSmallScreen ? 4 : 8),
                                  constraints: BoxConstraints(),
                                ),
                              ],
                            ),
                          ),
                          Flexible(
                            child: SingleChildScrollView(
                              padding: EdgeInsets.symmetric(
                                horizontal: horizontalPadding,
                                vertical: verticalPadding,
                              ),
                              child: Form(
                                key: formKey,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    _buildFieldLabel('Service Type',
                                        primaryColor, labelFontSize),
                                    AnimatedContainer(
                                      duration: Duration(milliseconds: 200),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                            fieldBorderRadius),
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                primaryColor.withOpacity(0.1),
                                            blurRadius: 8,
                                            offset: Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: DropdownButtonFormField<String>(
                                        value: selectedService,
                                        decoration: InputDecoration(
                                          prefixIcon: Icon(
                                            serviceIcon,
                                            color: primaryColor,
                                            size: isSmallScreen ? 20 : 24,
                                          ),
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(
                                                fieldBorderRadius),
                                          ),
                                        ),
                                        items: serviceTypes.keys
                                            .map((String type) {
                                          return DropdownMenuItem<String>(
                                            value: type,
                                            child: Row(
                                              children: [
                                                Icon(
                                                  serviceTypes[type]!,
                                                  color: primaryColor,
                                                  size: isSmallScreen ? 18 : 20,
                                                ),
                                                SizedBox(
                                                    width:
                                                        isSmallScreen ? 8 : 12),
                                                Text(
                                                  type,
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w500,
                                                    fontSize:
                                                        isSmallScreen ? 14 : 15,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        }).toList(),
                                        onChanged: (value) {
                                          if (value != null) {
                                            setState(() {
                                              selectedService = value;
                                              serviceTypeController.text =
                                                  value;
                                              serviceIcon = serviceTypes[
                                                      value] ??
                                                  Icons.miscellaneous_services;
                                            });
                                          }
                                        },
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Please select a service type';
                                          }
                                          return null;
                                        },
                                        autovalidateMode:
                                            AutovalidateMode.onUserInteraction,
                                        icon: Icon(
                                          Icons.arrow_drop_down_circle,
                                          color: primaryColor,
                                          size: isSmallScreen ? 20 : 24,
                                        ),
                                        dropdownColor: Colors.white,
                                        isExpanded: true,
                                        itemHeight: isSmallScreen ? 48 : 56,
                                        menuMaxHeight: screenHeight * 0.4,
                                      ),
                                    ),
                                    SizedBox(height: fieldSpacing),
                                    _buildFieldLabel(
                                        'Title', primaryColor, labelFontSize),
                                    _buildAnimatedFormField(
                                      controller: titleController,
                                      hintText: 'What service do you need?',
                                      icon: Icons.title,
                                      color: primaryColor,
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Please enter a title';
                                        }
                                        return null;
                                      },
                                      isSmallScreen: isSmallScreen,
                                      borderRadius: fieldBorderRadius,
                                    ),
                                    SizedBox(height: fieldSpacing),

                                    // Location Field with animation
                                    _buildFieldLabel('Location', primaryColor,
                                        labelFontSize),
                                    _buildAnimatedFormField(
                                      controller: locationController,
                                      hintText:
                                          'Where do you need the service?',
                                      icon: Icons.location_on,
                                      color: primaryColor,
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Please enter your location';
                                        }
                                        return null;
                                      },
                                      isSmallScreen: isSmallScreen,
                                      borderRadius: fieldBorderRadius,
                                    ),
                                    SizedBox(height: fieldSpacing),

                                    // Contact Field with animation
                                    _buildFieldLabel(
                                        'Contact', primaryColor, labelFontSize),
                                    _buildAnimatedFormField(
                                      controller: contactController,
                                      hintText: 'Your phone number',
                                      icon: Icons.phone,
                                      color: primaryColor,
                                      keyboardType: TextInputType.phone,
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Please enter your contact number';
                                        }
                                        if (!RegExp(r'^\+?[0-9]{10,15}$')
                                            .hasMatch(value)) {
                                          return 'Please enter a valid phone number';
                                        }
                                        return null;
                                      },
                                      isSmallScreen: isSmallScreen,
                                      borderRadius: fieldBorderRadius,
                                      enabled: isContactFieldEnabled,
                                    ),
                                    SizedBox(height: fieldSpacing),

                                    // Description Field with animation
                                    _buildFieldLabel('Description',
                                        primaryColor, labelFontSize),
                                    AnimatedContainer(
                                      duration: Duration(milliseconds: 200),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                            fieldBorderRadius),
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                primaryColor.withOpacity(0.1),
                                            blurRadius: 8,
                                            offset: Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: TextFormField(
                                        controller: descriptionController,
                                        decoration: InputDecoration(
                                          hintText:
                                              'Describe your urgent need in detail',
                                          prefixIcon: Align(
                                            widthFactor: 1.0,
                                            heightFactor: 1.0,
                                            child: Padding(
                                              padding: EdgeInsets.only(
                                                left: isSmallScreen ? 10 : 12,
                                                top: isSmallScreen ? 10 : 12,
                                              ),
                                              child: Icon(
                                                Icons.description,
                                                color: primaryColor,
                                                size: isSmallScreen ? 20 : 24,
                                              ),
                                            ),
                                          ),
                                          alignLabelWithHint: true,
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(
                                                fieldBorderRadius),
                                          ),
                                        ),
                                        maxLines: isSmallScreen ? 3 : 4,
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Please enter a description';
                                          }
                                          return null;
                                        },
                                        autovalidateMode:
                                            AutovalidateMode.onUserInteraction,
                                        style: TextStyle(
                                          fontSize: isSmallScreen ? 14 : 15,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: fieldSpacing * 0.75),
                                    Container(
                                      padding: EdgeInsets.all(
                                          isSmallScreen ? 10 : 12),
                                      decoration: BoxDecoration(
                                        color: Colors.blue.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(
                                            fieldBorderRadius),
                                        border: Border.all(
                                            color:
                                                Colors.blue.withOpacity(0.3)),
                                      ),
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.info_outline,
                                            color: Colors.blue,
                                            size: isSmallScreen ? 18 : 22,
                                          ),
                                          SizedBox(
                                              width: isSmallScreen ? 8 : 12),
                                          Expanded(
                                            child: Text(
                                              'Service providers will contact you based on the information provided',
                                              style: TextStyle(
                                                fontSize:
                                                    isSmallScreen ? 12 : 13,
                                                color: Colors.blue.shade800,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: isSmallScreen ? 8 : 16),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const Divider(height: 1),
                          Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: horizontalPadding,
                              vertical: fieldSpacing *
                                  0.2, // Further reduced vertical padding
                            ),
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.yellow.withOpacity(0.1),
                                borderRadius:
                                    BorderRadius.circular(fieldBorderRadius),
                                border: Border.all(
                                  color: Colors.yellow.withOpacity(0.3),
                                ),
                              ),
                              padding: EdgeInsets.all(
                                  isSmallScreen ? 6 : 8), // Minimal padding
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize:
                                    MainAxisSize.min, // Use minimum space
                                children: [
                                  // Ultra compact header with disclaimer intro
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.warning_amber_rounded,
                                        color: Colors.orange,
                                        size: isSmallScreen
                                            ? 16
                                            : 18, // Even smaller icon
                                      ),
                                      SizedBox(width: 4), // Minimal spacing
                                      Text(
                                        'Disclaimer:',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: isSmallScreen
                                              ? 12
                                              : 13, // Smaller header
                                          color: Colors.orange,
                                        ),
                                      ),
                                    ],
                                  ),

                                  // Points container with scroll indicator
                                  Container(
                                    height: isSmallScreen
                                        ? 40
                                        : 45, // Extremely reduced height
                                    margin: EdgeInsets.symmetric(
                                        vertical: 4), // Minimal margin
                                    child: Row(
                                      children: [
                                        // Points section
                                        Expanded(
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(4),
                                            child: Container(
                                              decoration: BoxDecoration(
                                                color: Colors.white
                                                    .withOpacity(0.3),
                                                border: Border.all(
                                                  color: Colors.orange
                                                      .withOpacity(0.2),
                                                  width: 1,
                                                ),
                                              ),
                                              child: ListView(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 8,
                                                        vertical: 4),
                                                children: [
                                                  Text(
                                                    '• No offensive or inappropriate content',
                                                    style: TextStyle(
                                                        fontSize: isSmallScreen
                                                            ? 15
                                                            : 18,
                                                        fontWeight: FontWeight
                                                            .bold // Smaller text
                                                        ),
                                                  ),
                                                  const SizedBox(
                                                      height:
                                                          3), // Minimal spacing
                                                  Text(
                                                    '• No illegal activities or harassment',
                                                    style: TextStyle(
                                                        fontSize: isSmallScreen
                                                            ? 15
                                                            : 18,
                                                        fontWeight: FontWeight
                                                            .bold // Smaller text
                                                        ),
                                                  ),
                                                  const SizedBox(
                                                      height:
                                                          3), // Minimal spacing
                                                  Text(
                                                    '• Violation may result in account blocking',
                                                    style: TextStyle(
                                                        fontSize: isSmallScreen
                                                            ? 15
                                                            : 18,
                                                        fontWeight:
                                                            FontWeight.bold),
                                                  ),
                                                  const SizedBox(height: 3),
                                                  Text(
                                                    '• You are responsible for all content',
                                                    style: TextStyle(
                                                        fontSize: isSmallScreen
                                                            ? 15
                                                            : 18,
                                                        fontWeight: FontWeight
                                                            .bold // Smaller text
                                                        ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),

                                        // Scroll indicator
                                        Container(
                                          width: 14,
                                          margin: EdgeInsets.only(left: 4),
                                          decoration: BoxDecoration(
                                            color: Colors.grey.withOpacity(0.1),
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                          child: const Center(
                                            child: Icon(
                                              Icons.keyboard_arrow_down,
                                              size: 14,
                                              color: Colors.grey,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  // Ultra compact checkbox
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Transform.scale(
                                        scale: 0.8,
                                        child: Checkbox(
                                          value: hasAgreedToDisclaimer,
                                          onChanged: (bool? value) {
                                            setState(() {
                                              hasAgreedToDisclaimer =
                                                  value ?? false;
                                            });
                                          },
                                          activeColor: primaryColor,
                                          materialTapTargetSize:
                                              MaterialTapTargetSize.shrinkWrap,
                                          visualDensity: VisualDensity.compact,
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            hasAgreedToDisclaimer =
                                                !hasAgreedToDisclaimer;
                                          });
                                        },
                                        child: Text(
                                          'I agree to the disclaimer',
                                          style: TextStyle(
                                            fontSize: isSmallScreen ? 15 : 18,
                                            fontWeight: FontWeight.bold,
                                            color: hasAgreedToDisclaimer
                                                ? primaryColor
                                                : Colors.black87,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const Divider(height: 1),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: horizontalPadding * 0.75,
                              vertical: isSmallScreen
                                  ? 4
                                  : 6, // Further reduced padding
                            ),
                            child: (isLandscape && isSmallScreen)
                                ? Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.stretch,
                                    children: [
                                      ElevatedButton.icon(
                                        style: ElevatedButton.styleFrom(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 10, vertical: 0),
                                          minimumSize: Size(
                                            double.infinity,
                                            isSmallScreen ? 32 : 36,
                                          ),
                                        ),
                                        onPressed: hasAgreedToDisclaimer
                                            ? () => _submitForm(
                                                  context,
                                                  formKey,
                                                  serviceTypeController,
                                                  titleController,
                                                  locationController,
                                                  contactController,
                                                  descriptionController,
                                                  primaryColor,
                                                  isSmallScreen,
                                                  hasAgreedToDisclaimer,
                                                )
                                            : null,
                                        icon: Icon(
                                          Icons.send,
                                          size: isSmallScreen
                                              ? 14
                                              : 16, // Smaller icon
                                        ),
                                        label: Text('Post Now'),
                                      ),
                                      SizedBox(height: 4),
                                      TextButton.icon(
                                        style: TextButton.styleFrom(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 10,
                                              vertical: 0), // Minimal padding
                                        ),
                                        onPressed: () {
                                          Navigator.of(context).pop();
                                        },
                                        icon: Icon(
                                          Icons.cancel_outlined,
                                          size: isSmallScreen
                                              ? 14
                                              : 16, // Smaller icon
                                        ),
                                        label: Text('Cancel'),
                                      ),
                                    ],
                                  )
                                : Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      TextButton.icon(
                                        style: TextButton.styleFrom(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 10,
                                              vertical: 0), // Minimal padding
                                        ),
                                        onPressed: () {
                                          Navigator.of(context).pop();
                                        },
                                        icon: Icon(
                                          Icons.cancel_outlined,
                                          size: isSmallScreen
                                              ? 14
                                              : 16, // Smaller icon
                                        ),
                                        label: Text('Cancel'),
                                      ),
                                      SizedBox(
                                          width: isSmallScreen
                                              ? 4
                                              : 6), // Reduced spacing
                                      ElevatedButton.icon(
                                        style: ElevatedButton.styleFrom(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 10,
                                              vertical: 0), // Minimal padding
                                          minimumSize: Size(
                                            isSmallScreen
                                                ? 80
                                                : 90, // Narrower button
                                            isSmallScreen
                                                ? 32
                                                : 36, // Even shorter button
                                          ),
                                        ),
                                        onPressed: hasAgreedToDisclaimer
                                            ? () => _submitForm(
                                                  context,
                                                  formKey,
                                                  serviceTypeController,
                                                  titleController,
                                                  locationController,
                                                  contactController,
                                                  descriptionController,
                                                  primaryColor,
                                                  isSmallScreen,
                                                  hasAgreedToDisclaimer,
                                                )
                                            : null,
                                        icon: Icon(
                                          Icons.send,
                                          size: isSmallScreen
                                              ? 14
                                              : 16, // Smaller icon
                                        ),
                                        label: Text('Post Now'),
                                      ),
                                    ],
                                  ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

// Helper function to submit the form
  void _submitForm(
    BuildContext context,
    GlobalKey<FormState> formKey,
    TextEditingController serviceTypeController,
    TextEditingController titleController,
    TextEditingController locationController,
    TextEditingController contactController,
    TextEditingController descriptionController,
    Color primaryColor,
    bool isSmallScreen,
    bool hasAgreedToDisclaimer,
  ) {
    if (formKey.currentState!.validate()) {
      if (!hasAgreedToDisclaimer) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  Icons.warning,
                  color: Colors.white,
                  size: isSmallScreen ? 20 : 24,
                ),
                SizedBox(width: isSmallScreen ? 8 : 12),
                Expanded(
                  child: Text(
                    'Please agree to the disclaimer before submitting',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: isSmallScreen ? 14 : 15,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            duration: Duration(seconds: 3),
            margin: EdgeInsets.all(isSmallScreen ? 16 : 20),
          ),
        );
        return;
      }

      final serviceType = serviceTypeController.text;
      final title = titleController.text;
      final location = locationController.text;
      final contact = contactController.text;
      final description = descriptionController.text;

      // Here typically save the data or perform an action
      print('Service Type: $serviceType');
      print('Title: $title');
      print('Location: $location');
      print('Contact: $contact');
      print('Description: $description');
      print('Disclaimer Agreed: $hasAgreedToDisclaimer');

      // Close dialog
      Navigator.of(context).pop();

      // Show confirmation
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.white,
                size: isSmallScreen ? 20 : 24,
              ),
              SizedBox(width: isSmallScreen ? 8 : 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Urgent need posted successfully!',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: isSmallScreen ? 14 : 15,
                      ),
                    ),
                    SizedBox(height: 2),
                    Text(
                      'Service providers will contact you soon',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: isSmallScreen ? 11 : 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          backgroundColor: primaryColor,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          duration: Duration(seconds: 4),
          action: SnackBarAction(
            label: 'OK',
            textColor: Colors.white,
            onPressed: () {},
          ),
          margin: EdgeInsets.all(isSmallScreen ? 16 : 20),
        ),
      );
    }
  }

  Widget _buildFieldLabel(String label, Color color, double fontSize) {
    return Padding(
      padding: EdgeInsets.only(bottom: 10.0, left: 4.0),
      child: Row(
        children: [
          Text(
            '$label',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: fontSize,
              color: color.withOpacity(0.9),
            ),
          ),
          if (label != 'Description')
            Text(
              '*',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: fontSize,
                color: Colors.red.shade400,
              ),
            ),
        ],
      ),
    );
  }

  void showComingSoonDialog(BuildContext context) {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8, // Responsive width
          padding: EdgeInsets.symmetric(
            vertical: MediaQuery.of(context).size.height * 0.03,
            horizontal: MediaQuery.of(context).size.width * 0.05,
          ),
          decoration: BoxDecoration(
            color: const Color.fromARGB(240, 0, 131, 143),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "We’re Sorry! 😔",
                style: TextStyle(
                  fontSize: MediaQuery.of(context).size.aspectRatio * 40,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: MediaQuery.of(context).size.height * 0.02),
              Text(
                "This feature is currently unavailable. We sincerely apologize for the inconvenience and appreciate your patience.\n\nIn the meantime, feel free to explore our other services that might be helpful for you! 🚀",
                style: TextStyle(
                  fontSize: MediaQuery.of(context).size.aspectRatio * 35,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: MediaQuery.of(context).size.height * 0.03),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    horizontal: MediaQuery.of(context).size.width * 0.1,
                    vertical: MediaQuery.of(context).size.height * 0.015,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onPressed: () {
                  Get.back();
                },
                child: Text(
                  "Okay",
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.aspectRatio * 30,
                    color: const Color.fromARGB(240, 0, 131, 143),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedFormField({
    required TextEditingController controller,
    required String hintText,
    required IconData icon,
    required Color color,
    required bool isSmallScreen,
    required double borderRadius,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    bool enabled = true,
  }) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 200),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          hintText: hintText,
          prefixIcon: Icon(
            icon,
            color: color,
            size: isSmallScreen ? 20 : 24,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
        ),
        keyboardType: keyboardType,
        validator: validator,
        autovalidateMode: AutovalidateMode.disabled,
        style: TextStyle(
          fontSize: isSmallScreen ? 14 : 15,
          color: enabled ? Colors.black87 : Colors.grey.shade700,
        ),
        enabled: enabled,
      ),
    );
  }

  buildContainer(context, String name, VoidCallback ontap, String image) {
    Size size = MediaQuery.of(context).size;
    return InkWell(
      onTap: () {
        ontap.call();
      },
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              image:
                  DecorationImage(image: AssetImage(image), fit: BoxFit.cover),
              // color: Theme.of(context).primaryColor,
              //borderRadius: BorderRadius.circular(4)
              // borderRadius: BorderRadius.circular(10000)
            ),
          ),
          SizedBox(
            height: size.height * 0.005,
          ),
          Text(
            name,
            style: const TextStyle(
                color: Colors.black, // color of market vacancy offer
                fontSize: 12,
                fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  categories() {
    return Row(
      children: [
        Expanded(
          child: GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                // childAspectRatio: 0.16 / 0.13,
                mainAxisExtent: 80,
                crossAxisSpacing: 10,
                mainAxisSpacing: 20,
              ),
              scrollDirection: Axis.vertical,
              itemCount: myServices.length,
              itemBuilder: (context, index) {
                // final product = catController.products[index];
                final service = myServices[index];
                return InkWell(
                  onTap: () {
                    Get.to(() => ServicesScreen(
                          name: service.name,
                          id: service.id.toString(),
                        ));
                  },
                  child: Column(
                    children: [
                      Expanded(
                        child: Image.asset(service.imageUrl
                            // fit: BoxFit.cover,
                            ),
                      ),
                      const SizedBox(height: 1),
                      Text(
                        service.name,
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        style: const TextStyle(
                          overflow: TextOverflow.ellipsis,
                          fontFamily: 'hello',
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                        ),
                      )
                    ],
                  ),
                );
              }),
        ),
      ],
    );
  }

  slider(BuildContext context) {
    Size size = MediaQuery.of(context).size;

    return CarouselSlider(
      options: CarouselOptions(
          aspectRatio: 16 / 9,
          viewportFraction: 1.0,
          autoPlayInterval: Duration(seconds: myTime),
          height: size.height * 0.25,
          enableInfiniteScroll: false,
          enlargeFactor: 0.3,
          autoPlay: true),
      items: topImages
          .map((e) => Stack(
                fit: StackFit.expand,
                children: [
                  //  Image.asset(
                  //   e,
                  //   fit: BoxFit.cover,
                  // ),
                  Image.network(
                    "$baseUrl/api/allimg/image/$e",
                    headers: {
                      "Authorization": "Bearer $token",
                    },
                  ),
                ],
              ))
          .toList(),
    );
  }

  secondSlider(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return CarouselSlider(
      options: CarouselOptions(
          aspectRatio: 16 / 9,
          viewportFraction: 1,
          autoPlayInterval: Duration(seconds: myTime),
          height: size.height * 0.25,
          enableInfiniteScroll: false,
          autoPlay: true),
      items: bottomImages
          .map((e) => Stack(
                fit: StackFit.expand,
                children: [
                  // Image.asset(
                  //   e,
                  //   fit: BoxFit.cover,
                  // ),
                  Image.network(
                    "$baseUrl/api/allimg/image/$e",
                    headers: {
                      "Authorization": "Bearer $token",
                    },
                  ),
                ],
              ))
          .toList(),
    );
  }

  final List<int> indexx = [7, 8, 9, 10, 11, 12];
}

class Service {
  final int id;
  final String name;
  final String imageUrl;

  Service(this.id, this.name, this.imageUrl);
}

class DataSearch extends SearchDelegate<String> {
  final List<String> data;

  DataSearch(this.data);

  @override
  ThemeData appBarTheme(BuildContext context) {
    // Set the status bar to light content (white text)
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light, // White icons
        statusBarBrightness: Brightness.dark, // For iOS
      ),
    );

    return Theme.of(context).copyWith(
      appBarTheme: const AppBarTheme(
        backgroundColor: Color.fromRGBO(0, 131, 143, 1),
        foregroundColor: Colors.white,
        iconTheme: IconThemeData(color: Colors.white),
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.w500,
        ),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
        ),
      ),
      inputDecorationTheme: const InputDecorationTheme(
        hintStyle: TextStyle(color: Colors.white70),
      ),
      textTheme: const TextTheme(
        titleLarge: TextStyle(
          color: Colors.white,
          fontSize: 18,
        ),
      ),
    );
  }

  @override
  String get searchFieldLabel => 'Search services...';

  @override
  TextStyle get searchFieldStyle => const TextStyle(
        color: Colors.white,
        fontSize: 16,
      );

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear, color: Colors.white),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back, color: Colors.white),
      onPressed: () {
        // Reset status bar when closing search
        SystemChrome.setSystemUIOverlayStyle(
          const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.light,
            statusBarBrightness: Brightness.dark,
          ),
        );
        close(context, '');
      },
    );
  }

  Future<List<Map<String, dynamic>>> _fetchTokenAndSearch(String query) async {
    if (query.isEmpty) {
      return [];
    }

    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString("token");

      if (token == null || token.isEmpty) {
        return [];
      }

      var headers = {
        'Authorization': 'Bearer $token',
      };

      var url = Uri.parse('$baseUrl/api/service/search?name=$query');
      var response = await http.get(url, headers: headers);

      if (response.statusCode == 200) {
        var data = json.decode(response.body) as List<dynamic>;
        consolelog("pp $data");
        return data.map((service) {
          return {
            'id': service['id'] as int,
            'name': service['name'] as String,
            'categoryTitle': service['categoryTitle'] as String,
          };
        }).toList();
      } else {
        print('Error: ${response.statusCode} - ${response.reasonPhrase}');
      }
    } catch (e) {
      print('Exception: $e');
    }

    return [];
  }

  @override
  Widget buildResults(BuildContext context) {
    final controller1 = Get.put(AuthController());
    var name1 = "";
    int id = 0;

    // Responsive dimensions
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isTablet = screenWidth > 600;
    final containerPadding = screenWidth * 0.04;
    final iconSize = screenWidth * 0.06;
    final titleFontSize = screenWidth * 0.045;
    final subtitleFontSize = screenWidth * 0.035;

    return Container(
      width: screenWidth,
      height: screenHeight,
      color: const Color(0xFFF8F9FA),
      child: FutureBuilder<List<Map<String, dynamic>>>(
        future: _fetchTokenAndSearch(query),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: screenWidth * 0.15,
                    height: screenWidth * 0.15,
                    padding: EdgeInsets.all(screenWidth * 0.05),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(screenWidth * 0.04),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 2,
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: CircularProgressIndicator(
                      valueColor: const AlwaysStoppedAnimation<Color>(
                          Color(0xFF4A90E2)),
                      strokeWidth: screenWidth * 0.008,
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.03),
                  Text(
                    'Searching for services...',
                    style: TextStyle(
                      fontSize: titleFontSize,
                      color: const Color(0xFF6B7280),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }

          if (snapshot.hasError) {
            return Center(
              child: Container(
                margin: EdgeInsets.all(containerPadding),
                padding: EdgeInsets.all(containerPadding * 1.5),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(screenWidth * 0.04),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 2,
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: const Color(0xFFEF4444),
                      size: iconSize * 1.5,
                    ),
                    SizedBox(height: screenHeight * 0.02),
                    Text(
                      'Something went wrong',
                      style: TextStyle(
                        fontSize: titleFontSize,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF374151),
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.01),
                    Text(
                      'Please try again',
                      style: TextStyle(
                        fontSize: subtitleFontSize,
                        color: const Color(0xFF6B7280),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
          }

          if (snapshot.hasData && snapshot.data!.isNotEmpty) {
            final result = snapshot.data!.first;
            id = result['id'];
            name1 = result['name'];
            controller1.pickedJobFieldName.value = id.toString();
            consolelog("Navigating to MapScreen with: $name1, $id");

            WidgetsBinding.instance.addPostFrameCallback((_) async {
              final returnedName = await Navigator.push(
                context,
                PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      MapScreen(
                    work: "",
                    name: name1.toString(),
                  ),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return SlideTransition(
                      position: animation.drive(
                        Tween(begin: const Offset(1.0, 0.0), end: Offset.zero),
                      ),
                      child: child,
                    );
                  },
                ),
              );
              if (returnedName != null) {
                consolelog("Returned name: $returnedName");
                query = "";
                controller1.pickedJobFieldName.value = "0";
                Navigator.pop(context);
              } else {
                consolelog("No data returned.");
                query = "";
                controller1.pickedJobFieldName.value = "0";
                Navigator.pop(context);
              }
              Navigator.pop(context);
            });

            return Center(
              child: Container(
                margin: EdgeInsets.all(containerPadding),
                padding: EdgeInsets.all(containerPadding * 1.5),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(screenWidth * 0.04),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 2,
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
              ),
            );
          } else {
            return Center(
              child: Container(
                margin: EdgeInsets.all(containerPadding),
                padding: EdgeInsets.all(containerPadding * 1.5),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(screenWidth * 0.04),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 2,
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.search_off,
                      color: const Color(0xFF9CA3AF),
                      size: iconSize * 1.5,
                    ),
                    SizedBox(height: screenHeight * 0.02),
                    Text(
                      'No results found',
                      style: TextStyle(
                        fontSize: titleFontSize,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF374151),
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.01),
                    Text(
                      'Try different keywords',
                      style: TextStyle(
                        fontSize: subtitleFontSize,
                        color: const Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }
        },
      ),
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    // Responsive dimensions
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isTablet = screenWidth > 600;
    final containerPadding = screenWidth * 0.04;
    final itemHeight = screenHeight * 0.08;
    final iconSize = screenWidth * 0.06;
    final titleFontSize = screenWidth * 0.045;
    final subtitleFontSize = screenWidth * 0.035;
    final borderRadius = screenWidth * 0.04;

    return Container(
      width: screenWidth,
      height: screenHeight,
      color: const Color(0xFFF8F9FA),
      child: FutureBuilder<List<Map<String, dynamic>>>(
        future: _fetchTokenAndSearch(query),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: screenWidth * 0.15,
                    height: screenWidth * 0.15,
                    padding: EdgeInsets.all(screenWidth * 0.05),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(screenWidth * 0.04),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 2,
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: CircularProgressIndicator(
                      valueColor: const AlwaysStoppedAnimation<Color>(
                          Color(0xFF4A90E2)),
                      strokeWidth: screenWidth * 0.008,
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.03),
                  Text(
                    'Loading suggestions...',
                    style: TextStyle(
                      fontSize: titleFontSize,
                      color: const Color(0xFF6B7280),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }

          if (snapshot.hasError) {
            return Center(
              child: Container(
                margin: EdgeInsets.all(containerPadding),
                padding: EdgeInsets.all(containerPadding * 1.5),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(borderRadius),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 2,
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: const Color(0xFFEF4444),
                      size: iconSize * 1.5,
                    ),
                    SizedBox(height: screenHeight * 0.02),
                    Text(
                      'Unable to load',
                      style: TextStyle(
                        fontSize: titleFontSize,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF374151),
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.01),
                    Text(
                      'Check connection and try again',
                      style: TextStyle(
                        fontSize: subtitleFontSize,
                        color: const Color(0xFF6B7280),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
          }

          if (snapshot.hasData && snapshot.data!.isNotEmpty) {
            final suggestions = snapshot.data!
                .where((service) =>
                    service['name'] != null &&
                    (service['name']
                            .toString()
                            .toLowerCase()
                            .contains(query.toLowerCase()) ||
                        service['categoryTitle']
                            .toString()
                            .toLowerCase()
                            .contains(query.toLowerCase())))
                .toList();

            if (suggestions.isEmpty) {
              return Center(
                child: Container(
                  margin: EdgeInsets.all(containerPadding),
                  padding: EdgeInsets.all(containerPadding * 1.5),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(borderRadius),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        spreadRadius: 2,
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.search_off,
                        color: const Color(0xFF9CA3AF),
                        size: iconSize * 1.5,
                      ),
                      SizedBox(height: screenHeight * 0.02),
                      Text(
                        'No matching services',
                        style: TextStyle(
                          fontSize: titleFontSize,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF374151),
                        ),
                      ),
                      SizedBox(height: screenHeight * 0.01),
                      Text(
                        'Try different search terms',
                        style: TextStyle(
                          fontSize: subtitleFontSize,
                          color: const Color(0xFF6B7280),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }

            return Container(
              padding: EdgeInsets.symmetric(
                horizontal: containerPadding,
                vertical: screenHeight * 0.02,
              ),
              child: ListView.separated(
                itemCount: suggestions.length,
                separatorBuilder: (context, index) =>
                    SizedBox(height: screenHeight * 0.015),
                itemBuilder: (context, index) {
                  final service = suggestions[index];
                  final serviceName = service['name'] ?? 'Unknown Service';
                  final categoryTitle =
                      service['categoryTitle'] ?? 'Unknown Category';

                  return AnimatedContainer(
                    duration: Duration(milliseconds: 300 + (index * 50)),
                    curve: Curves.easeOutBack,
                    child: Container(
                      height: itemHeight,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(borderRadius),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.08),
                            spreadRadius: 1,
                            blurRadius: 6,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(borderRadius),
                          onTap: () {
                            query = service['name'];
                            showResults(context);
                          },
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: containerPadding,
                              vertical: screenHeight * 0.01,
                            ),
                            child: Row(
                              children: [
                                Container(
                                  width: iconSize * 1.2,
                                  height: iconSize * 1.2,
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFE0F2FE),
                                    borderRadius: BorderRadius.circular(
                                        screenWidth * 0.025),
                                  ),
                                  child: Icon(
                                    Icons.business_center,
                                    color:
                                        const Color.fromARGB(240, 0, 131, 143),
                                    size: iconSize * 0.7,
                                  ),
                                ),
                                SizedBox(width: screenWidth * 0.04),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        serviceName,
                                        style: TextStyle(
                                          fontSize: titleFontSize,
                                          fontWeight: FontWeight.w600,
                                          color: const Color(0xFF374151),
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      SizedBox(height: screenHeight * 0.002),
                                      Text(
                                        categoryTitle,
                                        style: TextStyle(
                                          fontSize: subtitleFontSize,
                                          fontWeight: FontWeight.w400,
                                          color: const Color(0xFF6B7280),
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ),
                                ),
                                Icon(
                                  Icons.arrow_forward_ios,
                                  color: const Color(0xFF9CA3AF),
                                  size: screenWidth * 0.04,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            );
          } else {
            return Center(
              child: Container(
                margin: EdgeInsets.all(containerPadding),
                padding: EdgeInsets.all(containerPadding * 1.5),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(borderRadius),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 2,
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.category_outlined,
                      color: const Color(0xFF9CA3AF),
                      size: iconSize * 1.5,
                    ),
                    SizedBox(height: screenHeight * 0.02),
                    Text(
                      'Services are available',
                      style: TextStyle(
                        fontSize: titleFontSize,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF374151),
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.01),
                    Text(
                      'Start typing to search',
                      style: TextStyle(
                        fontSize: subtitleFontSize,
                        color: const Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }
        },
      ),
    );
  }
}
