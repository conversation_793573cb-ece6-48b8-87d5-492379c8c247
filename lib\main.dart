// // ignore_for_file: public_member_api_docs, sort_constructors_first

// import 'package:flutter/material.dart';
// import 'package:flutter_dotenv/flutter_dotenv.dart';
// import 'package:get/get_navigation/src/root/get_material_app.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smartsewa/core/development/console.dart';
// import 'package:smartsewa/core/enum.dart';
// import 'package:smartsewa/views/Theme/theme.dart';
// import 'package:smartsewa/views/widgets/Welcome%20Screen/splashscreen.dart';
// import 'package:upgrader/upgrader.dart';
// import 'package:flutter_localizations/flutter_localizations.dart';

// void main() async {
//   WidgetsFlutterBinding.ensureInitialized();
//   await dotenv.load(fileName: ".env");
//   SharedPreferences prefs = await SharedPreferences.getInstance();
//   final String? apptoken = prefs.getString("token");
//   final bool? workStatus = prefs.getBool("workStatus");
//   logger(apptoken.toString(), loggerType: LoggerType.success);

//   runApp(
//     MyApp(
//       apptoken: apptoken,
//       workStatus: workStatus,
//     ),
//   );
// }

// class MyApp extends StatelessWidget {
//   final String? apptoken;
//   final bool? workStatus;
//   const MyApp({
//     super.key,
//     this.apptoken,
//     this.workStatus,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return GetMaterialApp(
//       supportedLocales: const [
//         Locale('en', 'US'),
//         Locale('ne', 'NP'),
//       ],
//       localizationsDelegates: const [
//         GlobalMaterialLocalizations.delegate,
//         GlobalWidgetsLocalizations.delegate,
//         GlobalCupertinoLocalizations.delegate,
//       ],
//       theme: MyTheme().appTheme(),
//       debugShowCheckedModeBanner: false,
//       home: FutureBuilder(
//         future: Future.delayed(const Duration(seconds: 15)),
//         builder: (context, snapshot) {
//           return UpgradeAlert(
//             upgrader: Upgrader(
//               debugLogging: true,
//               debugDisplayAlways: true, // Set to false in production
//               countryCode: 'NP',
//               durationUntilAlertAgain: const Duration(minutes: 1),
//               minAppVersion: '1.0.61',
//               messages: UpgraderMessages(code: 'ne'),
//             ),
//             child: const SplashScreen(),
//           );
//         },
//       ),
//     );
//   }
// }

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get_navigation/src/root/get_material_app.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/core/enum.dart';
import 'package:smartsewa/views/Theme/theme.dart';
import 'package:smartsewa/views/widgets/Welcome%20Screen/splashscreen.dart';
import 'package:upgrader/upgrader.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: ".env");
  SharedPreferences prefs = await SharedPreferences.getInstance();
  final String? apptoken = prefs.getString("token");
  final bool? workStatus = prefs.getBool("workStatus");
  logger(apptoken.toString(), loggerType: LoggerType.success);
  runApp(
    MyApp(
      apptoken: apptoken,
      workStatus: workStatus,
    ),
  );
}

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class MyApp extends StatelessWidget {
  final String? apptoken;
  final bool? workStatus;

  MyApp({super.key, this.apptoken, this.workStatus});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      navigatorKey: navigatorKey,
      supportedLocales: const [
        Locale('en', 'US'),
        Locale('ne', 'NP'),
      ],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      theme: MyTheme().appTheme(),
      debugShowCheckedModeBanner: false,

      /// 👇 THIS ensures Upgrader wraps ALL pages globally
      builder: (context, child) => UpgradeAlert(
        navigatorKey: navigatorKey,
        showIgnore: false,
        showLater: true,
        upgrader: Upgrader(
          debugLogging: true,
          debugDisplayAlways: false, // Set to false in production
          countryCode: 'NP',
          minAppVersion: '1.0.50',
          durationUntilAlertAgain: const Duration(days: 1),
          willDisplayUpgrade: ({
            required bool display,
            String? installedVersion,
            UpgraderVersionInfo? versionInfo,
          }) {
            if (display) {
              consolelog('Upgrade dialog WILL be shown');
            } else {
              consolelog('App is up to date');
            }
          },
        ),
        child: child ?? const SplashScreen(),
      ),

      home: const SplashScreen(), // Fallback
    );
  }
}
