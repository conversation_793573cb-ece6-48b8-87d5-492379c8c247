import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:smartsewa/views/user_screen/order_status/sent_request.dart';

import 'history.dart';
import 'ongoing_service_user.dart';

class ServiceStatus extends StatelessWidget {
  const ServiceStatus({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          systemOverlayStyle: const SystemUiOverlayStyle(
              statusBarColor: Color.fromARGB(240, 0, 96, 100)),
          backgroundColor: const Color.fromARGB(240, 0, 131, 143),
          iconTheme: const IconThemeData(color: Colors.white),
          centerTitle: true,
          title: const Text(
            'Service Status',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w500,
              color:
                  Color.fromARGB(255, 255, 255, 255), //color of service status
            ),
          ),
          bottom: TabBar(
              indicator: const UnderlineTabIndicator(
                  borderSide:
                      BorderSide(color: Color.fromARGB(240, 0, 131, 143))),
              indicatorColor: Theme.of(context).secondaryHeaderColor,
              labelColor: Colors.amber,
              //dividerColor: Color.fromARGB(239, 207, 9, 9),
              unselectedLabelColor: Colors.white,
              labelStyle: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
              tabs: const [
                Tab(
                  child: Text(
                    'Requested',
                  ),
                ),
                Tab(
                  child: Text('Ongoing'),
                ),
                Tab(
                  child: Text('History'),
                ),
              ]),
        ),
        body: TabBarView(
          children: [
            const SentRequest(),
            const OngoingServiceUser(),
            HistoryScreen()
          ],
        ),
      ),
    );
  }
}
