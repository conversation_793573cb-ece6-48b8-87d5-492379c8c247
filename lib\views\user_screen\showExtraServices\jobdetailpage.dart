import 'package:flutter/material.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';

class JobDetailPage extends StatelessWidget {
  const JobDetailPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final themeColor = const Color.fromARGB(240, 0, 131, 143);

    return Scaffold(
      appBar: myAppbar(context, true, "Your Perfect Jobs"),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Company Header
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(Icons.business,
                        color: Colors.white, size: 30),
                  ),
                  const SizedBox(width: 16),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Daraz',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Text(
                          'Software Engineer',
                          style: TextStyle(fontSize: 14),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(Icons.location_on, size: 16),
                            const Text(
                              'Gaidhara, Kathmandu',
                              style: TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            const Icon(Icons.access_time, size: 16),
                            const Text(
                              '8 days left',
                              style: TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.share,
                        color: Colors.grey), // Share icon
                    onPressed: () {
                      // Call share function
                    },
                  ),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: themeColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                    onPressed: () {},
                    child: const Text('Apply Now'),
                  ),
                ],
              ),
            ),

            // Application Timeline
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              color: Colors.grey[200],
              child: Row(
                children: [
                  const Icon(Icons.access_time, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'Apply Before: 8 Days 15 Hours Left',
                    style: TextStyle(fontSize: 12, color: Colors.grey[700]),
                  ),
                ],
              ),
            ),

            // Basic Information
            _buildSection(
              'Basic Information',
              themeColor,
              [
                _buildInfoRow(
                    Icons.category, 'Job Category', 'Software Engineer'),
                _buildInfoRow(Icons.trending_up, 'Job Level', 'Senior Level'),
                _buildInfoRow(Icons.people, 'No. of vacancies', '5'),
                _buildInfoRow(Icons.timer, 'Employment time', 'Full'),
                _buildInfoRow(
                    Icons.location_on, 'Job Location', 'Kathmandu, Jawalakhel'),
                _buildInfoRow(
                    Icons.attach_money, 'Offered Salary', 'NRS (55-120K)'),
                _buildInfoRow(Icons.calendar_today, 'Deadline', '2024-01-26'),
                _buildInfoRow(Icons.work, 'Job Position', 'Senior Software'),
              ],
            ),

            // Job Specification
            _buildSection(
              'Job Specification',
              themeColor,
              [
                _buildSubSection('Education Degree :', 'Bachelor Degree'),
                _buildSubSection(
                    'Education :', 'Bachelor Degree in Relevant Field'),
                _buildSubSection('Experience :', '5 years'),
              ],
            ),

            // Other Specification
            _buildSection(
              'Other Specification',
              themeColor,
              [
                _buildListSection(
                  'Position Requirement',
                  [
                    'To develop the application of the company also the web application.',
                    'To educate junior developers.',
                    'To coordinate with other developers.',
                  ],
                ),
                _buildListSection(
                  'Functional Requirement',
                  [
                    'Experience in application development and related groundwork.',
                    'Ability to produce leads and cues from everywhere possible.',
                    'Expertise in development design.',
                    'To follow up effectively and consistently.',
                  ],
                ),
                _buildListSection(
                  'Roles and Responsibilities',
                  [
                    'To develop the application of the company also the web application.',
                    'To educate junior developers.',
                    'To coordinate with junior developers.',
                    'Experience in application development and related groundwork.',
                    'Ability to produce leads and cues from everywhere possible.',
                    'Expertise in development design.',
                    'To follow up effectively and consistently.',
                  ],
                ),
              ],
            ),

            // Apply Now Button at bottom
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: themeColor,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onPressed: () {},
                  child: const Text('Apply Now'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, Color themeColor, List<Widget> children) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            color: themeColor,
            width: double.infinity,
            child: Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              label,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget _buildSubSection(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 8),
          Text(value),
        ],
      ),
    );
  }

  Widget _buildListSection(String title, List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        ...items
            .map((item) => Padding(
                  padding: const EdgeInsets.only(left: 16, bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('• '),
                      Expanded(child: Text(item)),
                    ],
                  ),
                ))
            .toList(),
      ],
    );
  }
}
