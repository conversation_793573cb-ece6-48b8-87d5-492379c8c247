import 'package:flutter/material.dart';
import 'package:smartsewa/utils/get_swatch_color.dart';

class MyTheme {
  appTheme() {
    return ThemeData(
        textTheme: const TextTheme(
          displayLarge: TextStyle(
            color: Color.fromARGB(240, 0, 131, 143),
            //color: Color(0xFF2E7D32),
            fontSize: 24, //prajwal change 32 to 24
            fontWeight: FontWeight.w600,
          ),
          displayMedium: TextStyle(
            color: Colors.black,
            fontSize: 18, //prajwal change 32 to 24
            fontWeight: FontWeight.w500,
          ),
          titleLarge: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Color(0xFF889AAD),
          ),
        ),
        // primarySwatch: getSwatchColor(const Color(0xFF86E91A)),
        colorScheme: ColorScheme.fromSeed(
          seedColor: MaterialColor(0xFF86E91A,
              getSwatchColor(const Color.fromARGB(240, 0, 131, 143))),
          primary: const Color.fromARGB(240, 0, 131, 143),
          secondary: const Color.fromARGB(240, 0, 131, 143),
          //primary: const Color(0xFF2E7D32),
          //secondary: const Color(0xFF86E91A),
          background: Colors.white,
          error: Colors.red,
        ),
        primaryColor: const Color.fromARGB(240, 0, 131, 143),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: const Color(0xFF5148DF),
          ),
        ),
        scaffoldBackgroundColor: Colors.white);
  }
}

// import 'package:flutter/material.dart';
// import 'package:smartsewa/utils/get_swatch_color.dart';

// class MyTheme {
//   // Light theme
//   ThemeData appTheme() {
//     return ThemeData(
//       brightness: Brightness.light,
//       textTheme: const TextTheme(
//         displayLarge: TextStyle(
//           color: Color.fromARGB(240, 0, 131, 143),
//           fontSize: 24,
//           fontWeight: FontWeight.w600,
//         ),
//         displayMedium: TextStyle(
//           color: Colors.black,
//           fontSize: 18,
//           fontWeight: FontWeight.w500,
//         ),
//         titleLarge: TextStyle(
//           fontSize: 16,
//           fontWeight: FontWeight.w500,
//           color: Color(0xFF889AAD),
//         ),
//       ),
//       colorScheme: ColorScheme.fromSeed(
//         seedColor: MaterialColor(
//           0xFF86E91A,
//           getSwatchColor(const Color.fromARGB(240, 0, 131, 143)),
//         ),
//         brightness: Brightness.light,
//         primary: const Color.fromARGB(240, 0, 131, 143),
//         secondary: const Color(0xFF86E91A), // Adjusted secondary color
//         background: Colors.white,
//       ),
//       primaryColor: const Color.fromARGB(240, 0, 131, 143),
//       scaffoldBackgroundColor: Colors.white,
//       cardColor: Colors.white, // Container and card default background
//       elevatedButtonTheme: ElevatedButtonThemeData(
//         style: ElevatedButton.styleFrom(
//           foregroundColor: Colors.white,
//           backgroundColor: const Color(0xFF5148DF),
//         ),
//       ),
//     );
//   }

//   // Dark theme
//   ThemeData darkTheme() {
//     return ThemeData(
//       brightness: Brightness.dark,
//       textTheme: const TextTheme(
//         displayLarge: TextStyle(
//           color: Colors.white,
//           fontSize: 24,
//           fontWeight: FontWeight.w600,
//         ),
//         displayMedium: TextStyle(
//           color: Colors.white70,
//           fontSize: 18,
//           fontWeight: FontWeight.w500,
//         ),
//         titleLarge: TextStyle(
//           fontSize: 16,
//           fontWeight: FontWeight.w500,
//           color: Colors.white70,
//         ),
//       ),
//       colorScheme: ColorScheme.fromSeed(
//         seedColor: MaterialColor(
//           0xFF1A1A1A,
//           getSwatchColor(const Color.fromARGB(255, 33, 33, 33)),
//         ),
//         brightness: Brightness.dark,
//         primary: const Color(0xFF1A1A1A),
//         secondary: const Color(0xFF424242),
//         background: const Color(0xFF121212),
//       ),
//       primaryColor: const Color(0xFF1A1A1A),
//       scaffoldBackgroundColor: const Color(0xFF121212),
//       cardColor: const Color(0xFF1E1E1E), // Dark container and card background
//       elevatedButtonTheme: ElevatedButtonThemeData(
//         style: ElevatedButton.styleFrom(
//           foregroundColor: Colors.white,
//           backgroundColor: const Color(0xFF5148DF),
//         ),
//       ),
//     );
//   }
// }
