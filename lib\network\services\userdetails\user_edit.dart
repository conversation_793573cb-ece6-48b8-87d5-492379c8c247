import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/views/auth/login/login_screen.dart';
import 'package:smartsewa/views/user_screen/main_screen.dart';
import 'package:smartsewa/views/widgets/custom_toasts.dart';

import '../../base_client.dart';
import '../authServices/auth_controller.dart';
import 'current_user_controller.dart';

class UserEditController extends GetxController {
  var isLoading = false.obs;
  final storeController = Get.put(CurrentUserController());
  final controller = Get.put(AuthController());
  String baseUrl = BaseClient().baseUrl;

  Future<void> userProfileEdit(String fullName, String address,
      {bool? isFromServiceScreen = false}) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");
    int? id = prefs.getInt("id");

    try {
      final response = await http
          .put(
            Uri.parse('$baseUrl/api/users/$id'),
            headers: <String, String>{
              'Content-Type': 'application/json',
              'Authorization': "Bearer $apptoken"
            },
            body: jsonEncode(<String, dynamic>{
              "address": address,
              "fullName": fullName,
            }),
          )
          .timeout(const Duration(seconds: 20));

      Get.back();

      if (response.statusCode == 200) {
        storeController.getCurrentUser();
        successToast(msg: 'Updated Successfully');

        isFromServiceScreen ?? false
            ? Get.back()
            : Get.offAll(() => const MainScreen());
      } else {
        errorToast(msg: 'Error: ${response.body}');
      }
    } on HttpException catch (err) {
      errorToast(msg: 'HTTP Error: ${err.message}');
      isLoading.value = false;

      Get.back();
    } on SocketException catch (err) {
      errorToast(msg: 'Network Error: ${err.message}');
      isLoading.value = false;

      Get.back();
    } catch (err) {
      consolelog(err);
      Get.back();
      errorToast(msg: 'Unexpected error: ${err.toString()}');
      isLoading.value = false;
    } finally {}
  }

/*
  Future userProfileEdit(String fullName, String address,
      {bool? isFromServiceScreen = false}) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");
    int? id = prefs.getInt("id");
    try {
      final response = await http
          .put(
            Uri.parse('$baseUrl/api/users/$id'),
            headers: <String, String>{
              'Content-Type': 'application/json',
              'Authorization': "Bearer $apptoken"
            },
            body: jsonEncode(<String, dynamic>{
              "address": address,
              "fullName": fullName,
            }),
          )
          .timeout(const Duration(seconds: 20));

      Get.back();

      if (response.statusCode == 200) {
        storeController.getCurrentUser();
        successToast(msg: ' Updated Successfully');

        isFromServiceScreen ?? false
            ? Get.back()
            : Get.offAll(() => const MainScreen());
      } else {
        errorToast(msg: '${(response.body)} ');
      }
    } catch (err) {
      consolelog(err);
      Get.back();
      errorToast(msg: err.toString());
    }
  }
  */

  Future<void> userChangePassword(
    String oldPassword,
    String newPassword,
    String confirmPassword,
  ) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");
    int? id = prefs.getInt("id");

    try {
      final response = await http
          .post(
            Uri.parse('$baseUrl/api/users/$id/change-password'),
            headers: <String, String>{
              'Content-Type': 'application/json',
              'Authorization': "Bearer $apptoken",
            },
            body: jsonEncode(<String, dynamic>{
              "oldPassword": oldPassword,
              "newPassword": newPassword,
              "confirmPassword": confirmPassword,
            }),
          )
          .timeout(const Duration(seconds: 20)); // Handle request timeout

      log("This is change pw status: ${response.statusCode}");

      if (response.statusCode == 200) {
        successToast(msg: "Password changed successfully");
        await prefs.remove('token');
        await prefs.remove('id');
        await prefs.remove('workStatus');
        await prefs.setBool('rememberMe', false);
        Get.offAll(() => const LoginScreen());
      } else {
        errorToast(msg: "Invalid old password");
      }
    } on SocketException catch (err) {
      errorToast(msg: 'Network Error: ${err.message}');
      isLoading.value = false;
      Get.back();
    } on http.ClientException catch (err) {
      errorToast(msg: 'Client Error: ${err.message}');
      isLoading.value = false;
      Get.back();
    } catch (err) {
      consolelog('Unexpected error: $err');
      isLoading.value = false;
      Get.back();

      isLoading.value = false;
    } finally {}
  }

/*
  Future userChangePassword(
    String oldPassword,
    String newPassword,
    String confirmPassword,
  ) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");
    int? id = prefs.getInt("id");
    final response = await http.post(
      Uri.parse('$baseUrl/api/users/$id/change-password'),
      headers: <String, String>{
        'Content-Type': 'application/json',
        'Authorization': "Bearer $apptoken"
      },
      body: jsonEncode(<String, dynamic>{
        "oldPassword": oldPassword,
        "newPassword": newPassword,
        "confirmPassword": confirmPassword
      }),
    );
    log("This is change pw status: ${response.statusCode}");
    if (response.statusCode == 200) {
      successToast(msg: "Password changed successfully");
      SharedPreferences preferences = await SharedPreferences.getInstance();
      log(preferences.getKeys().toList().toString());
      await preferences.remove('token');
      await preferences.remove('id');
      await preferences.remove('workStatus');
      await preferences.setBool('rememberMe', false);
      Get.offAll(() => const LoginScreen());
    } else {
      errorToast(msg: "Invalid old password");
    }
  }

  */
}
