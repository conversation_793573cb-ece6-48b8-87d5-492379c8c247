// To parse this JSON data, do
//
//     final currentUserResponseModel = currentUserResponseModelFromJson(jsonString);
import 'dart:convert';

CurrentUserResponseModel currentUserResponseModelFromJson(String str) =>
    CurrentUserResponseModel.fromJson(json.decode(str));

String currentUserResponseModelToJson(CurrentUserResponseModel data) =>
    json.encode(data.toJson());

class CurrentUserResponseModel {
  String? fullName;
  String? mobileNumber;
  String? address;
  String? email;
  String? panNumber;
  String? companyName;
  dynamic role;
  String? citizenshipNum;
  dynamic issuedDate;
  dynamic imageUrlCitizenshipFront;
  dynamic imageUrlCitizenshipBack;
  String? picture;
  dynamic associationImg;
  dynamic academicImg;
  String? latitude;
  String? longitude;
  String? serviceProvided;
  dynamic serviceUsed;
  bool? workStatus;
  bool? approval;
  bool? onlineStatus;
  DateTime? userCreatedDate;
  dynamic dateOfBirth;
  dynamic review;
  double? averageRating;
  int? noOfRatingReceived;
  List<Role>? roles;
  String? citizenshipBackUrl;
  String? drivingLicenseUrl;
  String? nationalIdUrl;
  String? citizenshipFrontUrl;
  dynamic files;

  dynamic cv;
  String? firstName;
  String? lastName;
  String? issuedDistrict;
  String? gender;
  int? id;
  String? expiryDate;
  bool? expiryCheck;

  DateTime? currentUserResponseModelUserCreatedDate;

  CurrentUserResponseModel({
    this.fullName,
    this.mobileNumber,
    this.address,
    this.email,
    this.panNumber,
    this.companyName,
    this.role,
    this.citizenshipNum,
    this.issuedDate,
    this.imageUrlCitizenshipFront,
    this.imageUrlCitizenshipBack,
    this.picture,
    this.associationImg,
    this.academicImg,
    this.latitude,
    this.longitude,
    this.serviceProvided,
    this.serviceUsed,
    this.workStatus,
    this.approval,
    this.onlineStatus,
    this.userCreatedDate,
    this.dateOfBirth,
    this.review,
    this.averageRating,
    this.noOfRatingReceived,
    this.roles,
    this.files,
    this.citizenshipFrontUrl,
    this.citizenshipBackUrl,
    this.drivingLicenseUrl,
    this.nationalIdUrl,
    this.cv,
    this.firstName,
    this.lastName,
    this.issuedDistrict,
    this.gender,
    this.id,
    this.expiryDate,
    this.expiryCheck,
  });

  factory CurrentUserResponseModel.fromJson(Map<String, dynamic> json) =>
      CurrentUserResponseModel(
        fullName: json['fullName'],
        mobileNumber: json["mobileNumber"],
        address: json["address"],
        email: json["email"],
        panNumber: json["panNumber"],
        companyName: json["companyName"],
        role: json["role"],
        citizenshipNum: json["citizenshipNum"],
        issuedDate: json["issuedDate"],
        imageUrlCitizenshipFront: json["imageUrlCitizenshipFront"],
        imageUrlCitizenshipBack: json["imageUrlCitizenshipBack"],
        picture: json["picture"],
        associationImg: json["associationImg"],
        academicImg: json["academicImg"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        serviceProvided: json["serviceProvided"],
        serviceUsed: json["serviceUsed"],
        workStatus: json["workStatus"],
        approval: json["approval"],
        onlineStatus: json["onlineStatus"],
        userCreatedDate: json["UserCreatedDate"] == null
            ? null
            : DateTime.parse(json["UserCreatedDate"]),
        dateOfBirth: json["dateOfBirth"] == null
            ? null
            : DateTime.parse(json["dateOfBirth"]),
        review: json["review"],
        averageRating: json["average_rating"].toDouble(),
        noOfRatingReceived: json["no_of_rating_received"],
        id: json["id"],
        gender: json["gender"],
        files: json["files"],
        cv: json["cv"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        expiryDate: json['expiryDate'],
        expiryCheck: json['expiryCheck'],
      );

  get userType => null;

  Map<String, dynamic> toJson() => {
        "fullName": fullName,
        "email": email,
        "mobileNumber": mobileNumber,
        "panNumber": panNumber,
        "companyName": companyName,
        "role": role,
        "citizenshipNum": citizenshipNum,
        "issuedDate": issuedDate,
        "imageUrlCitizenshipFront": imageUrlCitizenshipFront,
        "imageUrlCitizenshipBack": imageUrlCitizenshipBack,
        "picture": picture,
        "associationImg": associationImg,
        "academicImg": academicImg,
        "latitude": latitude,
        "longitude": longitude,
        "serviceProvided": serviceProvided,
        "serviceUsed": serviceUsed,
        "workStatus": workStatus,
        "approval": approval,
        "onlineStatus": onlineStatus,
        "UserCreatedDate": userCreatedDate?.toIso8601String(),
        // "dateOfBirth": dateOfBirth?.toIso8601String(),
        "review": review,
        "average_rating": averageRating,
        "no_of_rating_received": noOfRatingReceived,
        "roles": roles == null
            ? []
            : List<dynamic>.from(roles!.map((x) => x.toJson())),
        "files": files,
        "address": address,
        "citizenshipFrontUrl": citizenshipFrontUrl,
        "citizenshipBackUrl": citizenshipBackUrl,
        "drivingLicenseUrl": drivingLicenseUrl,
        "nationalIdUrl": nationalIdUrl,
        "cv": cv,
        "firstName": firstName,
        "lastName": lastName,
        "issuedDistrict": issuedDistrict,
        "gender": gender,
        "id": id,
      };
}

class Role {
  int? id;
  String? name;

  Role({
    this.id,
    this.name,
  });

  factory Role.fromJson(Map<String, dynamic> json) => Role(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}


/*
class JobDetailsResponse {
  Category? category;
  List<Service>? serviceList;

  JobDetailsResponse({
    this.category,
    this.serviceList,
  });

  factory JobDetailsResponse.fromJson(Map<String, dynamic> json) =>
      JobDetailsResponse(
        category: json["category"] != null
            ? Category.fromJson(json["category"])
            : null,
        serviceList: json["serviceList"] != null
            ? List<Service>.from(
                json["serviceList"].map((x) => Service.fromJson(x)))
            : [],
      );

  Map<String, dynamic> toJson() => {
        "category": category?.toJson(),
        "serviceList": List<dynamic>.from(serviceList!.map((x) => x.toJson())),
      };
}

class Category {
  int? categoryId;
  String? categoryTitle;
  String? categoryImg;

  Category({
    this.categoryId,
    this.categoryTitle,
    this.categoryImg,
  });

  factory Category.fromJson(Map<String, dynamic> json) => Category(
        categoryId: json["categoryId"],
        categoryTitle: json["categoryTitle"],
        categoryImg: json["categoryImg"],
      );

  Map<String, dynamic> toJson() => {
        "categoryId": categoryId,
        "categoryTitle": categoryTitle,
        "categoryImg": categoryImg,
      };
}

class Service {
  int? id;
  String? name;
  dynamic category;
  dynamic serviceImageUrl;

  Service({
    this.id,
    this.name,
    this.category,
    this.serviceImageUrl,
  });

  factory Service.fromJson(Map<String, dynamic> json) => Service(
        id: json["id"],
        name: json["name"],
        category: json["category"],
        serviceImageUrl: json["serviceImageUrl"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "category": category,
        "serviceImageUrl": serviceImageUrl,
      };
}


*/