import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/network/services/authServices/auth_controller.dart';
import 'package:smartsewa/network/services/userdetails/user_edit.dart';
import 'package:smartsewa/views/utils.dart';
import 'package:smartsewa/views/widgets/custom_dialogs.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';

import '../../../network/services/userdetails/current_user_controller.dart';
import '../../widgets/buttons/app_buttons.dart';
import '../approval/map_controller.dart';

class EditProfileUser extends StatefulWidget {
  final mapController = Get.put(MapController());
  final bool? isFromServiceScreen;
  EditProfileUser({
    super.key,
    this.isFromServiceScreen = false,
  });

  @override
  State<EditProfileUser> createState() => _EditProfileUserState();
}

// Initialize controllers with the values passed to the widget

class _EditProfileUserState extends State<EditProfileUser> {
  final userController = Get.put(UserEditController());
  final authController = Get.put(AuthController());
  final storeController = Get.put(CurrentUserController());
  File? _profileImage;
  final _authController = Get.put(AuthController());
  late TextEditingController fullNameController = TextEditingController(
      text: storeController.currentUserData.value.fullName);

  late TextEditingController addressController = TextEditingController(
      text: storeController.currentUserData.value.address);
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  List<String> districts = ['Kathmandu', 'Bhaktapur', 'Lalitpur'];
  Map<String, List<String>> municipalities = {
    'Kathmandu': [
      'Budhanilkantha',
      'Chandragiri',
      'Dakshinkali',
      'Gokarneshwar',
      'Kathmandu Metropolitan',
      'Kageshwori Manohara',
      'Kirtipur',
      'Nagarjun',
      'Shankharapur',
      'Tarakeshwar',
      'Tokha'
    ],
    'Bhaktapur': [
      'Anantalingeshwar',
      'Bhaktapur',
      'Changunarayan',
      'MadhyapurThimi',
      'Nagarkot',
      'Suryavinayak'
    ],
    'Lalitpur': [
      'Bagmati',
      'Godawari',
      'Konjyosom',
      'Lalitpur Metropolitan',
      'Mahalaxmi',
      'Mahankal'
    ],
  };
  Map<String, List<String>> wards = {
    'Budhanilkantha': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13'
    ],
    'Chandragiri': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Dakshinkali': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Gokarneshwar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Kathmandu Metropolitan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 17',
      'Ward 18',
      'Ward 19',
      'Ward 20',
      'Ward 21',
      'Ward 22',
      'Ward 23',
      'Ward 24',
      'Ward 25',
      'Ward 26',
      'Ward 27',
      'Ward 28',
      'Ward 29',
      'Ward 30',
      'Ward 31',
      'Ward 32'
    ],
    'Kageshwori Manohara': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Kirtipur': [
      'Ward 1',
      'Ward2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Nagarjun': [
      'Ward 1',
      'Ward 2',
      'War d3',
      'Ward 4',
      'ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Tokha': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],
    'Shankharapur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Tarakeshwar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],
    'Bhaktapur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Anantalingeshwar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'MadhyapurThimi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Suryavinayak': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12'
    ],
    'Nagarkot': ['Ward1', 'Ward2', 'Ward3'],
    'Changunarayan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Lalitpur Metropolitan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17',
      'Ward 18',
      'Ward 19',
      'Ward 20',
      'Ward 21',
      'Ward 22',
      'Ward 23',
      'Ward 24',
      'Ward 25',
      'Ward 26',
      'Ward 27',
      'Ward 28',
      'Ward 29'
    ],
    'Mahalaxmi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Godawari': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14'
    ],
    'Konjyosom': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5'],
    'Bagmati': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12'
    ],
    'Mahankal': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5', 'Ward 6'],
  };

  String? selectedDistrict;
  String? selectedMunicipality;
  String? selectedWard;
  String? editedAddress;
  bool isEditingAddress = false;

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    final primaryColor = const Color.fromRGBO(0, 131, 143, 1);

    return Scaffold(
      appBar: myAppbar(context, true, "Edit Profile"),
      backgroundColor: Colors.white,
      body: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: size.width * 0.04,
          vertical: size.height * 0.02,
        ),
        child: SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.all(size.width * 0.05),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withOpacity(0.1),
                  spreadRadius: 2,
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(vertical: size.height * 0.02),
                    child: Column(
                      children: [
                        Stack(
                          alignment: Alignment.center,
                          children: [
                            LayoutBuilder(
                              builder: (context, constraints) {
                                double avatarRadius =
                                    constraints.maxWidth * 0.45; // 18% of width
                                return CircleAvatar(
                                  radius: avatarRadius.clamp(50,
                                      90), // Ensuring reasonable min/max size
                                  backgroundColor:
                                      const Color.fromARGB(255, 255, 255, 255),
                                  backgroundImage: _profileImage != null
                                      ? FileImage(_profileImage!)
                                      : NetworkImage(
                                          "$baseUrl/api/allimg/image/${storeController.currentUserData.value.picture}",
                                        ) as ImageProvider,
                                );
                              },
                            ),
                            Positioned(
                              bottom: 0,
                              right: 0,
                              child: _profileImage == null
                                  ? _cameraIconButton()
                                  : _saveImageButton(),
                            ),
                          ],
                        ),
                        SizedBox(height: size.height * 0.02),
                        // Display user's name
                        Text(
                          storeController.currentUserData.value.fullName ?? '',
                          style: TextStyle(
                            fontSize: size.width * 0.055,
                            fontWeight: FontWeight.bold,
                            color: primaryColor,
                          ),
                        ),
                        Divider(
                          color: primaryColor.withOpacity(0.5),
                          thickness: 1,
                          height: size.height * 0.04,
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: size.height * 0.03),

                  // Full Name Input Field
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 5,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TextFormField(
                      controller: fullNameController,
                      style: TextStyle(fontSize: size.width * 0.04),
                      keyboardType:
                          TextInputType.text, // Added to show text keyboard
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'[a-zA-Z\s]')), // Added to filter input
                      ],
                      decoration: InputDecoration(
                        labelText: 'Full Name',
                        labelStyle: TextStyle(
                          color: primaryColor,
                          fontSize: size.width * 0.04,
                        ),
                        hintText: 'Enter your full name',
                        hintStyle: TextStyle(
                          fontSize: size.width * 0.035,
                          color: Colors.grey,
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        prefixIcon: Icon(
                          Icons.person_outline,
                          color: primaryColor,
                          size: size.width * 0.06,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(15),
                          borderSide: BorderSide.none,
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(15),
                          borderSide: BorderSide(color: primaryColor, width: 2),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(15),
                          borderSide:
                              BorderSide(color: Colors.red.shade300, width: 1),
                        ),
                        contentPadding: EdgeInsets.symmetric(
                          vertical: size.height * 0.02,
                          horizontal: size.width * 0.04,
                        ),
                      ),
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your full name';
                        }
                        if (!RegExp(r"^[a-zA-Z\s]+$").hasMatch(value)) {
                          return 'Only letters and spaces are allowed';
                        }
                        return null;
                      },
                    ),
                  ),

                  SizedBox(height: size.height * 0.03),

                  // Address Dropdown Field
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 5,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Theme(
                      data: Theme.of(context).copyWith(
                        inputDecorationTheme: InputDecorationTheme(
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(15),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(15),
                            borderSide:
                                BorderSide(color: primaryColor, width: 2),
                          ),
                        ),
                      ),
                      child: buildAddressDropdown(),
                    ),
                  ),

                  SizedBox(height: size.height * 0.04),

                  // Update Button with Gradient
                  Container(
                    width: double.infinity,
                    height: size.height * 0.065,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      gradient: LinearGradient(
                        colors: [
                          primaryColor,
                          primaryColor.withOpacity(0.8),
                        ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: primaryColor.withOpacity(0.3),
                          spreadRadius: 1,
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: AppButton(
                        name: "Update Profile",
                        onPressed: () {
                          // Use current values if fields are empty
                          if (fullNameController.text.isEmpty) {
                            fullNameController.text = storeController
                                .currentUserData.value.fullName
                                .toString();
                          }
                          if (addressController.text.isEmpty) {
                            addressController.text = storeController
                                .currentUserData.value.address
                                .toString();
                          }
                          CustomDialogs.fullLoadingDialog(
                            context: context,
                            data: "Updating Profile...",
                          );
                          userController.userProfileEdit(
                            fullNameController.text,
                            addressController.text,
                          );
                        },
                      ),
                    ),
                  ),

                  SizedBox(height: size.height * 0.02),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _cameraIconButton() {
    return InkWell(
      onTap: () {
        _selectSource();
      },
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 6,
              spreadRadius: 3,
            ),
          ],
        ),
        padding: const EdgeInsets.all(6),
        child: Icon(
          Icons.camera_alt_outlined,
          color: Theme.of(context).primaryColor,
          size: 28,
        ),
      ),
    );
  }

  Widget _saveImageButton() {
    return ElevatedButton(
      onPressed: () {
        CustomDialogs.fullLoadingDialog(
          context: context,
          data: "Image Uploading...",
        );
        _authController.uploadProfile(_profileImage);
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color.fromARGB(255, 210, 230, 230),
        side: const BorderSide(
            color: Color.fromARGB(255, 245, 255, 255), width: 1.5),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      ),
      child: const Text(
          style: TextStyle(color: Color.fromRGBO(0, 131, 143, 1)), 'Save'),
    );
  }

  Widget buildAddressDropdown() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              onTap: () {
                showDistrictMunicipalityWardDialog();
              },
              controller: addressController,
              readOnly: !isEditingAddress,
              style: const TextStyle(fontSize: 16),
              decoration: InputDecoration(
                labelText: 'Address',
                labelStyle: const TextStyle(
                  color: Color.fromRGBO(0, 131, 143, 1),
                  fontWeight: FontWeight.w500,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Colors.grey,
                    width: 0.5,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Color.fromRGBO(0, 131, 143, 1),
                    width: 1.5,
                  ),
                ),
                filled: true,
                fillColor: Colors.white,
                prefixIcon: const Icon(
                  Icons.home_rounded,
                  color: Color.fromRGBO(0, 131, 143, 1),
                  size: 22,
                ),
                suffixIcon: const Icon(
                  Icons.arrow_drop_down,
                  color: Color.fromRGBO(0, 131, 143, 1),
                ),
                hintText: 'Select your address',
                hintStyle: TextStyle(
                  color: Colors.grey.shade400,
                  fontSize: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void showDistrictMunicipalityWardDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 8,
          backgroundColor: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.location_on_rounded,
                      color: Color.fromRGBO(0, 131, 143, 1),
                    ),
                    const SizedBox(width: 10),
                    const Text(
                      'Select District',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color.fromRGBO(0, 131, 143, 1),
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.grey),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                buildDropdown(
                  label: 'District',
                  items: districts,
                  onChanged: (value) {
                    setState(() {
                      selectedDistrict = value;
                      selectedMunicipality = '';
                      selectedWard = '';
                    });
                    Navigator.pop(context);
                    showMunicipalityDialog();
                  },
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        );
      },
    );
  }

  void showMunicipalityDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 8,
          backgroundColor: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.location_city_rounded,
                      color: Color.fromRGBO(0, 131, 143, 1),
                    ),
                    const SizedBox(width: 10),
                    const Text(
                      'Select Municipality',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color.fromRGBO(0, 131, 143, 1),
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.grey),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (selectedDistrict != null)
                  buildDropdown(
                    label: 'Municipality',
                    items: municipalities[selectedDistrict!] ?? [],
                    onChanged: (value) {
                      setState(() {
                        selectedMunicipality = value;
                        selectedWard = '';
                      });
                      Navigator.pop(context);
                      showWardDialog();
                    },
                  ),
                const SizedBox(height: 10),
                Container(
                  width: double.infinity,
                  alignment: Alignment.centerRight,
                  child: TextButton.icon(
                    icon: const Icon(
                      Icons.arrow_back,
                      color: Color.fromRGBO(0, 131, 143, 1),
                      size: 18,
                    ),
                    label: const Text(
                      'Back to District',
                      style: TextStyle(
                        color: Color.fromRGBO(0, 131, 143, 1),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    onPressed: () {
                      Navigator.pop(context);
                      showDistrictMunicipalityWardDialog();
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void showWardDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 8,
          backgroundColor: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.maps_home_work_rounded,
                      color: Color.fromRGBO(0, 131, 143, 1),
                    ),
                    const SizedBox(width: 10),
                    const Text(
                      'Select Ward',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color.fromRGBO(0, 131, 143, 1),
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.grey),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (selectedMunicipality != null)
                  buildDropdown(
                    label: 'Ward',
                    items: wards[selectedMunicipality!] ?? [],
                    onChanged: (value) {
                      setState(() {
                        selectedWard = value;
                        addressController.text =
                            '$selectedDistrict, $selectedMunicipality, $selectedWard';
                      });
                      Navigator.pop(context);
                    },
                  ),
                const SizedBox(height: 10),
                Container(
                  width: double.infinity,
                  alignment: Alignment.centerRight,
                  child: TextButton.icon(
                    icon: const Icon(
                      Icons.arrow_back,
                      color: Color.fromRGBO(0, 131, 143, 1),
                      size: 18,
                    ),
                    label: const Text(
                      'Back to Municipality',
                      style: TextStyle(
                        color: Color.fromRGBO(0, 131, 143, 1),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    onPressed: () {
                      Navigator.pop(context);
                      showMunicipalityDialog();
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget buildDropdown({
    required String label,
    required List<String> items,
    required Function(String) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: DropdownButtonFormField(
        decoration: InputDecoration(
          labelText: label,
          labelStyle: const TextStyle(
            color: Color.fromRGBO(0, 131, 143, 1),
            fontWeight: FontWeight.w500,
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          border: InputBorder.none,
          filled: true,
          fillColor: Colors.white,
        ),
        dropdownColor: Colors.white,
        borderRadius: BorderRadius.circular(12),
        icon: const Icon(
          Icons.keyboard_arrow_down_rounded,
          color: Color.fromRGBO(0, 131, 143, 1),
        ),
        style: const TextStyle(
          color: Colors.black87,
          fontSize: 16,
        ),
        value: selectedWard != null && items.contains(selectedWard)
            ? selectedWard
            : items.isNotEmpty
                ? items.first
                : null,
        items: items
            .map((item) => DropdownMenuItem(
                  value: item,
                  child: Text(
                    item,
                    style: const TextStyle(
                      color: Colors.black87,
                      fontSize: 16,
                    ),
                  ),
                ))
            .toList(),
        onChanged: (value) {
          onChanged(value.toString());
        },
        isExpanded: true,
      ),
    );
  }

  ///Upload profile image
  File? _profileimage;

  Future pickprofile() async {
    final imagePicker = ImagePicker();
    final pickedImage =
        await imagePicker.pickImage(source: ImageSource.gallery);

    if (pickedImage != null) {
      setState(() {
        _profileimage = File(pickedImage.path);
        print('picked image: $_profileimage');
      });
    } else {
      print('no profile picture selected');
    }
  }

  Future pickProfileImage(ImageSource source) async {
    final imagePicker = ImagePicker();
    final pickedImage = await imagePicker.pickImage(source: source);

    if (pickedImage != null) {
      setState(() {
        _profileImage = File(pickedImage.path);
      });
    }
  }

  void _selectSource() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Center(child: Text("Select")),
          content: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color.fromRGBO(
                      0, 131, 143, 1), // Set the background color
                ),
                onPressed: () {
                  pickProfileImage(ImageSource.gallery);
                  Navigator.pop(context);
                },
                child: const Text("Gallery"),
              ),
              const SizedBox(width: 10),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color.fromRGBO(
                      0, 131, 143, 1), // Set the background color
                ),
                onPressed: () {
                  pickProfileImage(ImageSource.camera);
                  Navigator.pop(context);
                },
                child: const Text("Camera"),
              ),
            ],
          ),
        );
      },
    );
  }
}

// import 'dart:async';
// import 'dart:io';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smartsewa/core/development/console.dart';
// import 'package:smartsewa/network/services/authServices/auth_controller.dart';
// import 'package:smartsewa/network/services/userdetails/user_edit.dart';
// import 'package:smartsewa/views/widgets/custom_dialogs.dart';
// import 'package:smartsewa/views/widgets/my_appbar.dart';
// import '../../../network/services/userdetails/current_user_controller.dart';
// import '../../widgets/buttons/app_buttons.dart';
// import '../approval/map_controller.dart';

// class EditProfileUser extends StatefulWidget {
//   final MapController mapController = Get.put(MapController());
//   final bool? isFromServiceScreen;

//   EditProfileUser({
//     Key? key,
//     this.isFromServiceScreen = false,
//   }) : super(key: key);

//   @override
//   State<EditProfileUser> createState() => _EditProfileUserState();
// }

// class _EditProfileUserState extends State<EditProfileUser> {
//   final UserEditController userController = Get.put(UserEditController());
//   final AuthController authController = Get.put(AuthController());
//   final CurrentUserController storeController =
//       Get.put(CurrentUserController());

//   late TextEditingController fullNameController;
//   late TextEditingController addressController;
//   final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

//   List<String> districts = ['Kathmandu', 'Bhaktapur', 'Lalitpur'];
//   Map<String, List<String>> municipalities = {
//     'Kathmandu': [
//       'Budhanilkantha',
//       'Chandragiri',
//       'Dakshinkali',
//       'Gokarneshwar',
//       'Kathmandu Metropolitan',
//       'Kageshwori Manohara',
//       'Kirtipur',
//       'Nagarjun',
//       'Shankharapur',
//       'Tarakeshwar',
//       'Tokha'
//     ],
//     'Bhaktapur': [
//       'Anantalingeshwar',
//       'Bhaktapur',
//       'Changunarayan',
//       'MadhyapurThimi',
//       'Nagarkot',
//       'Suryavinayak'
//     ],
//     'Lalitpur': [
//       'Bagmati',
//       'Godawari',
//       'Konjyosom',
//       'Lalitpur Metropolitan',
//       'Mahalaxmi',
//       'Mahankal'
//     ],
//   };

//   Map<String, List<String>> wards = {
//     'Budhanilkantha': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11',
//       'Ward 12',
//       'Ward 13'
//     ],
//     'Chandragiri': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11',
//       'Ward 12',
//       'Ward 13',
//       'Ward 14',
//       'Ward 15'
//     ],
//     'Dakshinkali': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9'
//     ],
//     'Gokarneshwar': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9'
//     ],
//     'Kathmandu Metropolitan': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11',
//       'Ward 12',
//       'Ward 13',
//       'Ward 14',
//       'Ward 15',
//       'Ward 17',
//       'Ward 18',
//       'Ward 19',
//       'Ward 20',
//       'Ward 21',
//       'Ward 22',
//       'Ward 23',
//       'Ward 24',
//       'Ward 25',
//       'Ward 26',
//       'Ward 27',
//       'Ward 28',
//       'Ward 29',
//       'Ward 30',
//       'Ward 31',
//       'Ward 32'
//     ],
//     'Kageshwori Manohara': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9'
//     ],
//     'Kirtipur': [
//       'Ward 1',
//       'Ward2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10'
//     ],
//     'Nagarjun': [
//       'Ward 1',
//       'Ward 2',
//       'War d3',
//       'Ward 4',
//       'ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10'
//     ],
//     'Tokha': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11'
//     ],
//     'Shankharapur': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9'
//     ],
//     'Tarakeshwar': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11'
//     ],
//     'Bhaktapur': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10'
//     ],
//     'Anantalingeshwar': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11',
//       'Ward 12',
//       'Ward 13',
//       'Ward 14',
//       'Ward 15'
//     ],
//     'MadhyapurThimi': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9'
//     ],
//     'Suryavinayak': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11',
//       'Ward 12'
//     ],
//     'Nagarkot': ['Ward1', 'Ward2', 'Ward3'],
//     'Changunarayan': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9'
//     ],
//     'Lalitpur Metropolitan': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11',
//       'Ward 12',
//       'Ward 13',
//       'Ward 14',
//       'Ward 15',
//       'Ward 16',
//       'Ward 17',
//       'Ward 18',
//       'Ward 19',
//       'Ward 20',
//       'Ward 21',
//       'Ward 22',
//       'Ward 23',
//       'Ward 24',
//       'Ward 25',
//       'Ward 26',
//       'Ward 27',
//       'Ward 28',
//       'Ward 29'
//     ],
//     'Mahalaxmi': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10'
//     ],
//     'Godawari': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11',
//       'Ward 12',
//       'Ward 13',
//       'Ward 14'
//     ],
//     'Konjyosom': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5'],
//     'Bagmati': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11',
//       'Ward 12'
//     ],
//     'Mahankal': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5', 'Ward 6'],

//     // Wards data goes here (as previously defined)
//     // Ensure to keep the data structured
//   };

//   String? selectedDistrict;
//   String? selectedMunicipality;
//   String? selectedWard;

//   File? _profileImage;

// //   String? editedAddress;
//   bool isEditingAddress = false;

//   @override
//   void initState() {
//     super.initState();
//     fullNameController = TextEditingController(
//         text: storeController.currentUserData.value.fullName);
//     addressController = TextEditingController(
//         text: storeController.currentUserData.value.address);
//   }

//   Future<void> pickProfileImage() async {
//     final imagePicker = ImagePicker();
//     final pickedImage =
//         await imagePicker.pickImage(source: ImageSource.gallery);
//     if (pickedImage != null) {
//       setState(() {
//         _profileImage = File(pickedImage.path);
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     Size size = MediaQuery.of(context).size;

//     return Scaffold(
//       appBar: myAppbar(context, true, "Edit Profile"),
//       body: SingleChildScrollView(
//         child: Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: [
//               buildProfileImage(),
//               SizedBox(height: size.height * 0.03),
//               buildProfileForm(size),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget buildProfileImage() {
//     return Stack(
//       children: [
//         CircleAvatar(
//           radius: 80,
//           backgroundColor: Colors.grey[300],
//           backgroundImage: _profileImage != null
//               ? FileImage(_profileImage!)
//               : AssetImage('assets/Logo.png') as ImageProvider,
//         ),
//         Positioned(
//           bottom: 0,
//           right: 0,
//           child: InkWell(
//             onTap: pickProfileImage,
//             child: Container(
//               padding: EdgeInsets.all(10),
//               decoration: BoxDecoration(
//                 color: Color.fromRGBO(0, 131, 143, 1),
//                 shape: BoxShape.circle,
//                 border: Border.all(color: Colors.white, width: 2),
//               ),
//               child: Icon(Icons.edit, color: Colors.white, size: 24),
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   Widget buildProfileForm(Size size) {
//     return Card(
//       color: Color.fromRGBO(243, 245, 245, 1),
//       elevation: 4,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(12),
//       ),
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Form(
//           key: _formKey,
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.stretch,
//             children: [
//               TextFormField(
//                 controller: fullNameController,
//                 decoration: InputDecoration(
//                   labelText: 'Full Name',
//                   labelStyle: TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
//                   border: OutlineInputBorder(
//                     borderRadius: BorderRadius.circular(10),
//                   ),
//                   prefixIcon:
//                       Icon(Icons.person, color: Color.fromRGBO(0, 131, 143, 1)),
//                 ),
//                 validator: (value) {
//                   if (value == null || value.isEmpty) {
//                     return 'Please enter your full name';
//                   }
//                   return null;
//                 },
//               ),
//               SizedBox(height: 20),
//               buildAddressDropdown(),
//               SizedBox(height: 20),
//               AppButton(
//                 name: "Update",
//                 onPressed: () {
//                   if (_formKey.currentState!.validate()) {
//                     CustomDialogs.fullLoadingDialog(
//                       context: context,
//                       data: "Updating Profile...",
//                     );
//                     userController.userProfileEdit(
//                       fullNameController.text,
//                       addressController.text,
//                     );
//                   }
//                 },
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget buildAddressDropdown() {
//     return Row(
//       children: [
//         Expanded(
//           child: TextField(
//             onTap: () {
//               showDistrictMunicipalityWardDialog();
//             },
//             controller: addressController,
//             readOnly: !isEditingAddress,
//             decoration: const InputDecoration(
//               labelText: 'Address',
//               labelStyle: TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
//               focusedBorder: OutlineInputBorder(
//                 borderSide: BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
//               ),
//               prefixIcon: Icon(
//                 Icons.home,
//                 color: Color.fromRGBO(0, 131, 143, 1),
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   // Widget buildAddressDropdown() {
//   //   return TextField(
//   //     controller: addressController,
//   //     decoration: InputDecoration(
//   //       labelText: 'Address',
//   //       labelStyle: TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
//   //       border: OutlineInputBorder(
//   //         borderRadius: BorderRadius.circular(10),
//   //       ),
//   //       prefixIcon: Icon(Icons.home, color: Color.fromRGBO(0, 131, 143, 1)),
//   //     ),
//   //     readOnly: true,
//   //     onTap: showDistrictMunicipalityWardDialog,
//   //   );
//   // }

//   void showDistrictMunicipalityWardDialog() {
//     Get.bottomSheet(
//       Container(
//         padding: const EdgeInsets.all(0.0),
//         height: MediaQuery.of(context).size.height * 0.32,
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
//           color: const Color.fromARGB(240, 0, 131, 143),
//         ),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text(
//               'Select District, Municipality & Ward',
//               style: TextStyle(
//                 fontSize: 18,
//                 fontWeight: FontWeight.bold,
//                 color: Colors.white,
//               ),
//             ),
//             SizedBox(height: 10),
//             buildDistrictDropdown(),
//             SizedBox(height: 10),
//             buildMunicipalityDropdown(),
//             SizedBox(height: 10),
//             buildWardDropdown(),
//             SizedBox(height: 10),
//             Center(
//               child: ElevatedButton(
//                 onPressed: () {
//                   Get.back(); // Close the dialog
//                 },
//                 style: ElevatedButton.styleFrom(
//                   foregroundColor: const Color.fromARGB(240, 0, 131, 143),
//                   backgroundColor: Colors.white,
//                 ),
//                 child: Text('Okay'),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget buildDistrictDropdown() {
//     return DropdownButton<String>(
//       value: selectedDistrict,
//       isExpanded: true,
//       hint: Text('Select District', style: TextStyle(color: Colors.white)),
//       dropdownColor: const Color.fromARGB(240, 0, 131, 143),
//       items: districts.map((String district) {
//         return DropdownMenuItem<String>(
//           value: district,
//           child: Text(district, style: TextStyle(color: Colors.white)),
//         );
//       }).toList(),
//       onChanged: (String? value) {
//         setState(() {
//           selectedDistrict = value;
//           selectedMunicipality = null;
//           selectedWard = null;
//         });
//       },
//     );
//   }

//   Widget buildMunicipalityDropdown() {
//     return DropdownButton<String>(
//       value: selectedMunicipality,
//       isExpanded: true,
//       hint: Text('Select Municipality', style: TextStyle(color: Colors.white)),
//       dropdownColor: const Color.fromARGB(240, 0, 131, 143),
//       items: selectedDistrict != null
//           ? municipalities[selectedDistrict]!.map((String municipality) {
//               return DropdownMenuItem<String>(
//                 value: municipality,
//                 child:
//                     Text(municipality, style: TextStyle(color: Colors.white)),
//               );
//             }).toList()
//           : [],
//       onChanged: (String? value) {
//         setState(() {
//           selectedMunicipality = value;
//           selectedWard = null; // Reset ward when municipality changes
//         });
//       },
//     );
//   }

//   Widget buildWardDropdown() {
//     return DropdownButton<String>(
//       value: selectedWard,
//       isExpanded: true,
//       hint: Text('Select Ward', style: TextStyle(color: Colors.white)),
//       dropdownColor: const Color.fromARGB(240, 0, 131, 143),
//       items: selectedMunicipality != null
//           ? wards[selectedMunicipality]!.map((String ward) {
//               return DropdownMenuItem<String>(
//                 value: ward,
//                 child: Text(ward, style: TextStyle(color: Colors.white)),
//               );
//             }).toList()
//           : [],
//       onChanged: (String? value) {
//         setState(() {
//           selectedWard = value;
//         });
//       },
//     );
//   }

//   @override
//   void dispose() {
//     fullNameController.dispose();
//     addressController.dispose();
//     super.dispose();
//   }
// }
