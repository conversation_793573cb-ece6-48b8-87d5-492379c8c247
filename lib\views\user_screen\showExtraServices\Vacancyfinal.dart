import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:smartsewa/network/services/authServices/auth_controller.dart';
import 'package:smartsewa/views/auth/registration/user_registration.dart';
import 'package:smartsewa/views/user_screen/showExtraServices/createvacancypage.dart';
import 'package:smartsewa/views/user_screen/showExtraServices/jobdetailpage.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primaryColor: const Color.fromARGB(240, 0, 131, 143),
        colorScheme: ColorScheme.fromSwatch().copyWith(
          primary: const Color.fromARGB(240, 0, 131, 143),
          secondary: const Color.fromARGB(240, 0, 131, 143),
        ),
      ),
      home: JobVacancyPage(),
    );
  }
}

class JobVacancyPage extends StatefulWidget {
  const JobVacancyPage({Key? key}) : super(key: key);

  @override
  State<JobVacancyPage> createState() => _JobVacancyPageState();
}

class _JobVacancyPageState extends State<JobVacancyPage> {
  TextEditingController _searchController = TextEditingController();
  int _currentBannerIndex = 0;
  final authController = Get.find<AuthController>();
  final List<Map<String, String>> banners = [
    {
      'title': 'Featured Jobs',
      'subtitle': 'Find your dream job today',
      'image': 'assets/banner1.jpg'
    },
    {
      'title': 'Premium Listings',
      'subtitle': 'Top companies are hiring',
      'image': 'assets/banner2.jpg'
    },
    {
      'title': 'Urgent Openings',
      'subtitle': 'Apply now for instant consideration',
      'image': 'assets/banner3.jpg'
    },
  ];

  // Sample job list
  final List<Map<String, String>> jobList = [
    {
      'company': 'Dairaz',
      'title': 'Software Engineer',
      'location': 'Kathmandu'
    },
    {
      'company': 'Siddharta Bank',
      'title': 'Product Designer',
      'location': 'Kathmandu'
    },
    {'company': 'TechStar', 'title': 'UI/UX Designer', 'location': 'Lalitpur'},
    // Add more jobs here
  ];

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width >= 600;
    final isDesktop = screenSize.width >= 1024;
    final themeColor = Theme.of(context).primaryColor;

    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: myAppbar(context, true, "Find Your Perfect Job"),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          if (authController.isUserGuest()) {
            final double screenWidth = MediaQuery.of(context).size.width;
            final bool isSmallScreen = screenWidth < 360;

            showDialog(
              context: context,
              builder: (BuildContext context) {
                return Dialog(
                  backgroundColor: Colors.transparent,
                  insetPadding: EdgeInsets.all(screenWidth * 0.05),
                  child: Container(
                    decoration: BoxDecoration(
                        color: const Color.fromARGB(240, 0, 131, 143),
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.2),
                            spreadRadius: 1,
                            blurRadius: 6,
                            offset: const Offset(0, 3),
                          ),
                        ]),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: EdgeInsets.all(screenWidth * 0.04),
                          decoration: const BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(25),
                                  topRight: Radius.circular(25)),
                              boxShadow: [
                                BoxShadow(
                                  color: Color.fromRGBO(0, 0, 0, 0.1),
                                  blurRadius: 8,
                                ),
                              ]),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.verified_user_rounded,
                                color: const Color.fromARGB(240, 0, 131, 143),
                                size: screenWidth * 0.08,
                              ),
                              SizedBox(width: screenWidth * 0.03),
                              Flexible(
                                child: Text(
                                  'Access Required',
                                  style: TextStyle(
                                    color:
                                        const Color.fromARGB(240, 0, 131, 143),
                                    fontSize: isSmallScreen ? 18 : 22,
                                    fontWeight: FontWeight.w700,
                                    letterSpacing: 0.8,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.all(screenWidth * 0.05),
                          child: Column(
                            children: [
                              SizedBox(height: screenWidth * 0.03),
                              Text(
                                'Please SignUp to get access on all features.',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: isSmallScreen ? 14 : 16,
                                  height: 1.4,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: screenWidth * 0.06),
                              // Buttons Row
                              LayoutBuilder(
                                builder: (context, constraints) {
                                  return Row(
                                    children: [
                                      Expanded(
                                        child: ElevatedButton(
                                          onPressed: () =>
                                              Navigator.pop(context),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.transparent,
                                            elevation: 0,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(15),
                                              side: const BorderSide(
                                                color: Colors.white,
                                                width: 1.5,
                                              ),
                                            ),
                                            padding: EdgeInsets.symmetric(
                                              vertical: screenWidth * 0.03,
                                            ),
                                          ),
                                          child: Text(
                                            'Later',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: isSmallScreen ? 14 : 16,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: screenWidth * 0.04),
                                      Expanded(
                                        child: ElevatedButton(
                                          onPressed: () {
                                            Navigator.pop(context);
                                            Get.to(() => UserRegistration());
                                          },
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.white,
                                            elevation: 4,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(15),
                                            ),
                                            padding: EdgeInsets.symmetric(
                                              vertical: screenWidth * 0.03,
                                            ),
                                          ),
                                          child: Text(
                                            'Sign Up Now',
                                            style: TextStyle(
                                              color: const Color.fromARGB(
                                                  240, 0, 131, 143),
                                              fontSize: isSmallScreen ? 14 : 16,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          } else {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    const CreateVacancyPage(), // Replace with the actual screen
              ),
            );
          }
        },
        backgroundColor: themeColor,
        icon: const Icon(
          Icons.add_circle_outline,
          color: Colors.white,
        ),
        label: const Text(
          'Create Vacancy',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16, // White text color
          ),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Search Bar Section
            Padding(
              padding: EdgeInsets.all(screenSize.width * 0.05),
              child: TextField(
                controller: _searchController,
                onChanged: (value) {
                  setState(() {});
                },
                decoration: InputDecoration(
                  labelText: 'Search Jobs',
                  prefixIcon: Icon(Icons.search, color: themeColor),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: themeColor),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
              ),
            ),

            Expanded(
              child: CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        vertical: 16,
                        horizontal: screenSize.width * 0.05,
                      ),
                      child: Wrap(
                        spacing: screenSize.width * 0.05,
                        runSpacing: 16,
                        alignment: WrapAlignment.spaceAround,
                        children: [
                          _buildCategoryIcon(
                            Icons.work,
                            'All Jobs',
                            isTablet,
                            themeColor,
                          ),
                          _buildCategoryIcon(
                            Icons.star,
                            'Featured',
                            isTablet,
                            themeColor,
                          ),
                          _buildCategoryIcon(
                            Icons.trending_up,
                            'Popular',
                            isTablet,
                            themeColor,
                          ),
                          _buildCategoryIcon(
                            Icons.history,
                            'Recent',
                            isTablet,
                            themeColor,
                          ),
                          _buildCategoryIcon(
                            Icons.bookmark,
                            'Saved',
                            isTablet,
                            themeColor,
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Carousel Banner Section
                  SliverToBoxAdapter(
                    child: Column(
                      children: [
                        CarouselSlider(
                          options: CarouselOptions(
                            height: isDesktop ? 200 : (isTablet ? 160 : 120),
                            viewportFraction: 0.9,
                            enlargeCenterPage: true,
                            autoPlay: true,
                            autoPlayInterval: const Duration(seconds: 3),
                            autoPlayAnimationDuration:
                                const Duration(milliseconds: 800),
                            autoPlayCurve: Curves.fastOutSlowIn,
                            onPageChanged: (index, reason) {
                              setState(() {
                                _currentBannerIndex = index;
                              });
                            },
                          ),
                          items: banners.map((banner) {
                            return Container(
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 5.0),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                gradient: LinearGradient(
                                  colors: [
                                    themeColor,
                                    themeColor.withOpacity(0.8),
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                              ),
                              child: Stack(
                                children: [
                                  // Optional: Add banner image here
                                  Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          banner['title']!,
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: isDesktop
                                                ? 32
                                                : (isTablet ? 28 : 24),
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          banner['subtitle']!,
                                          style: TextStyle(
                                            color:
                                                Colors.white.withOpacity(0.9),
                                            fontSize: isDesktop
                                                ? 18
                                                : (isTablet ? 16 : 14),
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                        ),
                        const SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: banners.asMap().entries.map((entry) {
                            return Container(
                              width: 8.0,
                              height: 8.0,
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 4.0),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: themeColor.withOpacity(
                                  _currentBannerIndex == entry.key ? 0.9 : 0.4,
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ],
                    ),
                  ),
                  SliverPadding(
                    padding: EdgeInsets.all(screenSize.width * 0.05),
                    sliver: SliverGrid(
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: isDesktop ? 3 : (isTablet ? 2 : 1),
                        mainAxisSpacing: 16,
                        crossAxisSpacing: 16,
                        mainAxisExtent: isTablet ? 180 : 150,
                      ),
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          // Filter job list based on search query
                          String query = _searchController.text.toLowerCase();
                          List<Map<String, String>> filteredJobs = jobList
                              .where((job) =>
                                  job['title']!.toLowerCase().contains(query) ||
                                  job['company']!
                                      .toLowerCase()
                                      .contains(query) ||
                                  job['location']!
                                      .toLowerCase()
                                      .contains(query))
                              .toList();

                          return _buildJobCard(
                            companyLogo:
                                filteredJobs[index % filteredJobs.length]
                                    ['company']!,
                            companyName:
                                filteredJobs[index % filteredJobs.length]
                                    ['company']!,
                            jobTitle: filteredJobs[index % filteredJobs.length]
                                ['title']!,
                            location: filteredJobs[index % filteredJobs.length]
                                ['location']!,
                            experience: '1 Year',
                            salary: 'Negotiable',
                            isTablet: isTablet,
                            themeColor: themeColor,
                            context: context,
                          );
                        },
                        childCount:
                            jobList.length, // Adjust for filtered job list
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryIcon(
      IconData icon, String label, bool isTablet, Color themeColor) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.all(isTablet ? 16 : 12),
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: themeColor.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 4,
              ),
            ],
          ),
          child: Icon(
            icon,
            color: themeColor,
            size: isTablet ? 28 : 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(fontSize: isTablet ? 14 : 12),
        ),
      ],
    );
  }

  Widget _buildJobCard({
    required String companyLogo,
    required String companyName,
    required String jobTitle,
    required String location,
    required String experience,
    required String salary,
    required bool isTablet,
    required Color themeColor,
    required BuildContext context,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(24),
          onTap: () {
            if (authController.isUserGuest()) {
              final double screenWidth = MediaQuery.of(context).size.width;
              final bool isSmallScreen = screenWidth < 360;

              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return Dialog(
                    backgroundColor: Colors.transparent,
                    insetPadding: EdgeInsets.all(screenWidth * 0.05),
                    child: Container(
                      decoration: BoxDecoration(
                          color: const Color.fromARGB(240, 0, 131, 143),
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.2),
                              spreadRadius: 1,
                              blurRadius: 6,
                              offset: const Offset(0, 3),
                            ),
                          ]),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            padding: EdgeInsets.all(screenWidth * 0.04),
                            decoration: const BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(25),
                                    topRight: Radius.circular(25)),
                                boxShadow: [
                                  BoxShadow(
                                    color: Color.fromRGBO(0, 0, 0, 0.1),
                                    blurRadius: 8,
                                  ),
                                ]),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.verified_user_rounded,
                                  color: const Color.fromARGB(240, 0, 131, 143),
                                  size: screenWidth * 0.08,
                                ),
                                SizedBox(width: screenWidth * 0.03),
                                Flexible(
                                  child: Text(
                                    'Access Required',
                                    style: TextStyle(
                                      color: const Color.fromARGB(
                                          240, 0, 131, 143),
                                      fontSize: isSmallScreen ? 18 : 22,
                                      fontWeight: FontWeight.w700,
                                      letterSpacing: 0.8,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.all(screenWidth * 0.05),
                            child: Column(
                              children: [
                                SizedBox(height: screenWidth * 0.03),
                                Text(
                                  'Please SignUp to get access on all features.',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: isSmallScreen ? 14 : 16,
                                    height: 1.4,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                SizedBox(height: screenWidth * 0.06),
                                // Buttons Row
                                LayoutBuilder(
                                  builder: (context, constraints) {
                                    return Row(
                                      children: [
                                        Expanded(
                                          child: ElevatedButton(
                                            onPressed: () =>
                                                Navigator.pop(context),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  Colors.transparent,
                                              elevation: 0,
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(15),
                                                side: const BorderSide(
                                                  color: Colors.white,
                                                  width: 1.5,
                                                ),
                                              ),
                                              padding: EdgeInsets.symmetric(
                                                vertical: screenWidth * 0.03,
                                              ),
                                            ),
                                            child: Text(
                                              'Later',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize:
                                                    isSmallScreen ? 14 : 16,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: screenWidth * 0.04),
                                        Expanded(
                                          child: ElevatedButton(
                                            onPressed: () {
                                              Navigator.pop(context);
                                              Get.to(() => UserRegistration());
                                            },
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.white,
                                              elevation: 4,
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(15),
                                              ),
                                              padding: EdgeInsets.symmetric(
                                                vertical: screenWidth * 0.03,
                                              ),
                                            ),
                                            child: Text(
                                              'Sign Up Now',
                                              style: TextStyle(
                                                color: const Color.fromARGB(
                                                    240, 0, 131, 143),
                                                fontSize:
                                                    isSmallScreen ? 14 : 16,
                                                fontWeight: FontWeight.w700,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            } else {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const JobDetailPage()),
              );
            }
          },
          child: Padding(
            padding: EdgeInsets.all(isTablet ? 20 : 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: isTablet ? 60 : 50,
                      height: isTablet ? 60 : 50,
                      decoration: BoxDecoration(
                        color: themeColor.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: Text(
                          companyLogo,
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: isTablet ? 18 : 16,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            companyName,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: isTablet ? 18 : 16,
                            ),
                          ),
                          Text(
                            jobTitle,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: isTablet ? 15 : 13,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // Location, Experience, and Salary Row
                Row(
                  children: [
                    Expanded(
                      child: Row(
                        children: [
                          Icon(Icons.location_on, size: 18, color: themeColor),
                          const SizedBox(width: 4),
                          Text(location),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'Experience: $experience',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          'Salary: $salary',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
