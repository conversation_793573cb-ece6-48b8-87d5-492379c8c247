import 'package:flutter/material.dart';

class AppButton1 extends StatelessWidget {
  final String name;
  final IconData? icon;
  final bool? isDisabled;
  final VoidCallback onPressed;

  const AppButton1({
    super.key,
    required this.name,
    this.icon,
    required this.onPressed,
    this.isDisabled,
    required EdgeInsets padding,
  });

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;

    return MaterialButton(
        disabledColor: Colors.grey.shade300,
        color: isDisabled ?? false
            ? Colors.grey.shade300
            : const Color.fromARGB(200, 0, 131, 143),
        // : Color.fromARGB(200, 0, 131, 143),

        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(width: 2.5, color: Colors.white)),
        onPressed: isDisabled ?? false
            ? null
            : () {
                onPressed.call();
              },
        child: SizedBox(
          height: size.height * 0.068,
          width: size.height / 2.0,
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                icon != null
                    ? Icon(
                        icon,
                        color: Colors.redAccent,
                      )
                    : Container(),
                Text(
                  name,
                  style: TextStyle(
                    fontSize: isDisabled ?? false ? 15 : 15,
                    fontWeight: FontWeight.w600,
                    color: isDisabled ?? false
                        ? const Color.fromARGB(200, 0, 131, 143)
                        : Colors.white,
                    // const Color.fromARGB(200, 0, 131, 143),
                  ),
                ),
                // SizedBox(
                //   width: size.width * 0.01,
                // )
              ],
            ),
          ),
        ));
  }
}
