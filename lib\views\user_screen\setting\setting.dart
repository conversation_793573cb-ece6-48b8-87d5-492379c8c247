import 'dart:convert';
import 'dart:developer';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/views/utils.dart';
import 'package:smartsewa/views/widgets/buttons/app_buttons.dart';
import 'package:smartsewa/views/widgets/buttons/app_buttons1_left.dart';
import 'package:smartsewa/views/widgets/custom_toasts.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../network/services/contact_services/contact_controller.dart';
import '../../auth/login/login_screen.dart';
import '../../widgets/my_appbar.dart';
import '../profile/edit_profile_user.dart';
import 'change_password.dart';

class SettingPage extends StatefulWidget {
  const SettingPage({super.key});

  @override
  State<SettingPage> createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  final ContactsController _controller = Get.put(ContactsController());
  bool switchValue1 = true;
  bool fingerprintEnabled = false;
  final ValueNotifier<bool?> selectedAutoLogin = ValueNotifier(null);

  @override
  void initState() {
    super.initState();
    _loadAutoLoginPreference();
    _checkFingerprintStatus();
  }

  void _loadAutoLoginPreference() async {
    final prefs = await SharedPreferences.getInstance();
    selectedAutoLogin.value = prefs.getBool("rememberMe") ?? false;
    bool? fingerprint = prefs.getBool('fingerprintEnabled');
  }

  Future<void> logout(BuildContext context) async {
    // Show confirmation dialog before proceeding with logout
    bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        double screenWidth = MediaQuery.of(context).size.width;
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15), // Smooth edges
          ),
          title: Center(
            child: Text(
              'Logout Confirmation',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: screenWidth * 0.05, // Responsive font size
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          content: Padding(
            padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.02),
            child: Text(
              'Are you sure you want to logout?',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: screenWidth * 0.04, // Responsive text
                color: Colors.black54,
              ),
            ),
          ),
          actionsAlignment: MainAxisAlignment.spaceEvenly,
          actions: <Widget>[
            SizedBox(
              width: screenWidth * 0.3, // Responsive button width
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[300],
                  foregroundColor: Colors.black,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'No',
                  style: TextStyle(fontSize: screenWidth * 0.04),
                ),
                onPressed: () {
                  Navigator.of(context).pop(false); // Dismiss the dialog
                },
              ),
            ),
            SizedBox(
              width: screenWidth * 0.3, // Responsive button width
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color.fromARGB(240, 0, 131, 143),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Yes',
                  style: TextStyle(fontSize: screenWidth * 0.04),
                ),
                onPressed: () async {
                  Navigator.of(context).pop(true); // Proceed with logout
                  final preferences = await SharedPreferences.getInstance();
                  await preferences.remove('token');
                  await preferences.remove('id');
                  await preferences.remove("isAuthenticated");
                  await preferences.remove('isGuest');
                  Get.offAll(() => const LoginScreen());
                },
              ),
            ),
          ],
        );
      },
    );

    if (confirmed ?? false) {
      // The user confirmed the logout, so proceed with the logout logic here
    }
  }

  Future<bool> showConfirmationDialog() async {
    return await showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Confirmation'),
          content: const Text('Are you sure you want to disable auto-login?'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            TextButton(
              child: const Text('Confirm'),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    );
  }

  void openMail() async {
    String? encodeQueryParameters(Map<String, String> params) {
      return params.entries
          .map((e) =>
              '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
          .join('&');
    }

    final Uri emailLaunchUri = Uri(
      scheme: 'mailto',
      path: _controller.feedbackEmail,
      query: encodeQueryParameters({'subject': 'Feedback !!!'}),
    );

    try {
      await launchUrl(emailLaunchUri);
    } catch (e) {
      log(e.toString());
    }
  }

  void openPlayStore() async {
    const String packageName = 'com.smartsewa.service';
    const String playStoreUrl =
        'https://play.google.com/store/apps/details?id=$packageName';
    const ClipboardData data = ClipboardData(text: playStoreUrl);
    await Clipboard.setData(data);

    try {
      await Share.share(playStoreUrl);
    } catch (e) {
      log(e.toString());
    }
  }

  // Add this to your _SettingPageState class

  Future<void> reportDispute({
    required String refCode,
    required String paymentDuration,
    required String merchant,
    required double amount,
    required int userId,
    required DateTime paymentDate,
  }) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('token');

      if (token == null) {
        errorToast(msg: "Authentication required");
        return;
      }

      var headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      };

      var request =
          http.Request('POST', Uri.parse('$baseUrl/api/disputes/report'));

      request.body = json.encode({
        "refCode": refCode,
        "paymentDuration": paymentDuration,
        "merchant": merchant,
        "amount": amount,
        "userId": userId,
        "paymentDate": paymentDate.toIso8601String()
      });

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        String responseBody = await response.stream.bytesToString();
        successToast(msg: "Dispute reported successfully");
        print('Dispute report response: $responseBody');
      } else {
        errorToast(msg: "Failed to report dispute");
        print('Failed to report dispute: ${response.reasonPhrase}');
      }
    } catch (e) {
      errorToast(msg: "Error reporting dispute: $e");
      print('Error occurred while reporting dispute: $e');
    }
  }

  void _showReportDisputeDialog() {
    final TextEditingController merchantController =
        TextEditingController(text: "Khalti");
    final TextEditingController refCodeController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        final mediaQuery = MediaQuery.of(context);
        final screenWidth = mediaQuery.size.width;
        final screenHeight = mediaQuery.size.height;
        final isSmallScreen = screenWidth < 600; // Mobile phones
        final isMediumScreen =
            screenWidth >= 600 && screenWidth < 900; // Tablets
        final isLargeScreen = screenWidth >= 900; // Desktops
        final isPortrait = mediaQuery.orientation == Orientation.portrait;

        // Responsive values
        final dialogPadding = isSmallScreen
            ? 16.0
            : isMediumScreen
                ? 20.0
                : 24.0;
        final borderRadius = isSmallScreen ? 16.0 : 20.0;
        final dialogWidth = isSmallScreen
            ? screenWidth * (isPortrait ? 0.95 : 0.85)
            : isMediumScreen
                ? screenWidth * (isPortrait ? 0.85 : 0.7)
                : screenWidth * (isPortrait ? 0.75 : 0.6);
        final dialogHeight = isPortrait
            ? screenHeight * (isSmallScreen ? 0.85 : 0.8)
            : screenHeight * 0.9;

        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          elevation: 16,
          insetPadding: EdgeInsets.symmetric(
            horizontal: isSmallScreen ? 8 : 16,
            vertical: isSmallScreen ? 16 : 24,
          ),
          child: Container(
            width: dialogWidth,
            height: dialogHeight,
            constraints: BoxConstraints(
              maxWidth: dialogWidth,
              maxHeight: dialogHeight,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(borderRadius),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.white, Colors.grey.shade50],
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                _buildDisputeHeader(isSmallScreen, isPortrait),

                // Content
                Flexible(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 16 : 24,
                      vertical: isSmallScreen ? 16 : 20,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Disclaimer Section
                        _buildDisputeDisclaimerSection(
                            isSmallScreen, isPortrait),

                        SizedBox(height: isSmallScreen ? 20 : 24),

                        // Merchant Field (First)
                        _buildMerchantDropdown(
                          controller: merchantController,
                          isSmallScreen: isSmallScreen,
                          isPortrait: isPortrait,
                        ),

                        SizedBox(height: isSmallScreen ? 20 : 24),

                        // Reference Code Field (Second)
                        _buildInputField(
                          controller: refCodeController,
                          label: 'Reference Code',
                          hint:
                              'Enter transaction reference from payment receipt',
                          description:
                              'The unique code you received after successful payment completion',
                          icon: Icons.confirmation_number_outlined,
                          isRequired: true,
                          isSmallScreen: isSmallScreen,
                          isPortrait: isPortrait,
                        ),

                        SizedBox(height: isSmallScreen ? 20 : 24),

                        // Important Notice
                        _buildImportantNotice(isSmallScreen, isPortrait),
                      ],
                    ),
                  ),
                ),

                // Action Buttons
                _buildActionButtons(
                  isSmallScreen: isSmallScreen,
                  isPortrait: isPortrait,
                  onCancel: () => Navigator.of(context).pop(),
                  onSubmit: () {
                    if (_validateDisputeForm(
                        merchantController, refCodeController)) {
                      _submitDispute(merchantController, refCodeController);
                      Navigator.of(context).pop();
                    }
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDisputeHeader(bool isSmallScreen, bool isPortrait) {
    return Container(
      padding: EdgeInsets.all(isSmallScreen ? (isPortrait ? 16 : 12) : 24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.red.shade400, Colors.red.shade600],
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(isSmallScreen ? 16 : 20),
          topRight: Radius.circular(isSmallScreen ? 16 : 20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.report_problem_outlined,
              color: Colors.white,
              size: isSmallScreen ? (isPortrait ? 20 : 18) : 24,
            ),
          ),
          SizedBox(width: isSmallScreen ? 12 : 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Report Payment Dispute',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: isSmallScreen ? (isPortrait ? 18 : 16) : 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'Help us resolve your payment issue',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: isSmallScreen ? (isPortrait ? 12 : 11) : 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDisputeDisclaimerSection(bool isSmallScreen, bool isPortrait) {
    return Container(
      padding: EdgeInsets.all(isSmallScreen ? (isPortrait ? 12 : 10) : 16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.blue.shade700,
                size: isSmallScreen ? (isPortrait ? 18 : 16) : 20,
              ),
              SizedBox(width: 8),
              Text(
                'Important Information',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade800,
                  fontSize: isSmallScreen ? (isPortrait ? 14 : 13) : 16,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            '• Merchant: Select the digital wallet used for payment (Khalti, eSewa, ConnectIPS, etc.)\n'
            '• Reference Code: Found in your payment receipt after successful transaction',
            style: TextStyle(
              color: Colors.blue.shade700,
              fontSize: isSmallScreen ? (isPortrait ? 12 : 11) : 13,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMerchantDropdown({
    required TextEditingController controller,
    required bool isSmallScreen,
    required bool isPortrait,
  }) {
    final List<String> merchants = [
      'Khalti',
      'eSewa',
      'ConnectIPS',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Payment Merchant',
              style: TextStyle(
                fontSize: isSmallScreen ? (isPortrait ? 14 : 13) : 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
            Text(
              ' *',
              style: TextStyle(
                fontSize: isSmallScreen ? (isPortrait ? 16 : 15) : 18,
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        SizedBox(height: 6),
        Text(
          'Select the digital wallet or payment method you used',
          style: TextStyle(
            fontSize: isSmallScreen ? (isPortrait ? 11 : 10) : 12,
            color: Colors.grey.shade600,
            fontStyle: FontStyle.italic,
          ),
        ),
        SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: DropdownButtonFormField<String>(
            value: controller.text.isNotEmpty ? controller.text : 'Khalti',
            decoration: InputDecoration(
              prefixIcon: Container(
                margin: EdgeInsets.all(12),
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.account_balance_wallet_outlined,
                  size: isSmallScreen ? (isPortrait ? 18 : 16) : 20,
                  color: Colors.grey.shade600,
                ),
              ),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide(color: Colors.grey.shade200),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide(color: Colors.red.shade300, width: 2),
              ),
              contentPadding: EdgeInsets.symmetric(
                vertical: isSmallScreen ? (isPortrait ? 14 : 12) : 16,
                horizontal: 16,
              ),
            ),
            items: merchants.map((String merchant) {
              return DropdownMenuItem<String>(
                value: merchant,
                child: Text(
                  merchant,
                  style: TextStyle(
                    fontSize: isSmallScreen ? (isPortrait ? 14 : 13) : 16,
                  ),
                ),
              );
            }).toList(),
            onChanged: (String? newValue) {
              if (newValue != null) {
                controller.text = newValue;
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? description,
    required IconData icon,
    TextInputType? keyboardType,
    bool isRequired = false,
    required bool isSmallScreen,
    required bool isPortrait,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: isSmallScreen ? (isPortrait ? 14 : 13) : 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
            if (isRequired)
              Text(
                ' *',
                style: TextStyle(
                  fontSize: isSmallScreen ? (isPortrait ? 16 : 15) : 18,
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
          ],
        ),
        if (description != null) ...[
          SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontSize: isSmallScreen ? (isPortrait ? 11 : 10) : 12,
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
        SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: controller,
            keyboardType: keyboardType,
            style: TextStyle(
              fontSize: isSmallScreen ? (isPortrait ? 14 : 13) : 16,
            ),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(
                color: Colors.grey.shade400,
                fontSize: isSmallScreen ? (isPortrait ? 14 : 13) : 16,
              ),
              prefixIcon: Container(
                margin: EdgeInsets.all(12),
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: isSmallScreen ? (isPortrait ? 18 : 16) : 20,
                  color: Colors.grey.shade600,
                ),
              ),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide(color: Colors.grey.shade200),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide(color: Colors.red.shade300, width: 2),
              ),
              contentPadding: EdgeInsets.symmetric(
                vertical: isSmallScreen ? (isPortrait ? 14 : 12) : 16,
                horizontal: 16,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImportantNotice(bool isSmallScreen, bool isPortrait) {
    return Container(
      padding: EdgeInsets.all(isSmallScreen ? (isPortrait ? 12 : 10) : 16),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning_amber_outlined,
                color: Colors.orange.shade700,
                size: isSmallScreen ? (isPortrait ? 18 : 16) : 20,
              ),
              SizedBox(width: 8),
              Text(
                'Important Notice',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange.shade800,
                  fontSize: isSmallScreen ? (isPortrait ? 14 : 13) : 16,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            'Please ensure all information matches your payment receipt exactly. '
            'False reports may result in account restrictions. Our team will '
            'review your dispute within 24-48 hours.',
            style: TextStyle(
              color: Colors.orange.shade700,
              fontSize: isSmallScreen ? (isPortrait ? 12 : 11) : 13,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons({
    required bool isSmallScreen,
    required bool isPortrait,
    required VoidCallback onCancel,
    required VoidCallback onSubmit,
  }) {
    final buttonPadding = isSmallScreen ? (isPortrait ? 16.0 : 12.0) : 16.0;
    final buttonFontSize = isSmallScreen ? (isPortrait ? 16.0 : 14.0) : 16.0;

    return Container(
      padding: EdgeInsets.all(isSmallScreen ? (isPortrait ? 16 : 12) : 24),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: isSmallScreen && isPortrait
          ? Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: onSubmit,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade400,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(
                        vertical: MediaQuery.of(context).size.width < 350
                            ? 12
                            : MediaQuery.of(context).size.width < 600
                                ? 16
                                : 20,
                      ),
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.send_outlined,
                          size: MediaQuery.of(context).size.width < 350
                              ? 16
                              : MediaQuery.of(context).size.width < 600
                                  ? 18
                                  : 20,
                          color: Colors.white,
                        ),
                        SizedBox(
                          width: MediaQuery.of(context).size.width < 350
                              ? 6
                              : MediaQuery.of(context).size.width < 600
                                  ? 8
                                  : 10,
                        ),
                        Text(
                          'Submit Report',
                          style: TextStyle(
                            fontSize: MediaQuery.of(context).size.width < 350
                                ? 14
                                : MediaQuery.of(context).size.width < 600
                                    ? 16
                                    : 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                    height: MediaQuery.of(context).size.width < 400 ? 8 : 12),
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: onCancel,
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        vertical: MediaQuery.of(context).size.width < 350
                            ? 12
                            : MediaQuery.of(context).size.width < 600
                                ? 16
                                : 20,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      side: BorderSide(color: Colors.grey.shade400),
                    ),
                    child: Text(
                      'Cancel',
                      style: TextStyle(
                        fontSize: MediaQuery.of(context).size.width < 350
                            ? 14
                            : MediaQuery.of(context).size.width < 600
                                ? 16
                                : 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ),
                ),
              ],
            )
          : Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: onCancel,
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: buttonPadding),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      side: BorderSide(color: Colors.grey.shade400),
                    ),
                    child: Text(
                      'Cancel',
                      style: TextStyle(
                        fontSize: buttonFontSize,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: isSmallScreen ? 12 : 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: onSubmit,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade400,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: buttonPadding),
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.send_outlined,
                          size: MediaQuery.of(context).size.width < 350
                              ? 16
                              : MediaQuery.of(context).size.width < 600
                                  ? 18
                                  : 20,
                        ),
                        SizedBox(
                          width: MediaQuery.of(context).size.width < 350
                              ? 6
                              : MediaQuery.of(context).size.width < 600
                                  ? 8
                                  : 10,
                        ),
                        Text(
                          'Submit Report',
                          style: TextStyle(
                            fontSize: MediaQuery.of(context).size.width < 350
                                ? 14
                                : MediaQuery.of(context).size.width < 600
                                    ? 16
                                    : 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  bool _validateDisputeForm(
    TextEditingController merchantController,
    TextEditingController refCodeController,
  ) {
    if (merchantController.text.isEmpty || refCodeController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please fill all required fields'),
          backgroundColor: Colors.red.shade400,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
      return false;
    }
    return true;
  }

  Future<void> _submitDispute(
    TextEditingController merchantController,
    TextEditingController refCodeController,
  ) async {
    // Get the current user ID
    final prefs = await SharedPreferences.getInstance();
    int userId = prefs.getInt('id') ?? 0;

    // Provide dummy/default values for required parameters or collect them from the UI as needed
    await reportDispute(
      merchant: merchantController.text,
      refCode: refCodeController.text,
      userId: userId,
      paymentDuration: "1", // Replace with actual value if available
      amount: 0.0, // Replace with actual value if available
      paymentDate: DateTime.now(), // Replace with actual value if available
    );
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    double screenWidth = size.width;
    double screenHeight = size.height;

    return Scaffold(
      appBar: myAppbar(context, true, "Settings"),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: screenWidth * 0.04, vertical: screenHeight * 0.025),
          child: Column(
            children: [
              SizedBox(height: screenHeight * 0.03),
              // Settings Container
              Container(
                padding: EdgeInsets.symmetric(
                    horizontal: screenWidth * 0.02,
                    vertical: screenHeight * 0.01),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(screenWidth * 0.05),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.5),
                      spreadRadius: screenWidth * 0.02,
                      blurRadius: screenWidth * 0.06,
                      offset: Offset(0, screenHeight * 0.005),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    buildSettingTile(
                      icon: Icons.person,
                      text: 'Edit Profile',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => EditProfileUser(),
                          ),
                        );
                      },
                    ),
                    buildSettingTile(
                      icon: Icons.lock,
                      text: 'Change Password',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ChangePassword(),
                          ),
                        );
                      },
                    ),
                    buildSettingSwitchTile(
                      icon: Icons.notifications,
                      text: 'Notification',
                      value: switchValue1,
                      onChanged: (value) {
                        setState(() {
                          switchValue1 = value;
                        });
                      },
                    ),
                    /* ValueListenableBuilder(
                      valueListenable: selectedAutoLogin,
                      builder: (context, value, child) {
                        return buildSettingSwitchTile(
                          icon: Icons.login_sharp,
                          text: 'Auto Login',
                          value: value ?? false,
                          onChanged: (val) async {
                            selectedAutoLogin.value = val;
                            final prefs = await SharedPreferences.getInstance();
                            await prefs.setBool("rememberMe", val);
                          },
                        );
                      },
                    ),

                    */
                    buildSettingTile(
                      icon: Icons.mail,
                      text: 'Feedback',
                      onTap: openMail,
                    ),
                    buildSettingTile(
                      icon: Icons.share,
                      text: 'Share App',
                      onTap: openPlayStore,
                    ),
                    buildSettingTile(
                      icon: Icons.report_problem,
                      text: 'Report Payment Dispute',
                      onTap: _showReportDisputeDialog,
                    ),
                    buildSettingTile(
                      icon: Icons.delete,
                      text: 'Delete',
                      onTap: () async {
                        TextEditingController passwordController =
                            TextEditingController();

                        // Get the username from SharedPreferences
                        final prefs = await SharedPreferences.getInstance();
                        String username = prefs.getString('username') ?? '';

                        if (username.isEmpty) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Error: Username not found.'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }

                        showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (BuildContext dialogContext) {
                            return AlertDialog(
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(screenWidth * 0.04),
                              ),
                              title: Row(
                                children: [
                                  Icon(
                                    Icons.warning_amber_rounded,
                                    color: Colors.red,
                                    size: screenWidth * 0.06,
                                  ),
                                  SizedBox(width: screenWidth * 0.02),
                                  Expanded(
                                    child: Text(
                                      'Confirm Deletion',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: screenWidth * 0.045,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              content: Container(
                                width: screenWidth * 0.8,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      'Are you sure you want to permanently delete your account? This action cannot be reversed and will result in the loss of all your data and settings.',
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.04 > 16
                                            ? 16
                                            : screenWidth * 0.04,
                                        height: 1.5,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    SizedBox(height: screenHeight * 0.02),
                                    TextField(
                                      controller: passwordController,
                                      obscureText: true,
                                      decoration: InputDecoration(
                                        labelText: 'Enter Password',
                                        labelStyle: TextStyle(
                                          fontSize: screenWidth * 0.035,
                                        ),
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                              screenWidth * 0.02),
                                        ),
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal: screenWidth * 0.04,
                                          vertical: screenHeight * 0.015,
                                        ),
                                      ),
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.04,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              actions: [
                                Padding(
                                  padding: EdgeInsets.only(
                                    bottom: screenHeight * 0.01,
                                    left: screenWidth * 0.02,
                                    right: screenWidth * 0.02,
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: OutlinedButton(
                                          style: OutlinedButton.styleFrom(
                                            padding: EdgeInsets.symmetric(
                                              vertical: screenHeight * 0.015,
                                            ),
                                            side: BorderSide(
                                              color: Theme.of(context)
                                                  .primaryColor,
                                            ),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      screenWidth * 0.02),
                                            ),
                                          ),
                                          onPressed: () =>
                                              Navigator.pop(dialogContext),
                                          child: Text(
                                            'Cancel',
                                            style: TextStyle(
                                              fontSize: screenWidth * 0.04,
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: screenWidth * 0.03),
                                      Expanded(
                                        child: ElevatedButton(
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.red,
                                            foregroundColor: Colors.white,
                                            padding: EdgeInsets.symmetric(
                                              vertical: screenHeight * 0.015,
                                            ),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      screenWidth * 0.02),
                                            ),
                                          ),
                                          onPressed: () {
                                            if (passwordController
                                                .text.isEmpty) {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(
                                                const SnackBar(
                                                  content: Text(
                                                      'Please enter your password'),
                                                  backgroundColor: Colors.red,
                                                ),
                                              );
                                              return;
                                            }
                                            Navigator.pop(dialogContext);
                                            deleteUserAccount(
                                                username,
                                                passwordController.text,
                                                context);
                                          },
                                          child: Text(
                                            'Delete',
                                            style: TextStyle(
                                              fontSize: screenWidth * 0.04,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            );
                          },
                        );
                      },
                    ),
                    SizedBox(height: screenHeight * 0.001),
                    if (fingerprintEnabled)
                      buildSettingTile(
                        icon: Icons.fingerprint_rounded,
                        text: 'Disable FingerPrint Login',
                        onTap: () async {
                          await _disableFingerprint();
                        },
                      ),
                    SizedBox(height: screenHeight * 0.02),
                    SizedBox(
                      width: screenWidth * 0.9,
                      child: ElevatedButton(
                        onPressed: () => logout(context),
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                            vertical: screenHeight * 0.02,
                          ),
                          backgroundColor:
                              const Color(0xFF00838F), // Preferred color
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(screenWidth *
                                0.045), // Responsive border radius
                          ),
                          elevation: 5.0, // Soft shadow
                          textStyle: TextStyle(
                            fontSize:
                                screenWidth * 0.045, // Responsive font size
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        child: Text(
                          'Logout',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize:
                                screenWidth * 0.045, // Responsive font size
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.06),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _checkFingerprintStatus() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    setState(() {
      fingerprintEnabled = preferences.getBool('fingerprintEnabled') ??
          false; // Default to false if not set
    });
  }

  Future<void> _disableFingerprint() async {
    // Show a confirmation dialog
    bool? confirmed = await showDialog<bool>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Confirm'),
            content: const Text(
                'Are you sure you want to disable fingerprint login?'),
            actions: <Widget>[
              TextButton(
                child: const Text('Cancel'),
                onPressed: () {
                  Navigator.of(context).pop(false); // User clicked Cancel
                },
              ),
              TextButton(
                child: const Text('Confirm'),
                onPressed: () async {
                  _disableFinger();
                  Navigator.of(context).pop(true);
                  // User clicked Confirm
                },
              ),
            ],
          );
        });
  }

  Future<void> _disableFinger() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    // Set the fingerprint status to false
    bool success = await preferences.setBool('fingerprintEnabled', false);

    // Log the status of the operation
    log('Fingerprint status updated successfully: $success');
    successToast(msg: "FingerPrint Login Disabled ");

    // Optionally, you can retrieve and log the updated status
    bool? fingerprintStatus = preferences.getBool('fingerprintEnabled');
    log('Current Fingerprint status from SharedPreferences: $fingerprintStatus');
    setState(() {
      fingerprintEnabled =
          false; // Update the local state to reflect the change
    });
  }

  Future<void> deleteUserAccount(
      String username, String password, BuildContext context) async {
    final scaffoldContext = context;
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16.0),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Deleting account...'),
              ],
            ),
          );
        },
      );
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token') ?? '';
      var headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token'
      };
      var request = http.Request(
          'DELETE',
          Uri.parse(
              '$baseUrl/api/users/delete?username=$username&password=$password'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();
      String responseBody = await response.stream.bytesToString();

      // Print for debugging
      print("Response status: ${response.statusCode}");
      print("Response body: $responseBody");

      // Dismiss loading dialog
      Navigator.of(context, rootNavigator: true).pop();

      try {
        var jsonResponse = json.decode(responseBody);

        switch (response.statusCode) {
          case 200:
            // Success case
            showSuccessDialog(scaffoldContext, jsonResponse['message']);
            break;
          case 401:
            // Invalid credentials, show attempts remaining
            showErrorDialog(scaffoldContext, jsonResponse['message']);
            break;
          case 403:
            // Maximum attempts reached
            showErrorDialog(scaffoldContext, jsonResponse['message']);
            break;
          case 404:
            // User not found
            showErrorDialog(scaffoldContext, jsonResponse['message']);
            break;
          case 500:
          default:
            // Internal server error or other unexpected errors
            showErrorDialog(scaffoldContext,
                jsonResponse['message'] ?? 'An unexpected error occurred');
            break;
        }
      } catch (parseError) {
        print("JSON parsing error: $parseError");
        showErrorDialog(scaffoldContext, 'Error processing server response');
      }
    } catch (e) {
      print("Exception occurred: $e");
      // Make sure to dismiss loading dialog if still showing
      Navigator.of(context, rootNavigator: true).pop();
      showErrorDialog(
          scaffoldContext, 'Connection error. Please try again later.');
    }
  }

  void showSuccessDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
          ),
          title: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green,
                size: MediaQuery.of(context).size.width * 0.06,
              ),
              SizedBox(width: MediaQuery.of(context).size.width * 0.02),
              Text(
                'Success',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: MediaQuery.of(context).size.width * 0.045,
                ),
              ),
            ],
          ),
          content: Text(
            message,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width * 0.04 > 16
                  ? 16
                  : MediaQuery.of(context).size.width * 0.04,
            ),
            textAlign: TextAlign.center,
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  onPressed: () {
                    Navigator.pop(dialogContext);
                    Navigator.pushAndRemoveUntil(
                        context,
                        MaterialPageRoute(builder: (context) => LoginScreen()),
                        (route) => false);
                  },
                  child: Text('OK'),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

// Helper function to show error dialog
  void showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
          ),
          title: Row(
            children: [
              Icon(
                Icons.error,
                color: Colors.red,
                size: MediaQuery.of(context).size.width * 0.06,
              ),
              SizedBox(width: MediaQuery.of(context).size.width * 0.02),
              Text(
                'Error',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: MediaQuery.of(context).size.width * 0.045,
                ),
              ),
            ],
          ),
          content: Text(
            message,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width * 0.04 > 16
                  ? 16
                  : MediaQuery.of(context).size.width * 0.04,
            ),
            textAlign: TextAlign.center,
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  onPressed: () => Navigator.pop(context),
                  child: Text('OK'),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // Setting Tile Widget
  Widget buildSettingTile({
    required IconData icon,
    required String text,
    required VoidCallback onTap,
  }) {
    return ListTile(
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 15),
      leading: Icon(
        icon,
        color: const Color(0xFF00838F),
        size: 28.0,
      ),
      title: Text(
        text,
        style: const TextStyle(
          fontFamily: 'Robot',
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Color(0xFF00838F),
        ),
      ),
      trailing: const Icon(
        CupertinoIcons.forward,
        color: Color(0xFF00838F),
      ),
    );
  }

  // Setting Switch Tile Widget
  Widget buildSettingSwitchTile({
    required IconData icon,
    required String text,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      leading: Icon(
        icon,
        color: const Color(0xFF00838F),
        size: 28.0,
      ),
      title: Text(
        text,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16,
          color: Color(0xFF00838F),
        ),
      ),
      trailing: Transform.scale(
        scale: 0.9,
        alignment: Alignment.centerRight,
        child: CupertinoSwitch(
          activeColor: const Color(0xFF00838F),
          value: value,
          onChanged: onChanged,
        ),
      ),
    );
  }
}
