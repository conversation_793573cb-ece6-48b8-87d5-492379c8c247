name: smartsewa
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+59

environment:
  sdk: ">=2.19.2 <3.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  carousel_slider: ^5.0.0
  cupertino_icons: ^1.0.5

  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_multiselect: any
  flutter_rating_bar: ^4.0.1
  geolocator: ^10.1.0
  geocoding: ^2.1.0
  timer_button_fork: ^4.0.1
  get: ^4.6.5
  google_maps_flutter: ^2.5.0
  http: ^1.1.0
  image_picker: ^1.0.4
  intl: ^0.19.0
  # khalti_flutter: ^3.0.0
  logger: ^2.0.2+1
  permission_handler: ^11.0.1
  share_plus: ^7.0.2
  shared_preferences: ^2.0.20
  smooth_page_indicator: ^1.0.1
  url_launcher: ^6.1.11
  flutter_dotenv: ^5.1.0
  webview_flutter: ^4.4.2
  fluttertoast: ^8.2.2
  custom_info_window: ^1.0.1
  image_cropper: ^5.0.0
  flutter_offline: any
  dropdown_search: ^5.0.6
  flutter_spinkit: ^5.2.0
  dio: ^5.3.2
  upgrader: ^11.4.0
  connectivity_plus: ^5.0.1
  local_auth: ^2.3.0
  flutter_staggered_animations: ^1.1.1
  khalti_checkout_flutter: ^1.0.0-dev.7
  marquee: ^2.3.0
  shimmer: ^3.0.0
  flutter_image_compress: ^2.3.0
  facebook_app_events: ^0.19.2
  # esewa_flutter_sdk:
  #   path: ./esewa_flutter_sdk

dev_dependencies:
  flutter_lints: ^3.0.1
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/category_image/
    - assets/services_images/
    - .env
  #   - images/a_dot_ham.jpeg
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
