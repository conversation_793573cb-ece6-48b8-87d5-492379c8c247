import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:smartsewa/network/services/authServices/register_service.dart';
import 'package:smartsewa/network/services/exraServices/payment_controller.dart';
import 'package:smartsewa/views/khalti_sdk_demo.dart';
import 'package:smartsewa/views/user_screen/esewa_payment_screen.dart';
import 'package:smartsewa/views/utils.dart';
import 'package:smartsewa/views/widgets/buttons/app_buttons.dart';
import 'package:smartsewa/views/widgets/custom_text.dart';
import 'package:smartsewa/views/widgets/custom_toasts.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';

import '../core/development/console.dart';
import '../core/states.dart';
import '../payment.dart';
import 'package:http/http.dart' as http;
import 'dart:async'; // For TimeoutException

class PaymentScreen extends StatefulWidget {
  PaymentScreen({
    super.key,
  });
  final Map<String, dynamic>? data = Get.arguments;

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen>
    with TickerProviderStateMixin {
  final getController = Get.put(RegisterServiceController());
  final paymentController = Get.put(PaymentController());
  bool showDueAmount = false;
  List<dynamic>? parsedJson;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  // Primary color for the app
  static const Color primaryColor = Color.fromARGB(240, 0, 131, 143);
  static const Color accentColor = Color.fromARGB(255, 0, 150, 163);
  static const Color backgroundColor = Color.fromARGB(255, 248, 250, 252);
  static const Color cardColor = Colors.white;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );
    _animationController.forward();
  }

  void submit(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double iconSize = screenWidth * 0.06; // ~24 on 400px width
    double spacing = screenWidth * 0.02; // ~8 on 400px width
    double borderRadius = screenWidth * 0.05; // ~20 on 400px width

    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        title: Row(
          children: [
            Icon(Icons.info_outline, color: primaryColor, size: iconSize),
            SizedBox(width: spacing),
            const Text(
              'Confirmation',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ],
        ),
        content: const Text('Are you sure you want to submit the payment?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey[600],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(screenWidth * 0.02),
              ),
            ),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(),
            style: ElevatedButton.styleFrom(
              backgroundColor: primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(screenWidth * 0.02),
              ),
              elevation: 0,
            ),
            child: const Text('Yes'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;
    final horizontalPadding = isTablet ? size.width * 0.15 : size.width * 0.04;
    final verticalPadding = size.height * 0.025; // ~20 on 800px height
    final backIconSize = size.width * 0.06; // Responsive back icon size
    final marginSize = size.width * 0.02; // Responsive margin for leading

    return WillPopScope(
      onWillPop: () async {
        selectedPaymentDurationService.value = null;
        selectedPaymentMethod.value = null;
        return true;
      },
      child: Scaffold(
        backgroundColor: backgroundColor,
        appBar: myAppbar(
          context,
          true,
          'Payment',
          leading: GestureDetector(
            onTap: () {
              selectedPaymentDurationService.value = null;
              selectedPaymentMethod.value = null;
              Get.back();
            },
            child: Container(
              margin: EdgeInsets.all(marginSize),
              child: Icon(Icons.arrow_back,
                  color: Colors.white, size: backIconSize),
            ),
          ),
        ),
        body: AnimatedBuilder(
          animation: _fadeAnimation,
          builder: (context, child) {
            return Opacity(
              opacity: _fadeAnimation.value,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: horizontalPadding.toDouble(),
                      vertical: verticalPadding,
                    ),
                    child: Column(
                      children: [
                        _buildHeaderSection(context, size),
                        SizedBox(height: size.height * 0.03), // ~24 on 800px
                        _buildPaymentCard(context, size, isTablet),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context, Size size) {
    final isTablet = size.width > 600;
    final padding = isTablet ? size.width * 0.05 : size.width * 0.06;
    final iconSize = isTablet ? 64.0 : 48.0;
    final spacingSmall = size.height * 0.015; // e.g. ~12
    final spacingLarge = size.height * 0.02; // e.g. ~16

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [primaryColor, accentColor],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.payment,
            size: iconSize,
            color: Colors.white,
          ),
          SizedBox(height: spacingSmall),
          Text(
            'Payment Gateway',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: isTablet ? 28 : 22,
                ),
          ),
          SizedBox(height: spacingLarge * 0.8),
          Text(
            'Secure and fast payment processing',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: isTablet ? 18 : 14,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentCard(BuildContext context, Size size, bool isTablet) {
    final verticalSpacingSmall = size.height * 0.015;
    final verticalSpacingMedium = size.height * 0.025;
    final verticalSpacingLarge = size.height * 0.035;
    final cardPadding = isTablet ? size.width * 0.05 : 24.0;
    final maxCardWidth = isTablet ? 600.0 : double.infinity;

    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxWidth: maxCardWidth,
      ),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(cardPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Payment Duration Section
            _buildSectionHeader('Payment Period', Icons.schedule),
            SizedBox(height: verticalSpacingSmall),
            _buildPeriodDropdown(context),

            // Promo Code Section
            // _buildSectionHeader('Promo Code', Icons.local_offer),
            // SizedBox(height: verticalSpacingSmall),
            // _buildPromoCodeField(context),

            SizedBox(height: verticalSpacingMedium),

            // Payment Method Section
            _buildSectionHeader('Payment Method', Icons.credit_card),
            SizedBox(height: verticalSpacingSmall),
            _buildPaymentMethodDropdown(context),

            SizedBox(height: verticalSpacingLarge),

            // Price Summary
            _buildPriceSummary(context),

            SizedBox(height: verticalSpacingLarge),

            // Submit Button
            _buildSubmitButton(context, size),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    final width = MediaQuery.of(Get.context!).size.width;
    final paddingSize = width * 0.02; // ~2% of screen width for padding
    final sizedBoxWidth = width * 0.03; // ~3% of screen width for spacing

    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(
              paddingSize.clamp(6, 12)), // keep padding reasonable
          decoration: BoxDecoration(
            color: primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: primaryColor, size: 20),
        ),
        SizedBox(width: sizedBoxWidth.clamp(8, 16)), // min 8 max 16 width
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildPeriodDropdown(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final horizontalPadding =
        (width * 0.04).clamp(12, 24); // 4% width, min 12 max 24
    final verticalPadding =
        (width * 0.03).clamp(12, 20); // 3% width, min 12 max 20

    return ValueListenableBuilder(
      valueListenable: selectedPaymentDurationService,
      builder: (context, value, child) => Container(
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.grey.withOpacity(0.2)),
        ),
        child: DropdownButtonFormField<PeriodData>(
          value: value,
          onChanged: (PeriodData? val) async {
            if (parsedJson == null) {
              await _updatePaymentDuration(val);
              await _updatePaymentDuration(val);
              selectedPaymentDurationService.value = val;
            } else {
              await _updatePaymentDuration(val);
              selectedPaymentDurationService.value = val;
            }
          },
          validator: (val) {
            if (val == null) {
              return 'Please select a period';
            }
            return null;
          },
          decoration: InputDecoration(
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(
              horizontal: horizontalPadding.toDouble(),
              vertical: verticalPadding.toDouble(),
            ),
            hintText: 'Select payment period',
            hintStyle: TextStyle(color: Colors.grey[600]),
            prefixIcon:
                Icon(Icons.calendar_today, color: primaryColor, size: 20),
          ),
          dropdownColor: Colors.white,
          icon: Icon(Icons.keyboard_arrow_down, color: primaryColor),
          items: PeriodData.values.map((PeriodData period) {
            return DropdownMenuItem<PeriodData>(
              value: period,
              child: Text(
                periodToStringService(period: period),
                style: const TextStyle(fontSize: 16),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildPromoCodeField(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
      ),
      child: TextFormField(
        controller: paymentController.promoCodeField,
        style: const TextStyle(color: Colors.black87, fontSize: 16),
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          hintText: 'Enter promo code (optional)',
          hintStyle: TextStyle(color: Colors.grey[600]),
          prefixIcon: Icon(Icons.local_offer, color: primaryColor, size: 20),
          suffixIcon: paymentController.promoCodeField.text.isNotEmpty
              ? IconButton(
                  icon: Icon(Icons.clear, color: Colors.grey[600]),
                  onPressed: () {
                    paymentController.promoCodeField.clear();
                    setState(() {});
                  },
                )
              : null,
        ),
        onChanged: (value) => setState(() {}),
      ),
    );
  }

  Widget _buildPaymentMethodDropdown(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final horizontalPadding = (width * 0.04).clamp(12, 24);
    final verticalPadding = (width * 0.03).clamp(12, 20);

    // Responsive sizes based on width
    final iconSize = (width * 0.05).clamp(16, 24);
    final logoWidth = (width * 0.08).clamp(24, 32);
    final logoHeight = (width * 0.06).clamp(18, 24);
    final textFontSize = (width * 0.04).clamp(14, 16);
    final hintFontSize = (width * 0.035).clamp(12, 14);
    final dropdownIconSize = (width * 0.05).clamp(20, 24);

    return ValueListenableBuilder(
      valueListenable: selectedPaymentMethod,
      builder: (context, value, child) => Container(
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.grey.withOpacity(0.2)),
        ),
        child: DropdownButtonFormField<PaymentMethod>(
          value: value,
          onChanged: (PaymentMethod? val) {
            selectedPaymentMethod.value = val;
          },
          validator: (val) {
            if (val == null) {
              return 'Please select a payment method';
            }
            return null;
          },
          decoration: InputDecoration(
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(
              horizontal: horizontalPadding.toDouble(),
              vertical: verticalPadding.toDouble(),
            ),
            hintText: 'Choose payment method',
            hintStyle: TextStyle(
                color: Colors.grey[600], fontSize: hintFontSize.toDouble()),
            prefixIcon: Icon(Icons.payment,
                color: primaryColor, size: iconSize.toDouble()),
          ),
          dropdownColor: Colors.white,
          icon: Icon(Icons.keyboard_arrow_down,
              color: primaryColor, size: dropdownIconSize.toDouble()),
          items: PaymentMethod.values.map((PaymentMethod payment) {
            return DropdownMenuItem<PaymentMethod>(
              value: payment,
              child: Row(
                children: [
                  Container(
                    width: logoWidth.toDouble(),
                    height: logoHeight.toDouble(),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Colors.grey.withOpacity(0.3)),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: Image.asset(
                        paymentLogos[payment] ?? "assets/default_logo.png",
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(Icons.payment,
                              size: (iconSize * 0.8), color: Colors.grey);
                        },
                      ),
                    ),
                  ),
                  SizedBox(width: width * 0.03), // responsive spacing
                  Text(
                    paymentToString(payment: payment),
                    style: TextStyle(fontSize: textFontSize.toDouble()),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildPriceSummary(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isTablet = screenWidth >= 768;
    final isDesktop = screenWidth >= 1024;
    final isSmallScreen = screenWidth < 360;

    // Responsive dimensions
    final containerPadding = EdgeInsets.all(isDesktop
        ? 24
        : isTablet
            ? 20
            : isSmallScreen
                ? 12
                : 16);

    final borderRadius = BorderRadius.circular(isDesktop
        ? 20
        : isTablet
            ? 16
            : 12);

    final iconSize = isDesktop
        ? 28.0
        : isTablet
            ? 26.0
            : isSmallScreen
                ? 20.0
                : 24.0;

    final spacing = isDesktop
        ? 16.0
        : isTablet
            ? 14.0
            : isSmallScreen
                ? 8.0
                : 12.0;

    final titleFontSize = isDesktop
        ? 20.0
        : isTablet
            ? 19.0
            : isSmallScreen
                ? 16.0
                : 18.0;

    final amountFontSize = isDesktop
        ? 24.0
        : isTablet
            ? 22.0
            : isSmallScreen
                ? 18.0
                : 20.0;

    return LayoutBuilder(
      builder: (context, constraints) {
        // Check if we need to stack vertically on very narrow screens
        final shouldStack = constraints.maxWidth < 300;

        return Container(
          width: double.infinity,
          padding: containerPadding,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                primaryColor.withOpacity(0.05),
                accentColor.withOpacity(0.05)
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: borderRadius,
            border: Border.all(
              color: primaryColor.withOpacity(0.2),
              width: isDesktop ? 1.5 : 1.0,
            ),
          ),
          child: shouldStack
              ? _buildVerticalLayout(
                  iconSize: iconSize,
                  spacing: spacing,
                  titleFontSize: titleFontSize,
                  amountFontSize: amountFontSize,
                )
              : _buildHorizontalLayout(
                  iconSize: iconSize,
                  spacing: spacing,
                  titleFontSize: titleFontSize,
                  amountFontSize: amountFontSize,
                  constraints: constraints,
                ),
        );
      },
    );
  }

  Widget _buildHorizontalLayout({
    required double iconSize,
    required double spacing,
    required double titleFontSize,
    required double amountFontSize,
    required BoxConstraints constraints,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Flexible(
          flex: constraints.maxWidth < 400 ? 3 : 2,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.account_balance_wallet,
                color: primaryColor,
                size: iconSize,
              ),
              SizedBox(width: spacing),
              Flexible(
                child: Text(
                  "Total Amount:",
                  style: TextStyle(
                    fontSize: titleFontSize,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: spacing),
        Flexible(
          flex: constraints.maxWidth < 400 ? 2 : 1,
          child: Text(
            getAmountForPeriodService(
                  period: selectedPaymentDurationService.value,
                  value: parsedJson,
                ) as String? ??
                'Rs 0',
            style: TextStyle(
              fontSize: amountFontSize,
              fontWeight: FontWeight.bold,
              color: primaryColor,
            ),
            textAlign: TextAlign.end,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildVerticalLayout({
    required double iconSize,
    required double spacing,
    required double titleFontSize,
    required double amountFontSize,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.account_balance_wallet,
              color: primaryColor,
              size: iconSize,
            ),
            SizedBox(width: spacing),
            Flexible(
              child: Text(
                "Total Amount:",
                style: TextStyle(
                  fontSize: titleFontSize,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        SizedBox(height: spacing),
        Container(
          width: double.infinity,
          child: Text(
            getAmountForPeriodService(
                  period: selectedPaymentDurationService.value,
                  value: parsedJson,
                ) as String? ??
                'Rs 0',
            style: TextStyle(
              fontSize: amountFontSize,
              fontWeight: FontWeight.bold,
              color: primaryColor,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton(BuildContext context, Size size) {
    return Obx(() {
      final isLoading =
          paymentController.isLoading.value || getController.isLoading.value;

      return AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: double.infinity,
        height: size.height * 0.07, // Responsive height based on screen size
        constraints: BoxConstraints(
          minHeight: 48, // Minimum height for accessibility
          maxHeight: 64, // Maximum height to prevent oversizing
        ),
        child: ElevatedButton(
          onPressed: isLoading ? null : _handlePaymentSubmission,
          style: ElevatedButton.styleFrom(
            backgroundColor: primaryColor,
            foregroundColor: Colors.white,
            elevation: isLoading ? 0 : 8,
            shadowColor: primaryColor.withOpacity(0.4),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                size.width * 0.04, // Responsive border radius
              ),
            ),
            disabledBackgroundColor: Colors.grey[300],
            padding: EdgeInsets.symmetric(
              horizontal: size.width * 0.04, // Responsive horizontal padding
              vertical: size.height * 0.015, // Responsive vertical padding
            ),
          ),
          child: isLoading
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: size.width * 0.05, // Responsive loader size
                      height: size.width * 0.05,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    SizedBox(width: size.width * 0.03), // Responsive spacing
                    Text(
                      'Processing...',
                      style: TextStyle(
                        fontSize: size.width * 0.04, // Responsive font size
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.security,
                      size: size.width * 0.05, // Responsive icon size
                    ),
                    SizedBox(width: size.width * 0.02), // Responsive spacing
                    Text(
                      'Proceed to Payment',
                      style: TextStyle(
                        fontSize: size.width * 0.04, // Responsive font size
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
        ),
      );
    });
  }

  void _handlePaymentSubmission() {
    paymentController.amountField.text = getAmountForPeriodService(
      period: selectedPaymentDurationService.value,
      value: parsedJson,
    );

    int amount = int.parse(paymentController.amountField.text) * 100;

    var uniqueKhaltiId = "${DateTime.now().millisecondsSinceEpoch}";
    var mobilenumber =
        getController.profileController.currentUserData.value.mobileNumber;
    String paymentDuration = periodToStringService(
      period: selectedPaymentDurationService.value,
    ).replaceAll(' ', '');

    PaymentMethod paymen;
    if (selectedPaymentMethod.value == PaymentMethod.khalti) {
      paymen = PaymentMethod.khalti;
    } else if (selectedPaymentMethod.value == PaymentMethod.connect) {
      paymen = PaymentMethod.connect;
    } else if (selectedPaymentMethod.value == PaymentMethod.ebanking) {
      paymen = PaymentMethod.ebanking;
    } else {
      paymen = PaymentMethod.khalti;
    }

    if (paymentController.amountField.text.isEmpty ||
        selectedPaymentDurationService.value == null ||
        selectedPaymentMethod.value == null) {
      errorToast(msg: "Please fill all required fields");
    } else {
      if (selectedPaymentMethod.value == PaymentMethod.khalti ||
          selectedPaymentMethod.value == PaymentMethod.connect ||
          selectedPaymentMethod.value == PaymentMethod.ebanking) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => KhaltiSDKDemo(
              onPaymentInitiated: (khalti) {
                khalti.open(context);
              },
              mobileNumber: mobilenumber.toString(),
              finalAmount: amount.toString(),
              paymentDuration: paymentDuration,
            ),
          ),
        );
      } else if (selectedPaymentMethod.value == PaymentMethod.esewa) {
        Get.to(() => EsewaPaymentScreen(
              mobileNumber: mobilenumber.toString(),
              finalAmount: paymentController.amountField.text,
              paymentDuration: paymentDuration,
              merchant: "ESEWA",
            ));
      }
    }
  }

  // final TextEditingController amountField = TextEditingController();
  final TextEditingController promoCodeField = TextEditingController();
  final TextEditingController dueAmountField = TextEditingController();

  @override
  void dispose() {
    _animationController.dispose();
    promoCodeField.dispose();
    dueAmountField.dispose();
    super.dispose();
  }

  String periodToString(PeriodData period) {
    switch (period) {
      default:
        return '0 year';
    }
  }

  String getAmountForPeriod(PeriodData? period) {
    switch (period) {
      default:
        return '0';
    }
  }

  Future<void> _updatePaymentDuration(PeriodData? val) async {
    setState(() {
      selectedPaymentDurationService.value = val;
    });

    if (val != null) {
      if (parsedJson == null) {
        String jsonResponse = await fetchPrice();
        if (jsonResponse == 'Error: Fetching data') {
          parsedJson = null;
          consolelog(jsonResponse);
          errorToast(msg: jsonResponse);
        } else {
          parsedJson = jsonDecode(jsonResponse);
        }
      }
    }
  }

  Future<String> fetchPrice() async {
    var headers = {
      'Content-Type': 'application/json',
    };
    var request = http.Request('GET', Uri.parse('$baseUrl/api/price'));
    request.headers.addAll(headers);

    try {
      consolelog('API hit');

      http.StreamedResponse response =
          await request.send().timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        return await response.stream.bytesToString();
      } else {
        consolelog('Error: HTTP ${response.statusCode}');
        return 'Error: Fetching data';
      }
    } on TimeoutException catch (_) {
      consolelog('Error: Request timed out');
      return 'Error: Fetching data';
    } on http.ClientException catch (e) {
      consolelog('Error: Client error - ${e.message}');
      return 'Error: Fetching data';
    } catch (e) {
      consolelog('Error: An unexpected error occurred - $e');
      return 'Error: Fetching data';
    }
  }

  final Map<PaymentMethod, String> paymentLogos = {
    PaymentMethod.khalti: "assets/khalti_logo.png",
    PaymentMethod.esewa: "assets/esewa_logo.png",
    PaymentMethod.connect: "assets/connect_ips_logo.png",
    PaymentMethod.ebanking: "assets/ebanking_logo.png",
  };

  void openPromoCodeDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          title: Row(
            children: [
              Icon(Icons.local_offer, color: primaryColor, size: 24),
              const SizedBox(width: 8),
              const Text('Enter Promo Code'),
            ],
          ),
          content: Container(
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.withOpacity(0.2)),
            ),
            child: TextField(
              controller: promoCodeField,
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(16),
                hintText: 'Enter promo code',
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel', style: TextStyle(color: Colors.grey[600])),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  updateDueAmount();
                  Navigator.of(context).pop();
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: primaryColor,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
              ),
              child: const Text('Apply', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }

  String calculateDiscountedAmount(String amount, String promoCode) {
    if (promoCode == 'smartsewa') {
      double originalAmount = double.parse(amount.replaceAll('Rs ', ''));
      double discount = originalAmount * 0.5;
      double discountedAmount = originalAmount - discount;
      return 'Rs ${discountedAmount.toStringAsFixed(0)}';
    } else {
      return amount;
    }
  }

  void updateDueAmount() {}
}
