// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:flutter/material.dart';

import 'package:smartsewa/views/widgets/my_appbar.dart';

class VacancyTemplate extends StatefulWidget {
  final String? token;
  const VacancyTemplate({
    super.key,
    this.token,
  });

  @override
  State<VacancyTemplate> createState() => _AddNewVacancyState();
}

class _AddNewVacancyState extends State<VacancyTemplate> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: myAppbar(context, true, "Vacancy Template"),
    );
  }
}
