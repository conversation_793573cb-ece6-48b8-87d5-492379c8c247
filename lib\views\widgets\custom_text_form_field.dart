// ignore_for_file: public_member_api_docs, sort_constructors_first

/*
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../core/regex_config.dart';

class CustomTextFormField extends StatelessWidget {
  TextEditingController? controller;
  String? hintText;
  TextInputType textInputType;
  String? labelText;
  Widget? suffix;
  bool? isEnabled;
  bool readOnly;
  bool obscureText;
  final Function? validator;
  bool onlyText;
  bool onlyNumber;
  int? maxLine;
  int? minLine;
  int? maxLength;
  bool? prefixText;
  bool? filled;
  Color? fillColor;
  IconData? prefixIcon;
  Function()? onTap;
  Function? onChanged;
  Function? onFieldSubmitted;
  String? initialValue;
  bool? isFromSearch;
  bool? autoFocus;
  AutovalidateMode? autovalidateMode;
  List<String> autoFillHint;
  bool searchString;
  bool fullNameString;
  bool doubleNumber;
  bool latlng;
  TextInputAction? textInputAction;
  double borderRadius;
  Color? textColor;
  CustomTextFormField({
    super.key,
    this.textColor,
    this.controller,
    this.hintText,
    this.textInputType = TextInputType.text,
    this.labelText,
    this.suffix,
    this.isEnabled = true,
    this.readOnly = false,
    this.obscureText = false,
    this.validator,
    this.onlyText = false,
    this.onlyNumber = false,
    this.maxLine = 1,
    this.minLine = 1,
    this.maxLength,
    this.prefixText,
    this.filled = false,
    this.fillColor = const Color(0xffF4F4F4),
    this.prefixIcon,
    this.onTap,
    this.onChanged,
    this.onFieldSubmitted,
    this.initialValue,
    this.isFromSearch = false,
    this.autoFocus = false,
    this.autovalidateMode,
    this.autoFillHint = const [],
    this.searchString = false,
    this.fullNameString = false,
    this.doubleNumber = false,
    this.latlng = false,
    this.textInputAction,
    this.borderRadius = 8,
    required InputDecoration decoration,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      minLines: minLine,
      maxLines: maxLine,
      maxLength: maxLength,
      textInputAction: textInputAction,
      autofillHints: autoFillHint,
      autofocus: autoFocus ?? false,
      validator: (value) {
        return validator == null ? null : validator!(value);
      },
      style: TextStyle(
        color: readOnly ? Colors.grey : textColor,
        fontSize: 16,
        fontFamily: "Outfit",
        fontWeight: FontWeight.w400,
      ),
      inputFormatters: onlyNumber
          ? [
              FilteringTextInputFormatter.allow(RegexConfig.numberRegex),
            ]
          : onlyText
              ? [
                  FilteringTextInputFormatter.allow(RegexConfig.textRegex),
                ]
              : searchString
                  ? [
                      FilteringTextInputFormatter.allow(
                          RegexConfig.searchRegrex)
                    ]
                  : fullNameString
                      ? [
                          FilteringTextInputFormatter.allow(
                              RegexConfig.fullNameTextRegrex)
                        ]
                      : doubleNumber
                          ? [
                              FilteringTextInputFormatter.allow(
                                  RegExp(r'(^\d*\.?\d*)'))
                            ]
                          : [],
      readOnly: readOnly,
      initialValue: initialValue,
      enabled: isEnabled,
      onTap: onTap,
      onChanged: (val) => isFromSearch == true ? onChanged!(val) : null,
      autovalidateMode: autovalidateMode ?? AutovalidateMode.onUserInteraction,
      controller: controller,
      // onFieldSubmitted: (val) =>
      //     isFromSearch == true ? onFieldSubmitted!(val) : null,
      keyboardType: textInputType,
      obscureText: obscureText,
      decoration: InputDecoration(
        // prefixText: prefixText == true ? "$phoneNumberPrefixText " : null,
        filled: filled,
        errorStyle: const TextStyle(
          fontSize: 10.0,
          fontFamily: "Outfit",
        ),
        hintStyle: const TextStyle(
          fontSize: 16.0,
          color: Colors.grey,
        ),
        prefixIcon: prefixIcon != null
            ? Icon(
                prefixIcon,
                color: Colors.grey,
              )
            : null,
        fillColor: filled == true ? fillColor : null,
        hintText: hintText,
        labelText: labelText,
        suffixIcon: suffix,
        enabledBorder: filled == true
            ? InputBorder.none
            : OutlineInputBorder(
                borderRadius: BorderRadius.circular(borderRadius),
                borderSide: BorderSide(
                  color: Colors.grey.withOpacity(0.6),
                ),
              ),
        border: filled == true
            ? InputBorder.none
            : OutlineInputBorder(
                borderRadius: BorderRadius.circular(borderRadius),
                borderSide: BorderSide(
                  color: Colors.grey.withOpacity(0.6),
                ),
              ),
        focusedBorder: filled == true
            ? InputBorder.none
            : OutlineInputBorder(
                borderRadius: BorderRadius.circular(borderRadius),
                borderSide: BorderSide(
                  color: Colors.grey.withOpacity(0.6),
                ),
              ),
      ),
    );
  }
}


*/

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../core/regex_config.dart';

class CustomTextFormField extends StatelessWidget {
  final TextEditingController? controller;
  final String? hintText;
  final TextInputType textInputType;
  final String? labelText;
  final Widget? suffix;
  final bool? isEnabled;
  final bool readOnly;
  final bool obscureText;
  final Function? validator;
  final bool onlyText;
  final bool onlyNumber;
  final int? maxLine;
  final int? minLine;
  final int? maxLength;
  final bool? prefixText;
  final bool? filled;
  final Color? fillColor;
  final IconData? prefixIcon;
  final Function()? onTap;
  final Function? onChanged;
  final Function? onFieldSubmitted;
  final String? initialValue;
  final bool? isFromSearch;
  final bool? autoFocus;
  final AutovalidateMode? autovalidateMode;
  final List<String> autoFillHint;
  final bool searchString;
  final bool fullNameString;
  final bool doubleNumber;
  final bool latlng;
  final TextInputAction? textInputAction;
  final double borderRadius;
  final Color? textColor;
  final InputDecoration? decoration; // New parameter for decoration

  const CustomTextFormField({
    super.key,
    this.textColor,
    this.controller,
    this.hintText,
    this.textInputType = TextInputType.text,
    this.labelText,
    this.suffix,
    this.isEnabled = true,
    this.readOnly = false,
    this.obscureText = false,
    this.validator,
    this.onlyText = false,
    this.onlyNumber = false,
    this.maxLine = 1,
    this.minLine = 1,
    this.maxLength,
    this.prefixText,
    this.filled = false,
    this.fillColor = const Color(0xffF4F4F4),
    this.prefixIcon,
    this.onTap,
    this.onChanged,
    this.onFieldSubmitted,
    this.initialValue,
    this.isFromSearch = false,
    this.autoFocus = false,
    this.autovalidateMode,
    this.autoFillHint = const [],
    this.searchString = false,
    this.fullNameString = false,
    this.doubleNumber = false,
    this.latlng = false,
    this.textInputAction,
    this.borderRadius = 8,
    this.decoration, // Initialize the decoration parameter
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      minLines: minLine,
      maxLines: maxLine,
      maxLength: maxLength,
      textInputAction: textInputAction,
      autofillHints: autoFillHint,
      autofocus: autoFocus ?? false,
      validator: (value) {
        return validator == null ? null : validator!(value);
      },
      style: TextStyle(
        color: readOnly ? Colors.grey : textColor,
        fontSize: 16,
        fontFamily: "Outfit",
        fontWeight: FontWeight.w400,
      ),
      inputFormatters: onlyNumber
          ? [
              FilteringTextInputFormatter.allow(RegexConfig.numberRegex),
            ]
          : onlyText
              ? [
                  FilteringTextInputFormatter.allow(RegexConfig.textRegex),
                ]
              : searchString
                  ? [
                      FilteringTextInputFormatter.allow(
                          RegexConfig.searchRegrex)
                    ]
                  : fullNameString
                      ? [
                          FilteringTextInputFormatter.allow(
                              RegexConfig.fullNameTextRegrex)
                        ]
                      : doubleNumber
                          ? [
                              FilteringTextInputFormatter.allow(
                                  RegExp(r'(^\d*\.?\d*)'))
                            ]
                          : [],
      readOnly: readOnly,
      initialValue: initialValue,
      enabled: isEnabled,
      onTap: onTap,
      onChanged: (val) => isFromSearch == true ? onChanged!(val) : null,
      autovalidateMode: autovalidateMode ?? AutovalidateMode.onUserInteraction,
      controller: controller,
      keyboardType: textInputType,
      obscureText: obscureText,
      decoration: decoration ??
          InputDecoration(
            labelText: labelText,
            labelStyle: const TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
            prefixIcon: prefixIcon != null
                ? Icon(
                    prefixIcon,
                    color: const Color.fromRGBO(0, 131, 143, 1),
                  )
                : null,
            filled: filled,
            fillColor: fillColor,
            hintText: hintText,
            suffixIcon: suffix,
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide:
                  const BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide:
                  const BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: const BorderSide(color: Colors.red),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: const BorderSide(color: Colors.red),
            ),
          ),
    );
  }
}
