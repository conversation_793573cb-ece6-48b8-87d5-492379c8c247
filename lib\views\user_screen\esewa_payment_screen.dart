import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/core/route_navigation.dart';
import 'package:smartsewa/views/serviceProviderScreen/service_main_screen.dart';
import 'package:smartsewa/views/user_screen/main_screen.dart';
import 'package:smartsewa/views/utils.dart';
import 'package:smartsewa/views/widgets/custom_toasts.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../widgets/custom_snackbar.dart';

import 'package:http/http.dart' as http;

//import 'package:esewa_flutter_sdk/esewa_config.dart';
//import 'package:esewa_flutter_sdk/esewa_flutter_sdk.dart';
//import 'package:esewa_flutter_sdk/esewa_payment.dart';
//import 'package:esewa_flutter_sdk/esewa_payment_success_result.dart';

class EsewaPaymentScreen extends StatefulWidget {
  String mobileNumber;
  String finalAmount;
  String paymentDuration;
  String merchant;
  EsewaPaymentScreen(
      {super.key,
      required this.paymentDuration,
      required this.mobileNumber,
      required this.finalAmount,
      required this.merchant});

  @override
  State<EsewaPaymentScreen> createState() => _EsewaPaymentScreenState();
}

class _EsewaPaymentScreenState extends State<EsewaPaymentScreen> {
  WebViewController? _webViewController;
  String uniqueEsewaId = '';
  bool initialLoader = true;
  // String esewaUrl = BaseClient().esewa

  @override
  void initState() {
    super.initState();
    uniqueEsewaId = "${DateTime.now().millisecondsSinceEpoch}";
    consolelog(uniqueEsewaId);
  }

  Future<void> verifyPayment(String? refCode) async {
    var headers = {'Content-Type': 'application/json'};

    var request =
        http.Request('POST', Uri.parse('$baseUrl/api/v1/user/payments'));

    request.body = json.encode({
      "paymentDetails": "ServiceProvider",
      "paymentDuration": int.tryParse(
              (widget.paymentDuration).replaceAll(RegExp(r'[^0-9]'), '')) ??
          1,
      "merchant": "ESEWA",
      "refcode": refCode,
      "mobileNumber": widget.mobileNumber
    });

    request.headers.addAll(headers);

    try {
      http.StreamedResponse response = await request.send();

      consolelog(request.body);

      if (response.statusCode == 200) {
        // Parse the response body as JSON
        String responseBody = await response.stream.bytesToString();
        var jsonResponse = json.decode(responseBody);

        // Retrieve message and success fields from the response
        String message = jsonResponse['message'];
        bool success = jsonResponse['success'];
        successToast(msg: message);

        print('Message: $message');
        print('Success: $success');
      } else {
        // Handle non-200 status codes
        String responseBody = await response.stream.bytesToString();
        var jsonResponse = json.decode(responseBody);
        String message = jsonResponse['message'];
        errorToast(msg: message);
        print('Error: ${response.statusCode} - ${response.reasonPhrase}');
      }
    } on SocketException {
      errorToast(msg: 'No Internet connection /n Contact Smartsewa');
    } on HttpException {
      errorToast(msg: 'Server error. /n Contact Smartsewa');
    } on FormatException {
      errorToast(msg: 'Bad response format. /n Contact Smartsewa');
    } catch (e) {
      errorToast(msg: 'Unexpected error: /n Contact Smartsewa $e');
    }
  }

  success(String? refId) async {
    verifyPayment(refId);
    Get.back();
  }

  NavigationDecision _interceptNavigation(NavigationRequest request) {
    if (request.url.contains('esewa_payment_success')) {
      console(Uri.parse(request.url.toString()).queryParameters['refId']);

      console("success ${request.url}");

      success(Uri.parse(request.url.toString()).queryParameters['refId']);
      return NavigationDecision.prevent;
    } else if (request.url.contains('esewa_payment_failed')) {
      errorToast(msg: "Payment failed at esewa end");
      consolelog("alex4");
      back(context);
      console("failed ${request.url}");
      return NavigationDecision.prevent;
    } else {
      return NavigationDecision.navigate;
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        CustomSnackBar.showSnackBar(
            title: "Payment Cancelled", color: Colors.red);
        return true;
      },
      child: Scaffold(
        appBar: myAppbar(context, true, "Esewa Payment"),
        body: Stack(
          children: [
WebViewWidget(
  controller: _webViewController ??= WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted)
    ..setNavigationDelegate(
      NavigationDelegate(
        onNavigationRequest: _interceptNavigation,
        onWebResourceError: (error) {
          if (error.errorType ==
              WebResourceErrorType.javaScriptExceptionOccurred) {
            CustomSnackBar.showSnackBar(
                title: 'Failed to execute', color: Colors.red);
          } else if (error.errorType ==
              WebResourceErrorType.fileNotFound) {
            CustomSnackBar.showSnackBar(
                title: "Sorry, transaction is failed", color: Colors.red);
          } else if (error.description.contains('CORS')) {
            CustomSnackBar.showSnackBar(
                title: 'Cross-origin request blocked.',
                color: Colors.red);
          } else {
            CustomSnackBar.showSnackBar(
                title: "Failed to proceed", color: Colors.red);
          }
        },
        onPageFinished: (data) {},
      ),
    )
    ..loadHtmlString('''
      <html>
        <body></body>
        <script type="text/javascript">
          var path="https://esewa.com.np/epay/main";
          var params= {
            amt: ${widget.finalAmount},
            psc: 0,
            pdc: 0,
            txAmt: 0,
            tAmt: ${widget.finalAmount},
            pid: "$uniqueEsewaId",
            scd: "NP-ES-SMARTSEWA",
            su: "http://merchant.com.np/page/esewa_payment_success",
            fu: "http://merchant.com.np/page/esewa_payment_failed"
          };

          function post(path, params) {
            var form = document.createElement("form");
            form.setAttribute("method", "POST");
            form.setAttribute("action", path);

            for(var key in params) {
              var hiddenField = document.createElement("input");
              hiddenField.setAttribute("type", "hidden");
              hiddenField.setAttribute("name", key);
              hiddenField.setAttribute("value", params[key]);
              form.appendChild(hiddenField);
            }

            document.body.appendChild(form);
            form.submit();
          }
          post(path,params);
        </script>
      </html>
    '''),
),
            Visibility(
              visible: initialLoader,
              child: Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    color: Colors.white,
                    child: const LinearProgressIndicator(),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}

















     //    su: "http://merchant.com.np/page/esewa_payment_success",


//  var path= "https://uat.esewa.com.np/epay/main";

/*
import 'package:flutter/material.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/core/route_navigation.dart';
import 'package:smartsewa/esewa_flutter_sdk/lib/esewa_config.dart';
import 'package:smartsewa/views/widgets/custom_toasts.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../widgets/custom_snackbar.dart';

class EsewaPaymentScreen extends StatefulWidget {
  const EsewaPaymentScreen({Key? key}) : super(key: key);

  @override
  State<EsewaPaymentScreen> createState() => _EsewaPaymentScreenState();
}

class _EsewaPaymentScreenState extends State<EsewaPaymentScreen> {
  late WebViewController _webViewController;
  String uniqueEsewaId = '';
  bool initialLoader = true;

  @override
  void initState() {
    uniqueEsewaId = "${DateTime.now().millisecondsSinceEpoch}";
    consolelog(uniqueEsewaId);
    super.initState();
  }

  void success(String? refId) async {}

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        CustomSnackBar.showSnackBar(
            title: "Payment Cancelled", color: Colors.red);
        return true;
      },
      child: Scaffold(
        appBar: myAppbar(context, true, "Esewa Payment"),
        body: Stack(
          children: [
            WebViewWidget(
              controller: _webViewController = WebViewController()
                ..setJavaScriptMode(JavaScriptMode.unrestricted)
                // ..loadRequest(Uri.parse('https://uat.esewa.com.np/epay/main'))
                ..setNavigationDelegate(
                  NavigationDelegate(
                    onProgress: (int progress) {
                      // Update loading bar.
                    },
                    onPageStarted: (String url) {
                      "about:blank";
                    },
                    onPageFinished: (String url) {},
                    onWebResourceError: (WebResourceError error) {
                      if (error.errorType ==
                          WebResourceErrorType.javaScriptExceptionOccurred) {
                        CustomSnackBar.showSnackBar(
                            title: 'Failed to execute', color: Colors.red);
                      } else if (error.errorType ==
                          WebResourceErrorType.fileNotFound) {
                        CustomSnackBar.showSnackBar(
                            title: "Sorry, transaction is failed",
                            color: Colors.red);
                      } else {
                        CustomSnackBar.showSnackBar(
                            title: "Failed to proceed", color: Colors.red);
                      }
                    },
                    onNavigationRequest: (NavigationRequest request) {
                      if (request.url.contains('esewa_payment_success')) {
                        console(Uri.parse(request.url.toString())
                            .queryParameters['refId']);
                        console("success ${request.url}");
                        success(Uri.parse(request.url.toString())
                            .queryParameters['refId']);
                        return NavigationDecision.prevent;
                      } else if (request.url.contains('esewa_payment_failed')) {
                        errorToast(msg: "Payment failed at esewa end");
                        back(context);
                        console("failed ${request.url}");
                        return NavigationDecision.prevent;
                      } else {
                        return NavigationDecision.navigate;
                      }
                    },
                  ),
                )
                ..loadRequest(Uri.parse('https://uat.esewa.com.np/epay/main')),
              onWebViewCreated: (webViewController) {
                _webViewController = webViewController;
                _webViewController.runJavaScriptReturningResult('''
      var path= "https://uat.esewa.com.np/epay/main";
      var params= {
        amt: "10",
        psc: 0,
        pdc: 0,
        txAmt: 0,
        tAmt: "10",
        pid: '$uniqueEsewaId',
        scd: "EPAYTEST",
        su: "http://merchant.com.np/page/esewa_payment_success",
        fu: "http://merchant.com.np/page/esewa_payment_failed"
      }

      function post(path, params) {
        var form = document.createElement("form");
        form.setAttribute("method", "POST");
        form.setAttribute("action", path);

        for(var key in params) {
                var hiddenField = document.createElement("input");
                hiddenField.setAttribute("type", "hidden");
                hiddenField.setAttribute("name", key);
                hiddenField.setAttribute("value", params[key]);
                form.appendChild(hiddenField);
        }

        document.body.appendChild(form);
        form.submit();
      }
      post(path,params);
      ''').then((value) {
                  setState(() {
                    initialLoader = false;
                  });
                });
              },
            ),
            Visibility(
              visible: initialLoader,
              child: Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    color: Colors.white,
                    child: const LinearProgressIndicator(),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
*/
