import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:get/get.dart'; // Add this import for Get
import 'package:smartsewa/network/services/authServices/auth_controller.dart';
import 'package:smartsewa/views/auth/registration/user_registration.dart';
import 'package:smartsewa/views/serviceProviderScreen/extraServices/upload_secondhand.dart';
import 'package:smartsewa/views/user_screen/showExtraServices/roomdetail.dart'; // Import for UserRegistration
import '../../widgets/my_appbar.dart';

class RoomFinderPage extends StatefulWidget {
  const RoomFinderPage({Key? key}) : super(key: key);

  @override
  _RoomFinderPageState createState() => _RoomFinderPageState();
}

class _RoomFinderPageState extends State<RoomFinderPage> {
  final TextEditingController _searchController = TextEditingController();
  final AuthController authController =
      Get.find<AuthController>(); // Initialize authController
  String selectedCategory = 'All';
  List<Map<String, String>> filteredRooms = [];

  // Dummy data for the list of rooms
  final List<Map<String, String>> rooms = [
    {
      "name": "Luxurious Duplex Flat",
      "location": "Kathmandu, Nepal",
      "price": "NPR 30,000/month",
      "daysRemaining": "10 days",
      "image": "https://via.placeholder.com/400x300?text=Luxury+Apartment",
      "views": "150",
      "bedrooms": "3",
      "bathrooms": "2",
      "parking": "Yes",
    },
    {
      "name": "Modern Studio Apartment",
      "location": "Pokhara, Nepal",
      "price": "NPR 25,000/month",
      "daysRemaining": "5 days",
      "image": "https://via.placeholder.com/400x300?text=Studio+Apartment",
      "views": "100",
      "bedrooms": "1",
      "bathrooms": "1",
      "parking": "No",
    },
    {
      "name": "Cozy One-Bedroom Flat",
      "location": "Lalitpur, Nepal",
      "price": "NPR 15,000/month",
      "daysRemaining": "3 days",
      "image": "https://via.placeholder.com/400x300?text=Cozy+Flat",
      "views": "50",
      "bedrooms": "1",
      "bathrooms": "1",
      "parking": "Yes",
    },
  ];

  final List<String> bannerImages = [
    "https://via.placeholder.com/800x300?text=Special+Offer",
    "https://via.placeholder.com/800x300?text=New+Properties",
    "https://via.placeholder.com/800x300?text=Featured+Homes",
    "https://via.placeholder.com/800x300?text=Featured+Homes",
  ];

  @override
  void initState() {
    super.initState();
    filteredRooms = List.from(rooms);
  }

  void _filterRoomsByCategory(String category) {
    setState(() {
      selectedCategory = category;
      if (category == 'All') {
        filteredRooms = List.from(rooms); // Show all rooms
      } else {
        filteredRooms = rooms
            .where((room) =>
                room['name']!.toLowerCase().contains(category.toLowerCase()))
            .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: myAppbar(context, true, "RealEstate"),
      body: RefreshIndicator(
        onRefresh: () async {
          // Implement refresh functionality
          await Future.delayed(const Duration(seconds: 1));
          setState(() {
            filteredRooms = List.from(rooms);
          });
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSearchBar(),
                const SizedBox(height: 16),
                _buildCategories(),
                const SizedBox(height: 16),
                _buildBannerSlider(),
                const SizedBox(height: 24),
                _buildSectionHeader(),
                const SizedBox(height: 16),
                _buildRoomsList(),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          if (authController.isUserGuest()) {
            final double screenWidth = MediaQuery.of(context).size.width;
            final bool isSmallScreen = screenWidth < 360;

            showDialog(
              context: context,
              builder: (BuildContext context) {
                return Dialog(
                  backgroundColor: Colors.transparent,
                  insetPadding: EdgeInsets.all(screenWidth * 0.05),
                  child: Container(
                    decoration: BoxDecoration(
                        color: const Color.fromARGB(240, 0, 131, 143),
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.2),
                            spreadRadius: 1,
                            blurRadius: 6,
                            offset: const Offset(0, 3),
                          ),
                        ]),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: EdgeInsets.all(screenWidth * 0.04),
                          decoration: const BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(25),
                                  topRight: Radius.circular(25)),
                              boxShadow: [
                                BoxShadow(
                                  color: Color.fromRGBO(0, 0, 0, 0.1),
                                  blurRadius: 8,
                                ),
                              ]),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.verified_user_rounded,
                                color: const Color.fromARGB(240, 0, 131, 143),
                                size: screenWidth * 0.08,
                              ),
                              SizedBox(width: screenWidth * 0.03),
                              Flexible(
                                child: Text(
                                  'Access Required',
                                  style: TextStyle(
                                    color:
                                        const Color.fromARGB(240, 0, 131, 143),
                                    fontSize: isSmallScreen ? 18 : 22,
                                    fontWeight: FontWeight.w700,
                                    letterSpacing: 0.8,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.all(screenWidth * 0.05),
                          child: Column(
                            children: [
                              SizedBox(height: screenWidth * 0.03),
                              Text(
                                'Please SignUp to get access on all features.',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: isSmallScreen ? 14 : 16,
                                  height: 1.4,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: screenWidth * 0.06),
                              // Buttons Row
                              LayoutBuilder(
                                builder: (context, constraints) {
                                  return Row(
                                    children: [
                                      Expanded(
                                        child: ElevatedButton(
                                          onPressed: () =>
                                              Navigator.pop(context),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.transparent,
                                            elevation: 0,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(15),
                                              side: const BorderSide(
                                                color: Colors.white,
                                                width: 1.5,
                                              ),
                                            ),
                                            padding: EdgeInsets.symmetric(
                                              vertical: screenWidth * 0.03,
                                            ),
                                          ),
                                          child: Text(
                                            'Later',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: isSmallScreen ? 14 : 16,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: screenWidth * 0.04),
                                      Expanded(
                                        child: ElevatedButton(
                                          onPressed: () {
                                            Navigator.pop(context);
                                            Get.to(() => UserRegistration());
                                          },
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.white,
                                            elevation: 4,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(15),
                                            ),
                                            padding: EdgeInsets.symmetric(
                                              vertical: screenWidth * 0.03,
                                            ),
                                          ),
                                          child: Text(
                                            'Sign Up Now',
                                            style: TextStyle(
                                              color: const Color.fromARGB(
                                                  240, 0, 131, 143),
                                              fontSize: isSmallScreen ? 14 : 16,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          } else {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => UploadSecondHand()),
            );
          }
        },
        backgroundColor:
            const Color.fromARGB(240, 0, 131, 143), // The color you prefer
        child: const Icon(
          Icons.add,
          color: Colors.white,
        ), // The icon for adding real estate
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextField(
        controller: _searchController,
        decoration: const InputDecoration(
          hintText: "Search for rooms...",
          prefixIcon: Icon(Icons.search),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16),
        ),
        onChanged: _filterRoomsByCategory,
      ),
    );
  }

  Widget _buildCategories() {
    return SizedBox(
      height: 100,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildCategoryItem(Icons.all_inclusive, "All"),
          _buildCategoryItem(Icons.king_bed, "Rooms"),
          _buildCategoryItem(Icons.apartment, "Flat"), // All section added
          _buildCategoryItem(Icons.home, "House"),
          _buildCategoryItem(Icons.local_hotel, "Hotels"),
          _buildCategoryItem(Icons.landscape, "Land"),
          _buildCategoryItem(Icons.business, "Business"),
        ],
      ),
    );
  }

  Widget _buildCategoryItem(IconData icon, String label) {
    return GestureDetector(
      onTap: () {
        _filterRoomsByCategory(label);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('$label category selected')),
        );
      },
      child: Container(
        width: 80,
        margin: const EdgeInsets.only(right: 16),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color.fromARGB(240, 0, 131, 143),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: Colors.white),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBannerSlider() {
    return CarouselSlider(
      options: CarouselOptions(
        height: 180,
        autoPlay: true,
        enlargeCenterPage: true,
        aspectRatio: 16 / 9,
        autoPlayInterval: const Duration(seconds: 3),
        autoPlayAnimationDuration: const Duration(milliseconds: 800),
      ),
      items: bannerImages.map((imageUrl) {
        return GestureDetector(
          child: Container(
            width: MediaQuery.of(context).size.width,
            margin: const EdgeInsets.symmetric(horizontal: 5.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: DecorationImage(
                image: NetworkImage(imageUrl),
                fit: BoxFit.cover,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSectionHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          "Nearby Rooms",
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        TextButton(
          onPressed: () {
            // Implement see all functionality
          },
          child: const Text(
            "See All",
            style: TextStyle(
              color: Color.fromARGB(240, 0, 131, 143),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRoomsList() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: filteredRooms.length,
      itemBuilder: (context, index) {
        return _buildRoomCard(filteredRooms[index]);
      },
    );
  }

  Widget _buildRoomCard(Map<String, String> room) {
    return GestureDetector(
      onTap: () {
        if (authController.isUserGuest()) {
          final double screenWidth = MediaQuery.of(context).size.width;
          final bool isSmallScreen = screenWidth < 360;

          showDialog(
            context: context,
            builder: (BuildContext context) {
              return Dialog(
                backgroundColor: Colors.transparent,
                insetPadding: EdgeInsets.all(screenWidth * 0.05),
                child: Container(
                  decoration: BoxDecoration(
                      color: const Color.fromARGB(240, 0, 131, 143),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.2),
                          spreadRadius: 1,
                          blurRadius: 6,
                          offset: const Offset(0, 3),
                        ),
                      ]),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: EdgeInsets.all(screenWidth * 0.04),
                        decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(25),
                                topRight: Radius.circular(25)),
                            boxShadow: [
                              BoxShadow(
                                color: Color.fromRGBO(0, 0, 0, 0.1),
                                blurRadius: 8,
                              ),
                            ]),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.verified_user_rounded,
                              color: const Color.fromARGB(240, 0, 131, 143),
                              size: screenWidth * 0.08,
                            ),
                            SizedBox(width: screenWidth * 0.03),
                            Flexible(
                              child: Text(
                                'Access Required',
                                style: TextStyle(
                                  color: const Color.fromARGB(240, 0, 131, 143),
                                  fontSize: isSmallScreen ? 18 : 22,
                                  fontWeight: FontWeight.w700,
                                  letterSpacing: 0.8,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(screenWidth * 0.05),
                        child: Column(
                          children: [
                            SizedBox(height: screenWidth * 0.03),
                            Text(
                              'Please SignUp to get access on all features.',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: isSmallScreen ? 14 : 16,
                                height: 1.4,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            SizedBox(height: screenWidth * 0.06),
                            // Buttons Row
                            LayoutBuilder(
                              builder: (context, constraints) {
                                return Row(
                                  children: [
                                    Expanded(
                                      child: ElevatedButton(
                                        onPressed: () => Navigator.pop(context),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.transparent,
                                          elevation: 0,
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(15),
                                            side: const BorderSide(
                                              color: Colors.white,
                                              width: 1.5,
                                            ),
                                          ),
                                          padding: EdgeInsets.symmetric(
                                            vertical: screenWidth * 0.03,
                                          ),
                                        ),
                                        child: Text(
                                          'Later',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: isSmallScreen ? 14 : 16,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: screenWidth * 0.04),
                                    Expanded(
                                      child: ElevatedButton(
                                        onPressed: () {
                                          Navigator.pop(context);
                                          Get.to(() => UserRegistration());
                                        },
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.white,
                                          elevation: 4,
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(15),
                                          ),
                                          padding: EdgeInsets.symmetric(
                                            vertical: screenWidth * 0.03,
                                          ),
                                        ),
                                        child: Text(
                                          'Sign Up Now',
                                          style: TextStyle(
                                            color: const Color.fromARGB(
                                                240, 0, 131, 143),
                                            fontSize: isSmallScreen ? 14 : 16,
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        } else {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const RoomDetailPage(
                roomDetails: {
                  'category': 'Room on rent',
                  'title': 'Beautiful Room in Kathmandu',
                  'location': 'Kathmandu',
                  'description': 'Spacious room with all facilities...',
                  'price': '15000',
                  'floor': '2nd',
                  'landArea': '1200 sqft',
                  'roadSize': '12 ft',
                  'roadType': 'Pitched',
                  'waterSupply': 'Yes',
                  'parking': '4 Wheeler',
                  'negotiable': 'Yes',
                  'contactPerson': 'John Doe',
                  'image': null,
                },
              ),
            ),
          );
        }
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 6,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(12)),
              child: Image.network(
                room['image']!,
                height: 180, // Adjusted size for better responsiveness
                width: double.infinity,
                fit: BoxFit.cover,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    room['name']!,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.location_on,
                          size: 18, color: Colors.grey),
                      const SizedBox(width: 6),
                      Text(
                        room['location']!,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12), // Adjusted space
                  // Row for Bedroom, Bathroom, Parking icons with Yes/No options
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildFeatureIcon(Icons.bed, room['bedrooms']!),
                      _buildFeatureIcon(Icons.bathtub, room['bathrooms']!),
                      _buildFeatureIcon(Icons.local_parking, room['parking']!),
                    ],
                  ),
                  const SizedBox(height: 12), // Adjusted space between rows
                  // Row for Views and Remaining Days with icons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildFeatureWithIcon(
                          Icons.remove_red_eye, '${room['views']} views'),
                      _buildFeatureWithIcon(Icons.access_time,
                          'Remaining: ${room['daysRemaining']}'),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    room['price']!,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureIcon(IconData icon, String value) {
    return Row(
      children: [
        Icon(icon, size: 18, color: Colors.grey),
        const SizedBox(width: 6),
        Text(
          value.isNotEmpty ? value : "N/A", // Check if value is not empty
          style: const TextStyle(fontSize: 14, color: Colors.grey),
        ),
      ],
    );
  }

  Widget _buildFeatureWithIcon(IconData icon, String value) {
    return Row(
      children: [
        Icon(icon, size: 18, color: Colors.grey),
        const SizedBox(width: 6),
        Text(
          value,
          style: const TextStyle(fontSize: 14, color: Colors.grey),
        ),
      ],
    );
  }
}
