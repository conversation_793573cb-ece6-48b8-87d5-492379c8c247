import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:smartsewa/network/services/authServices/auth_controller.dart';
import 'package:smartsewa/views/utils.dart';

import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/network/base_client.dart';
import 'package:smartsewa/network/services/userdetails/current_user_controller.dart';

import 'package:smartsewa/views/user_screen/approval/open_map_screen.dart';
import 'package:smartsewa/views/user_screen/main_screen.dart';

import 'package:smartsewa/views/widgets/custom_toasts.dart';
import '../../widgets/buttons/app_buttons.dart';

import 'package:http/http.dart' as http;
import '../../user_screen/approval/map_controller.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';

class ApprovalScreen extends StatefulWidget {
  final mapController = Get.put(MapController());
  ApprovalScreen({
    super.key,
    required String mobileNumber,
    required String fullName,
  });

  final profileController = Get.put(CurrentUserController());

  final isLoading = false.obs;

  @override
  State<ApprovalScreen> createState() => _ApprovalScreenState();
}

class _ApprovalScreenState extends State<ApprovalScreen> {
  final profileController = Get.put(CurrentUserController());
  String baseUrl = BaseClient().baseUrl;

  @override
  void initState() {
    widget.profileController.getCurrentUser();
    super.initState();
  }

  final controller = Get.put(AuthController());
  final TextEditingController _skillController = TextEditingController();
  final TextEditingController _workingAddressController =
      TextEditingController();
  final TextEditingController _documentController = TextEditingController();

  bool workStatus = false;
  bool approval = false;

  String? citizenshipFrontUrl;
  String? citizenshipBackUrl;
  String? drivingLicenseUrl;
  String? nationalIdUrl;
  File? citizenshipFront;
  File? citizenshipBack;
  File? drivingLicense;
  File? nationalId;
  int count = 0, count1 = 0;

  var isLoading = false.obs;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  Position? position;

  String selectedMainSkill = '';
  List<String> selectedSkills = [];
  Map<String, List<String>> selectedSubSkills = {};

  Future<void> becomeServiceProvider({
    required Map<String, dynamic> jobDetails,
    required String latitude,
    required bool workStatus,
    required bool approval,
    File? citizenshipFront,
    File? citizenshipBack,
    File? drivingLicense,
    File? nationalId,
    int? id,
  }) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");
    int? id = prefs.getInt("id");

    try {
      // Upload images and get URLs
      if (citizenshipFront != null && citizenshipBack != null) {
        if (citizenshipFrontUrl == null && citizenshipBackUrl == null) {
          var result = await uploadImage('citizenshipFront', citizenshipFront);
          citizenshipFrontUrl = result['citizenshipFront'];

          var result1 = await uploadImage('citizenshipBack', citizenshipBack);
          citizenshipBackUrl = result1['citizenshipBack'];
        }
      } else {
        if (count == 1) {
          errorToast(msg: 'Select both sides of citizenship');
        }
      }

      if (drivingLicense != null) {
        if (drivingLicenseUrl == null) {
          var result = await uploadImage('drivingLicense', drivingLicense);
          drivingLicenseUrl = result['drivingLicense'];
          citizenshipFrontUrl = drivingLicenseUrl;
        }
      } else {
        if (count == 2) {
          errorToast(msg: 'Driving License not selected');
        }
      }
      if (nationalId != null) {
        if (nationalIdUrl == null) {
          var result = await uploadImage('nationalId', nationalId);
          nationalIdUrl = result['nationalId'];
          citizenshipFrontUrl = nationalIdUrl;
        }
      } else {
        if (count == 3) {
          errorToast(msg: 'National Id not selected');
        }
      }
      if (count == 0) {
        errorToast(msg: 'Please select any one of the document');
      }

      if (selectedJobCategoryId == null || selectedServiceIds.isEmpty) {
        errorToast(msg: 'Please select your skills and sub-skills');
        return;
      }

      final Map<String, dynamic> requestData = {
        "jobDetails": {
          "jobCategoryId": int.parse(selectedJobCategoryId!),
          "serviceIds": selectedServiceIds.map(int.parse).toList(),
        },
        "latitude": latitude,
        "workStatus": workStatus,
        "approval": approval,
        "imageUrlCitizenshipFront": citizenshipFrontUrl,
        "imageUrlCitizenshipBack": citizenshipBackUrl,
      };

      final String jsonData = jsonEncode(requestData);

      consolelog('Register Service Request Data: $jsonData');

      if (drivingLicenseUrl != null ||
          nationalIdUrl != null ||
          (citizenshipBackUrl != null && citizenshipFrontUrl != null)) {
        final response = await http.put(
          Uri.parse('$baseUrl/api/users/$id/update'),
          headers: <String, String>{
            'Content-Type': 'application/json',
            'Authorization': "Bearer $apptoken",
          },
          body: jsonData,
        );

        consolelog(response.statusCode);
        if (response.statusCode == 200) {
          successToast(msg: "Service Provider Registered Successfully");
          // Get.offAll(() => const Profile());
          id = jsonDecode(response.body)['id'];

          consolelog(id);
        } else {
          count = 5;
          errorToast(
              msg: 'Error registering service provider: ${response.body}');
        }
      } else {
        count1 = 5;
      }
    } catch (err) {
      if (err.toString().contains('SocketException')) {
        errorToast(msg: 'No internet connection');
      } else {
        errorToast(msg: err.toString());
      }
    } finally {}
  }

//////////////////////////////for documents upload/////////////////////////////

  final List<File?> _pickedImages = [];
  Future<void> _pickImage(ImageSource source, String documentType) async {
    final picker = ImagePicker();

    try {
      if (documentType == 'citizenship') {
        final selectedSide = await _showCitizenshipSideDialog();
        if (selectedSide == null) {
          return;
        }
        if (selectedSide == 'front') {
          documentType = 'citizenship_front';
          final selectedSource =
              await _showImageSourceDialog("Select Front Side ");
          if (selectedSource == null) {
            return;
          }
          source = selectedSource;
        } else {
          documentType = 'citizenship_back';
          final selectedSource =
              await _showImageSourceDialog("Select Back Side ");
          if (selectedSource == null) {
            return;
          }
          source = selectedSource;
        }
      } else if (documentType == 'driving_license' ||
          documentType == 'national_id') {
        final selectedSource = await _showImageSourceDialog("Select Image");
        if (selectedSource == null) {
          return;
        }
        source = selectedSource;
      }

      final pickedFile = await picker.pickImage(source: source);

      if (pickedFile != null) {
        final File pickedImage = File(pickedFile.path);
        final File? compressedImage = await compressImage(pickedImage);

        if (compressedImage != null) {
          setState(() {
            switch (documentType) {
              case 'citizenship_front':
                citizenshipFront = compressedImage;
                _documentController.text = "Citizenship Front Selected";
                count = 1;
                break;
              case 'citizenship_back':
                citizenshipBack = compressedImage;
                _documentController.text = "Citizenship  Selected";
                count = 1;
                break;
              case 'driving_license':
                drivingLicense = compressedImage;
                _documentController.text = "Driving License  Selected";
                count = 2;
                break;
              case 'national_id':
                nationalId = compressedImage;
                _documentController.text = "National Id  Selected";
                count = 3;

                break;
              default:
                break;
            }

            final side = documentType.split('_').last;
            final message = 'Successfully Selected $side side';
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(message)),
            );

            Future.delayed(const Duration(seconds: 1), () {
              if (documentType == 'citizenship_front') {
                _pickImage(source, 'citizenship');
              }
            });
          });
          consolelog('Selected Document Type: $documentType');
        } else {
          consolelog('Image compression failed');
        }
      } else {
        consolelog('No image selected');
      }
    } catch (error) {
      consolelog('Error picking image: $error');
    }
  }

  Future<ImageSource?> _showImageSourceDialog(String title) async {
    return await showDialog<ImageSource>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          actions: [
            ListTile(
              leading: const Icon(Icons.camera),
              title: const Text('Take a Picture'),
              onTap: () {
                Navigator.pop(context, ImageSource.camera);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo),
              title: const Text('Choose from Gallery'),
              onTap: () {
                Navigator.pop(context, ImageSource.gallery);
              },
            ),
          ],
        );
      },
    );
  }

  Future<String?> _showCitizenshipSideDialog() async {
    bool isBackEnabled = citizenshipFront != null;

    return await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Citizenship Side'),
          actions: [
            ListTile(
              title: const Text('Front'),
              enabled: citizenshipFront == null,
              onTap: () {
                if (citizenshipFront != null) {
                  errorToast(msg: "Already Selected (Front)");
                } else {
                  Navigator.pop(context, 'front');
                }
              },
            ),
            ListTile(
              title: const Text('Back'),
              enabled: isBackEnabled,
              onTap: () async {
                if (isBackEnabled) {
                  final selectedSource =
                      await _showImageSourceDialog("Select Back Side ");
                  if (selectedSource != null) {
                    Navigator.pop(context, 'back');
                  }
                }
              },
            ),
          ],
        );
      },
    );
  }

  // @override
  // Widget build(BuildContext context) {
  //   Size size = MediaQuery.of(context).size;
  //   return Scaffold(
  //     appBar: myAppbar(context, true, "Become A Service Provider"),
  //     body: Padding(
  //       padding: const EdgeInsets.fromLTRB(15.0, 10.0, 15.0, 0),
  //       child: SingleChildScrollView(
  //         child: Form(
  //           key: _formKey,
  //           child: Column(
  //               crossAxisAlignment: CrossAxisAlignment.center,
  //               children: [
  //                 SizedBox(height: size.height * 0.02),
  //                 Image.asset(
  //                   'assets/Logo.png',
  //                   height: size.height * 0.2,
  //                 ),
  //                 SizedBox(height: size.height * 0.02),
  //                 SizedBox(height: size.height * 0.02),
  //                 buildSkillsDropdown(),
  //                 SizedBox(height: size.height * 0.02),
  //                 Row(
  //                   children: [
  //                     Expanded(
  //                       child: TextFormField(
  //                         controller: _workingAddressController,
  //                         decoration: const InputDecoration(
  //                           labelText: 'Select your working address',
  //                           labelStyle: TextStyle(
  //                               color: Color.fromRGBO(0, 131, 143, 1)),
  //                           focusedBorder: OutlineInputBorder(
  //                             borderSide: BorderSide(
  //                                 color: Color.fromRGBO(0, 131, 143, 1)),
  //                           ),
  //                         ),
  //                         autovalidateMode: AutovalidateMode.onUserInteraction,
  //                         validator: (value) {
  //                           if (value == null || value.isEmpty) {
  //                             return 'Please enter your working address';
  //                           }
  //                           return null;
  //                         },
  //                       ),
  //                     ),
  //                     const SizedBox(width: 16.0),
  //                     ElevatedButton(
  //                       onPressed: () async {
  //                         permission = await Geolocator.requestPermission();
  //                         if (permission == LocationPermission.deniedForever) {
  //                           await Geolocator.openAppSettings();
  //                         } else if (permission != LocationPermission.denied &&
  //                             permission != LocationPermission.deniedForever) {
  //                           widget.mapController.isMapLoading.value = true;
  //                           position = await Geolocator.getCurrentPosition(
  //                               desiredAccuracy: LocationAccuracy.high);

  //                           LatLng selectedLocation =
  //                               await Get.to(() => OpenMapScreen(
  //                                     completeGoogleMapController:
  //                                         completeGoogleMapController,
  //                                     kGoogle: kGoogle,
  //                                     marker: marker,
  //                                     onpressed: onpressed,
  //                                     onLocationSelected: (LatLng location) {
  //                                       String formattedLocation =
  //                                           '${location.latitude},${location.longitude}';

  //                                       _workingAddressController.text =
  //                                           formattedLocation;
  //                                     },
  //                                   ));
  //                         }
  //                       },
  //                       style: ElevatedButton.styleFrom(
  //                         backgroundColor: const Color.fromRGBO(0, 131, 143, 1),
  //                         minimumSize: const Size(80, 40),
  //                       ),
  //                       child: const Text(
  //                         'Open Map',
  //                         style: TextStyle(color: Colors.white),
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //                 SizedBox(height: size.height * 0.02),
  //                 Row(
  //                   children: [
  //                     Expanded(
  //                       child: _pickedImages.isEmpty
  //                           ? TextFormField(
  //                               controller: _documentController,
  //                               decoration: const InputDecoration(
  //                                 labelText: 'Upload your documents',
  //                                 labelStyle: TextStyle(
  //                                     color: Color.fromRGBO(0, 131, 143, 1)),
  //                                 focusedBorder: OutlineInputBorder(
  //                                   borderSide: BorderSide(
  //                                       color: Color.fromRGBO(0, 131, 143, 1)),
  //                                 ),
  //                               ),
  //                             )
  //                           : Container(),
  //                     ),
  //                     InkWell(
  //                       onTap: () {
  //                         count = 0;
  //                         count1 = 0;
  //                         citizenshipBack = null;
  //                         citizenshipBackUrl = null;
  //                         citizenshipFrontUrl = null;
  //                         drivingLicenseUrl = null;
  //                         nationalIdUrl = null;
  //                         citizenshipFront = null;
  //                         _documentController.text = "";

  //                         drivingLicense = null;
  //                         nationalId = null;
  //                         showModalBottomSheet(
  //                           context: context,
  //                           builder: (BuildContext context) {
  //                             return Column(
  //                               mainAxisSize: MainAxisSize.min,
  //                               children: <Widget>[
  //                                 ListTile(
  //                                   title: const Text('Citizenship'),
  //                                   onTap: () {
  //                                     Navigator.pop(context);

  //                                     _pickImage(
  //                                         ImageSource.gallery, 'citizenship');
  //                                   },
  //                                 ),
  //                                 ListTile(
  //                                   title: Text('Driving License'),
  //                                   onTap: () {
  //                                     Navigator.pop(context);
  //                                     _pickImage(ImageSource.gallery,
  //                                         'driving_license');
  //                                   },
  //                                 ),
  //                                 ListTile(
  //                                   title: Text('National ID'),
  //                                   onTap: () {
  //                                     Navigator.pop(context);
  //                                     _pickImage(
  //                                         ImageSource.gallery, 'national_id');
  //                                   },
  //                                 ),
  //                               ],
  //                             );
  //                           },
  //                         );
  //                       },
  //                       child: Icon(
  //                         Icons.upload_file,
  //                         color: Colors.teal[800], // Change the color as needed
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //                 SizedBox(height: size.height * 0.02),
  //                 SizedBox(height: size.height * 0.02),
  //                 SizedBox(height: size.height * 0.02),
  //                 SizedBox(height: size.height * 0.02),
  //                 buildServiceProviderButton(),
  //               ]),
  //         ),
  //       ),
  //     ),
  //   );
  // }

  Widget buildSkillsDropdown() {
    return TextFormField(
      onTap: () {
        showSkillsDialog();
      },
      controller: _skillController,
      readOnly: true,
      decoration: const InputDecoration(
        labelText: 'Select Your Job Field',
        labelStyle: TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please select your skills';
        }
        return null;
      },
    );
  }

  void showSkillsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Your Job Field'),
          content: SizedBox(
            height: 700,
            child: Scrollbar(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    buildSkillsCheckboxList(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget buildSkillsCheckboxList() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: fetchSkills(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const CircularProgressIndicator();
        } else if (snapshot.hasError) {
          return Text('Error: ${snapshot.error}');
        } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Text('No skills available');
        }

        List<Map<String, dynamic>> skills = snapshot.data!;
        return Column(
          children: skills.map((skill) {
            String categoryId = skill['categoryId']!;
            String categoryTitle = skill['categoryTitle']!;
            return ListTile(
              title: Text(categoryTitle),
              onTap: () {
                setState(() {
                  selectedJobCategoryId = categoryId;
                  if (selectedMainSkill != categoryTitle) {
                    selectedSubSkills[selectedMainSkill] = [];
                    selectedSkills = [];
                    _updateTextFieldText('');
                  }
                  selectedMainSkill = categoryTitle;
                  Navigator.pop(context);
                  showSubSkillsDialog(categoryId, categoryTitle);
                });
              },
            );
          }).toList(),
        );
      },
    );
  }

  void _updateTextFieldText(String mainSkill) {
    if (selectedSkills.isNotEmpty) {
      _skillController.text = '$mainSkill: ${selectedSkills.join(', ')}';
    } else {
      _skillController.text = '';
    }
  }

  void showSubSkillsDialog(String categoryId, String mainSkill) {
    selectedSkills = List.from(selectedSubSkills[mainSkill] ?? []);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return FutureBuilder<List<Map<String, dynamic>>>(
          future: fetchSubSkills(categoryId),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const AlertDialog(
                title: Text('Loading Sub Skills...'),
                content: CircularProgressIndicator(),
              );
            } else if (snapshot.hasError) {
              return AlertDialog(
                title: const Text('Error'),
                content: Text('Error: ${snapshot.error}'),
              );
            } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
              return const AlertDialog(
                title: Text('No Sub Skills Available'),
                content: Text('No sub skills available for this skill.'),
              );
            }

            List<Map<String, dynamic>> subSkillsList = snapshot.data!;

            return StatefulBuilder(
              builder: (context, setState) {
                return AlertDialog(
                  title: const Text('Select Sub Skills'),
                  content: SizedBox(
                    height: 300,
                    child: Scrollbar(
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: subSkillsList.map((subSkill) {
                            return CheckboxListTile(
                              title: Text(subSkill['name']),
                              value: selectedSkills.contains(subSkill['name']),
                              onChanged: (value) {
                                setState(() {
                                  if (value != null) {
                                    if (value) {
                                      selectedSkills.add(subSkill['name']);
                                    } else {
                                      selectedSkills.remove(subSkill['name']);
                                    }
                                  }
                                  _updateTextFieldText(mainSkill);
                                });
                              },
                              controlAffinity: ListTileControlAffinity.leading,
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),
                  actions: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ElevatedButton(
                          onPressed: () {
                            setState(() {
                              selectedSubSkills[mainSkill] =
                                  List.from(selectedSkills);
                              selectedServiceIds = subSkillsList
                                  .where((subSkill) =>
                                      selectedSkills.contains(subSkill['name']))
                                  .map((subSkill) => subSkill['id'].toString())
                                  .toList();
                              _updateTextFieldText(mainSkill);
                            });
                            Navigator.pop(context);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                const Color.fromRGBO(0, 131, 143, 1),
                          ),
                          child: const SizedBox(
                            width: 80,
                            height: 40,
                            child: Center(
                              child: Text(
                                'Done',
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                );
              },
            );
          },
        );
      },
    );
  }

  Widget buildServiceProviderButton() {
    return AppButton(
      onPressed: () async {
        // Check if all required fields are filled
        if (_skillController.text.isEmpty ||
            _workingAddressController.text.isEmpty) {
          errorToast(msg: 'Please fill all required fields');
          return;
        }
        workStatus = true;
        approval = true;

        await becomeServiceProvider(
          jobDetails: {
            "jobCategoryId": int.parse(selectedJobCategoryId!),
            "serviceIds": selectedServiceIds.map(int.parse).toList(),
          },
          latitude: _workingAddressController.text,
          workStatus: workStatus,
          approval: approval,
          citizenshipBack: citizenshipBack,
          citizenshipFront: citizenshipFront,
          drivingLicense: drivingLicense,
          nationalId: nationalId,
          id: profileController.currentUserData.value.id,
        );
        if (count1 != 5 && count1 != 6 && count == 0) {
        } else if (count1 == 5) {
          if (count != 0) {
            consolelog('value: $count');
            errorToast(msg: ' Document Uploading failed \n Please try again');
          }
        } else {
          successToast(msg: ' Request Submitted  Successfully');

          Navigator.pop(context);
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const MainScreen(),
            ),
          );
        }
      },
      name: 'Update',
    );
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: myAppbar(context, true, "Become Service Provider"),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
        child: SingleChildScrollView(
          child: Center(
            child: Container(
              width: size.width * 0.9, // Container width is 90% of screen width
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black12.withOpacity(0.1),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(height: size.height * 0.03),

                  // Job Field Dropdown
                  buildSkillsDropdown(),
                  SizedBox(height: size.height * 0.03),

                  // Working Address Field with Map Button
                  // buildAddressField(size),
                  buildWorkingAddressSection(size),
                  SizedBox(height: size.height * 0.03),

                  // Document Upload Section
                  buildDocumentUploadSection(),
                  SizedBox(height: size.height * 0.03),

                  // Submit Button
                  buildServiceProviderButton(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildMiniMap() {
    // Parse the coordinates from the controller
    LatLng? userLocation;
    if (_workingAddressController.text.isNotEmpty) {
      try {
        List<String> coordinates = _workingAddressController.text.split(',');
        if (coordinates.length == 2) {
          double lat = double.parse(coordinates[0].trim());
          double lng = double.parse(coordinates[1].trim());
          userLocation = LatLng(lat, lng);
        }
      } catch (e) {
        consolelog("Error parsing coordinates: $e");
      }
    }

    return Container(
      margin: const EdgeInsets.only(top: 8.0),
      height: 180,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: const Color.fromRGBO(0, 131, 143, 0.5),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.0),
        child: Stack(
          children: [
            // If using Google Maps
            userLocation != null
                ? GoogleMap(
                    initialCameraPosition: CameraPosition(
                      target:
                          LatLng(userLocation.latitude, userLocation.longitude),
                      zoom: 15,
                    ),
                    zoomControlsEnabled: false,
                    mapToolbarEnabled: false,
                    myLocationEnabled: true,
                    myLocationButtonEnabled: false,
                    markers: {
                      Marker(
                        markerId: const MarkerId('userLocation'),
                        position: LatLng(
                            userLocation.latitude, userLocation.longitude),
                        infoWindow:
                            const InfoWindow(title: 'Your Working Location'),
                      ),
                    },
                    onMapCreated: (GoogleMapController controller) {
                      // Store controller if needed
                    },
                  )
                : const Center(
                    child: Text(
                      'No location selected',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ),

            // Coordinates display
            if (userLocation != null)
              Positioned(
                bottom: 8,
                left: 8,
                right: 8,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Text(
                    'Location: ${userLocation.latitude.toStringAsFixed(6)}, ${userLocation.longitude.toStringAsFixed(6)}',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Color.fromRGBO(0, 131, 143, 1),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),

            // Expand button
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(
                    Icons.fullscreen,
                    color: Color.fromRGBO(0, 131, 143, 1),
                  ),
                  onPressed: () async {
                    permission = await Geolocator.requestPermission();
                    if (permission == LocationPermission.deniedForever) {
                      await Geolocator.openAppSettings();
                    } else if (permission != LocationPermission.denied &&
                        permission != LocationPermission.deniedForever) {
                      widget.mapController.isMapLoading.value = true;
                      position = await Geolocator.getCurrentPosition(
                          desiredAccuracy: LocationAccuracy.high);

                      LatLng selectedLocation =
                          await Get.to(() => OpenMapScreen(
                                completeGoogleMapController:
                                    completeGoogleMapController,
                                kGoogle: kGoogle,
                                marker: marker,
                                onpressed: onpressed,
                                onLocationSelected: (LatLng location) {
                                  String formattedLocation =
                                      '${location.latitude},${location.longitude}';
                                  _workingAddressController.text =
                                      formattedLocation;
                                },
                              ));
                    }
                  },
                  tooltip: 'Expand Map',
                ),
              ),
            ),

            // Select location button
            Positioned(
              top: 8,
              left: 8,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(
                    Icons.add_location_alt,
                    color: Color.fromRGBO(0, 131, 143, 1),
                  ),
                  onPressed: () async {
                    permission = await Geolocator.requestPermission();
                    if (permission == LocationPermission.deniedForever) {
                      await Geolocator.openAppSettings();
                    } else if (permission != LocationPermission.denied &&
                        permission != LocationPermission.deniedForever) {
                      widget.mapController.isMapLoading.value = true;
                      position = await Geolocator.getCurrentPosition(
                          desiredAccuracy: LocationAccuracy.high);

                      LatLng selectedLocation =
                          await Get.to(() => OpenMapScreen(
                                completeGoogleMapController:
                                    completeGoogleMapController,
                                kGoogle: kGoogle,
                                marker: marker,
                                onpressed: onpressed,
                                onLocationSelected: (LatLng location) {
                                  String formattedLocation =
                                      '${location.latitude},${location.longitude}';
                                  _workingAddressController.text =
                                      formattedLocation;
                                },
                              ));
                    }
                  },
                  tooltip: 'Select Location',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildWorkingAddressSection(Size size) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Working Address',
              style: TextStyle(
                color: Color.fromRGBO(0, 131, 143, 1),
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
            // Open Map button - always visible
            ElevatedButton.icon(
              onPressed: () async {
                permission = await Geolocator.requestPermission();
                if (permission == LocationPermission.deniedForever) {
                  await Geolocator.openAppSettings();
                } else if (permission != LocationPermission.denied &&
                    permission != LocationPermission.deniedForever) {
                  widget.mapController.isMapLoading.value = true;
                  position = await Geolocator.getCurrentPosition(
                      desiredAccuracy: LocationAccuracy.high);

                  await Get.to(() => OpenMapScreen(
                        completeGoogleMapController:
                            completeGoogleMapController,
                        kGoogle: kGoogle,
                        marker: marker,
                        onpressed: onpressed,
                        onLocationSelected: (LatLng location) {
                          String formattedLocation =
                              '${location.latitude},${location.longitude}';
                          _workingAddressController.text = formattedLocation;
                          setState(() {}); // Refresh UI to show map
                        },
                      ));
                }
              },
              icon: const Icon(Icons.map, color: Colors.white, size: 16),
              label: const Text('Open Map'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color.fromRGBO(0, 131, 143, 1),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                minimumSize: const Size(100, 36),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8.0),

        // Hidden field to store coordinates
        Opacity(
          opacity: 0,
          child: Container(
            height: 0,
            child: TextFormField(
              controller: _workingAddressController,
              readOnly: true,
            ),
          ),
        ),

        // Show mini map or placeholder
        _workingAddressController.text.isNotEmpty
            ? buildMiniMap()
            : Container(
                width: double.infinity,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(12.0),
                  border: Border.all(
                    color: Colors.grey.shade400,
                    width: 1.0,
                  ),
                ),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.location_on,
                        color: Color.fromRGBO(0, 131, 143, 1),
                        size: 36,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'No location selected',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
      ],
    );
  }

  // Widget buildSkillsDropdown() {
  //   return Container(
  //     padding: const EdgeInsets.symmetric(horizontal: 10.0),
  //     decoration: BoxDecoration(
  //       color: Colors.white,
  //       borderRadius: BorderRadius.circular(15),
  //       boxShadow: [
  //         BoxShadow(
  //           color: Colors.black.withOpacity(0.05),
  //           blurRadius: 8,
  //           offset: const Offset(0, 3),
  //         ),
  //       ],
  //     ),
  //     child: TextFormField(
  //       onTap: () {
  //         // Skills dialog logic goes here
  //       },
  //       readOnly: true,
  //       decoration: InputDecoration(
  //         labelText: 'Select Your Job Field',
  //         labelStyle: const TextStyle(
  //           color: Color.fromRGBO(0, 131, 143, 1),
  //           fontWeight: FontWeight.w600,
  //         ),
  //         filled: true,
  //         fillColor: Colors.white,
  //         enabledBorder: OutlineInputBorder(
  //           borderSide: const BorderSide(color: Colors.teal, width: 1.5),
  //           borderRadius: BorderRadius.circular(12),
  //         ),
  //         focusedBorder: OutlineInputBorder(
  //           borderSide: const BorderSide(color: Colors.teal, width: 1.5),
  //           borderRadius: BorderRadius.circular(12),
  //         ),
  //         prefixIcon: const Icon(Icons.work_outline, color: Colors.teal),
  //       ),
  //     ),
  //   );
  // }

  // Widget buildAddressField(Size size) {
  //   return Container(
  //     padding: const EdgeInsets.symmetric(horizontal: 10.0),
  //     decoration: BoxDecoration(
  //       color: Colors.white,
  //       borderRadius: BorderRadius.circular(15),
  //       boxShadow: [
  //         BoxShadow(
  //           color: Colors.black.withOpacity(0.05),
  //           blurRadius: 8,
  //           offset: const Offset(0, 3),
  //         ),
  //       ],
  //     ),
  //     child: Row(
  //       children: [
  //         Expanded(
  //           child: TextFormField(
  //             controller: _workingAddressController,
  //             readOnly: true, // Prevent user from typing
  //             decoration: InputDecoration(
  //               labelText: 'Select your working address',
  //               labelStyle: const TextStyle(
  //                 color: Color.fromRGBO(0, 131, 143, 1),
  //                 fontWeight: FontWeight.w600,
  //               ),
  //               hintText: 'Tap the button to choose a location', // Placeholder
  //               hintStyle: TextStyle(color: Colors.grey[500]),
  //               filled: true,
  //               fillColor: Colors.white,
  //               enabledBorder: OutlineInputBorder(
  //                 borderSide: const BorderSide(color: Colors.teal, width: 1.5),
  //                 borderRadius: BorderRadius.circular(12),
  //               ),
  //               focusedBorder: OutlineInputBorder(
  //                 borderSide: const BorderSide(color: Colors.teal, width: 1.5),
  //                 borderRadius: BorderRadius.circular(12),
  //               ),
  //               prefixIcon:
  //                   const Icon(Icons.location_on_outlined, color: Colors.teal),
  //             ),
  //           ),
  //         ),
  //         const SizedBox(width: 10.0),
  //         ElevatedButton.icon(
  //           onPressed: () async {
  //             permission = await Geolocator.requestPermission();
  //             if (permission == LocationPermission.deniedForever) {
  //               await Geolocator.openAppSettings();
  //             } else if (permission != LocationPermission.denied &&
  //                 permission != LocationPermission.deniedForever) {
  //               widget.mapController.isMapLoading.value = true;
  //               position = await Geolocator.getCurrentPosition(
  //                   desiredAccuracy: LocationAccuracy.high);
  //               LatLng selectedLocation = await Get.to(() => OpenMapScreen(
  //                     completeGoogleMapController: completeGoogleMapController,
  //                     kGoogle: kGoogle,
  //                     marker: marker,
  //                     onpressed: onpressed,
  //                     onLocationSelected: (LatLng location) {
  //                       String formattedLocation =
  //                           '${location.latitude}, ${location.longitude}';
  //                       _workingAddressController.text = formattedLocation;
  //                     },
  //                   ));
  //             }
  //           },
  //           style: ElevatedButton.styleFrom(
  //             backgroundColor: const Color.fromRGBO(0, 131, 143, 1),
  //             minimumSize: const Size(110, 48),
  //             shape: RoundedRectangleBorder(
  //               borderRadius: BorderRadius.circular(12),
  //             ),
  //           ),
  //           icon: const Icon(Icons.map, color: Colors.white),
  //           label: const Text(
  //             'Open Map',
  //             style: TextStyle(color: Colors.white),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget buildDocumentUploadSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextFormField(
              controller: _documentController,
              readOnly: true, // Users cannot type manually
              decoration: InputDecoration(
                labelText: 'Upload your documents',
                labelStyle: const TextStyle(
                  color: Color.fromRGBO(0, 131, 143, 1),
                  fontWeight: FontWeight.w600,
                ),
                hintText: 'Tap the upload icon to select documents',
                hintStyle: TextStyle(color: Colors.grey[500]),
                filled: true,
                fillColor: Colors.white,
                enabledBorder: OutlineInputBorder(
                  borderSide: const BorderSide(color: Colors.teal, width: 1.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: const BorderSide(color: Colors.teal, width: 1.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                prefixIcon:
                    const Icon(Icons.file_upload_outlined, color: Colors.teal),
              ),
            ),
          ),
          const SizedBox(width: 10.0),
          IconButton(
            icon: Icon(Icons.upload_file, color: Colors.teal[800], size: 30),
            onPressed: () {
              count = 0;
              count1 = 0;
              citizenshipBack = null;
              citizenshipBackUrl = null;
              citizenshipFrontUrl = null;
              drivingLicenseUrl = null;
              nationalIdUrl = null;
              citizenshipFront = null;
              _documentController.text =
                  ""; // Reset field before selecting files

              drivingLicense = null;
              nationalId = null;

              showModalBottomSheet(
                context: context,
                builder: (BuildContext context) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      ListTile(
                        title: const Text('Citizenship'),
                        onTap: () {
                          Navigator.pop(context);
                          _pickImage(ImageSource.gallery, 'citizenship');
                        },
                      ),
                      ListTile(
                        title: const Text('Driving License'),
                        onTap: () {
                          Navigator.pop(context);
                          _pickImage(ImageSource.gallery, 'driving_license');
                        },
                      ),
                      ListTile(
                        title: const Text('National ID'),
                        onTap: () {
                          Navigator.pop(context);
                          _pickImage(ImageSource.gallery, 'national_id');
                        },
                      ),
                    ],
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

//   Widget buildServiceProviderButton() {
//     return SizedBox(
//       width: double.infinity,
//       child: ElevatedButton(
//         onPressed: () {
//           // Submit form logic goes here
//         },
//         style: ElevatedButton.styleFrom(
//           backgroundColor: const Color.fromRGBO(0, 131, 143, 1),
//           padding: const EdgeInsets.symmetric(vertical: 16),
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(12),
//           ),
//           elevation: 5,
//         ),
//         child: const Text(
//           'Submit Request',
//           style: TextStyle(
//             fontSize: 18,
//             fontWeight: FontWeight.bold,
//             color: Colors.white,
//           ),
//         ),
//       ),
//     );
//   }
// }
}


