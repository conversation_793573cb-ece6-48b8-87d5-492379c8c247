
// import 'dart:async';
// import 'dart:convert';
// import 'dart:io';
// import 'package:smartsewa/views/utils.dart';

// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:get/get.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';

// import 'package:image_picker/image_picker.dart';
// import 'package:smartsewa/core/development/console.dart';
// import 'package:smartsewa/network/services/authServices/auth_controller.dart';
// import 'package:smartsewa/views/auth/login/login_screen.dart';

// import 'package:smartsewa/views/user_screen/approval/open_map_screen.dart';
// import 'package:smartsewa/views/widgets/buttons/app_buttons.dart';

// import 'package:smartsewa/views/widgets/custom_toasts.dart';
// import 'package:smartsewa/views/widgets/my_appbar.dart';

// import 'package:http/http.dart' as http;
// import 'package:timer_button_fork/timer_button_fork.dart';

// import '../../user_screen/approval/map_controller.dart';
// import '../../user_screen/drawer screen/privacypolicyscreen.dart';

// class UserRegistration extends StatefulWidget {
//   final mapController = Get.put(MapController());
//   UserRegistration({super.key});

//   @override
//   State<UserRegistration> createState() => _UserRegistrationState();
// }

// class _UserRegistrationState extends State<UserRegistration> {
//   final controller = Get.put(AuthController());
//   final GlobalKey<FormState> _formkey = GlobalKey<FormState>();

// //////////////    controllers for all the form field      ////////////////////////////
//   final TextEditingController _fullNameController = TextEditingController();
//   final TextEditingController _phoneController = TextEditingController();
//   final TextEditingController _addressController = TextEditingController();
//   final TextEditingController _passwordController = TextEditingController();
//   final TextEditingController _confirmPasswordController =
//       TextEditingController();
//   final TextEditingController _otpController = TextEditingController();
//   final TextEditingController _skillController = TextEditingController();
//   final TextEditingController _workingaddressController =
//       TextEditingController();
//   // final TextEditingController _documentController = TextEditingController();

// ///////////////////// global variables ////////////////////////////////////
//   bool isServiceProvider = false;
//   Position? position;
//   bool workStatus = false;
//   bool approval = false;
//   int id = 0;
//   var isLoading = false.obs;
//   bool isFormFilled = false;
//   bool isTermsChecked = false;
//   int count = 0, count1 = 0;
//   String? citizenshipFrontUrl;
//   String? citizenshipBackUrl;
//   String? drivingLicenseUrl;
//   String? nationalIdUrl;
//   bool isEmailVerified = false;
//   bool isPhoneVerified = false;

// //////////////    for select your address dropdown  ///////////////////////////////////////
//   String? selectedDistrict;
//   String? selectedMunicipality;
//   String? selectedWard;
//   String? editedAddress;
//   bool isEditingAddress = false;

// /////////////////////// password visible   ///////////////////////////////////////////
//   bool _passwordVisible = false;
//   bool _confirmPasswordVisible = false;

//   //////////////////////////// OTP DIALOG /////////////////////

//   void showOtpInputDialog(BuildContext context, Function onOtpVerified) {
//   showDialog(
//     context: context,
//     builder: (BuildContext context) {
//       // Get screen dimensions
//       double screenWidth = MediaQuery.of(context).size.width;
//       double screenHeight = MediaQuery.of(context).size.height;
      
//       // Define responsive breakpoints for iOS devices
//       bool isSmallDevice = screenWidth < 375; // iPhone SE, iPhone 12 mini
//       bool isMediumDevice = screenWidth >= 375 && screenWidth < 414; // iPhone 12, 13, 14
//       bool isLargeDevice = screenWidth >= 414; // iPhone Pro Max, Plus models
      
//       // Responsive dialog dimensions
//       double dialogWidth = isSmallDevice ? screenWidth * 0.85 : (isMediumDevice ? screenWidth * 0.8 : screenWidth * 0.75);
//       double dialogPadding = isSmallDevice ? 16 : (isMediumDevice ? 20 : 24);
//       double dialogBorderRadius = isSmallDevice ? 12 : 16;
      
//       // Responsive typography
//       double titleFontSize = isSmallDevice ? 18 : (isMediumDevice ? 20 : 22);
//       double labelFontSize = isSmallDevice ? 14 : (isMediumDevice ? 16 : 18);
//       double buttonFontSize = isSmallDevice ? 14 : (isMediumDevice ? 16 : 18);
//       double inputFontSize = isSmallDevice ? 16 : (isMediumDevice ? 18 : 20);
      
//       // Responsive spacing
//       double contentSpacing = isSmallDevice ? 12 : 16;
//       double buttonSpacing = isSmallDevice ? 8 : 12;
//       double inputHeight = isSmallDevice ? 48 : (isMediumDevice ? 52 : 56);
//       double buttonHeight = isSmallDevice ? 40 : (isMediumDevice ? 44 : 48);
      
//       // Responsive button dimensions
//       double buttonPadding = isSmallDevice ? 12 : 16;
//       double buttonBorderRadius = isSmallDevice ? 8 : 10;
      
//       return Dialog(
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(dialogBorderRadius),
//         ),
//         child: Container(
//           width: dialogWidth,
//           padding: EdgeInsets.all(dialogPadding),
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               // Title
//               Text(
//                 'Enter OTP sent to Phone',
//                 style: TextStyle(
//                   fontSize: titleFontSize,
//                   fontWeight: FontWeight.bold,
//                   color: const Color.fromRGBO(0, 131, 143, 1),
//                 ),
//               ),
//               SizedBox(height: contentSpacing),
              
//               // OTP Input Field
//               Container(
//                 height: inputHeight,
//                 child: TextFormField(
//                   maxLength: 6,
//                   keyboardType: TextInputType.number,
//                   controller: _otpController,
//                   style: TextStyle(
//                     fontSize: inputFontSize,
//                     fontWeight: FontWeight.w500,
//                   ),
//                   decoration: InputDecoration(
//                     labelText: '6-digit OTP',
//                     labelStyle: TextStyle(
//                       fontSize: labelFontSize,
//                       color: Colors.grey[600],
//                     ),
//                     border: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(buttonBorderRadius),
//                       borderSide: BorderSide(
//                         color: Colors.grey[300]!,
//                         width: 1.5,
//                       ),
//                     ),
//                     focusedBorder: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(buttonBorderRadius),
//                       borderSide: const BorderSide(
//                         color: Color.fromRGBO(0, 131, 143, 1),
//                         width: 2.0,
//                       ),
//                     ),
//                     enabledBorder: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(buttonBorderRadius),
//                       borderSide: BorderSide(
//                         color: Colors.grey[300]!,
//                         width: 1.5,
//                       ),
//                     ),
//                     contentPadding: EdgeInsets.symmetric(
//                       horizontal: buttonPadding,
//                       vertical: buttonPadding * 0.75,
//                     ),
//                     counterText: '', // Hide character counter
//                   ),
//                 ),
//               ),
//               SizedBox(height: buttonSpacing),
              
//               // Buttons Row
//               Row(
//                 children: [
//                   // Resend Button
//                   Expanded(
//                     flex: isSmallDevice ? 1 : 2,
//                     child: SizedBox(
//                       height: buttonHeight,
//                       child: TimerButton(
//                         onPressed: () async {
//                           try {
//                             String mobile = _phoneController.text;
//                             var request = http.MultipartRequest(
//                               'POST',
//                               Uri.parse(
//                                   '$baseUrl/api/v1/auth/$mobile/mobileRegister'),
//                             );

//                             var response = await request
//                                 .send()
//                                 .timeout(const Duration(seconds: 20));

//                             var responseBody =
//                                 await response.stream.bytesToString();

//                             if (response.statusCode == 200) {
//                               successToast(msg: 'OTP Resent Successfully');
//                             } else {
//                               errorToast(
//                                   msg: '${jsonDecode(responseBody)['message']} ');
//                             }
//                           } on TimeoutException {
//                             errorToast(msg: 'Request Timeout');
//                             isLoading.value = false;
//                           } on http.ClientException {
//                             errorToast(msg: 'Client Error');
//                             isLoading.value = false;
//                           } catch (err) {
//                             errorToast(msg: 'Unexpected error: ${err.toString()}');
//                             isLoading.value = false;
//                           }
//                         },
//                         label: 'Resend',
//                         timeOutInSeconds: 120,
//                         color: const Color.fromARGB(255, 0, 131, 143),
//                         disabledColor: const Color.fromARGB(255, 0, 131, 143),
//                         disabledTextStyle: TextStyle(
//                           color: Colors.white,
//                           fontSize: buttonFontSize,
//                         ),
//                       ),
//                     ),
//                   ),
//                   SizedBox(width: buttonSpacing),
                  
//                   // Verify Button
//                   Expanded(
//                     flex: isSmallDevice ? 1 : 2,
//                     child: SizedBox(
//                       height: buttonHeight,
//                       child: ElevatedButton(
//                         onPressed: () async {
//                           try {
//                             String otp = _otpController.text;
//                             String mobile = _phoneController.text;
//                             var request = http.MultipartRequest(
//                               'POST',
//                               Uri.parse('$baseUrl/api/v1/auth/$mobile/verify-otp'),
//                             );

//                             request.fields.addAll({'otp': otp});

//                             var response = await request
//                                 .send()
//                                 .timeout(const Duration(seconds: 20));

//                             var responseBody =
//                                 await response.stream.bytesToString();

//                             if (response.statusCode == 200) {
//                               successToast(
//                                   msg: 'Phone Number Verified Successfully');
//                               Get.back();
//                               onOtpVerified();
//                             } else {
//                               errorToast(
//                                   msg: '${jsonDecode(responseBody)['message']} ');
//                             }
//                           } on TimeoutException {
//                             errorToast(msg: 'Request Timeout');
//                             isLoading.value = false;
//                           } on http.ClientException {
//                             errorToast(msg: 'Client Error');
//                             isLoading.value = false;
//                           } catch (err) {
//                             errorToast(msg: 'Unexpected error: ${err.toString()}');
//                             isLoading.value = false;
//                           }
//                         },
//                         style: ElevatedButton.styleFrom(
//                           backgroundColor: const Color.fromRGBO(0, 131, 143, 1),
//                           foregroundColor: Colors.white,
//                           padding: EdgeInsets.symmetric(
//                             horizontal: buttonPadding,
//                             vertical: buttonPadding * 0.5,
//                           ),
//                           shape: RoundedRectangleBorder(
//                             borderRadius: BorderRadius.circular(buttonBorderRadius),
//                           ),
//                           elevation: isSmallDevice ? 2 : 3,
//                         ),
//                         child: Text(
//                           'Verify OTP',
//                           style: TextStyle(
//                             fontSize: buttonFontSize,
//                             fontWeight: FontWeight.w600,
//                           ),
//                         ),
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ],
//           ),
//         ),
//       );
//     },
//   );
// }
//   //////////////////////////////for documents upload/////////////////////////////
//   File? citizenshipFront;
//   File? citizenshipBack;
//   File? drivingLicense;
//   File? nationalId;

//   final List<File?> _pickedImages = [];
//   Future<void> _pickImage(ImageSource source, String documentType) async {
//     final picker = ImagePicker();

//     try {
//       if (documentType == 'citizenship') {
//         final selectedSide = await _showCitizenshipSideDialog();
//         if (selectedSide == null) {
//           return;
//         }
//         if (selectedSide == 'front') {
//           documentType = 'citizenship_front';
//           final selectedSource =
//               await _showImageSourceDialog("Select Front Side ");
//           if (selectedSource == null) {
//             return;
//           }
//           source = selectedSource;
//         } else {
//           documentType = 'citizenship_back';
//           final selectedSource =
//               await _showImageSourceDialog("Select Back Side ");
//           if (selectedSource == null) {
//             return;
//           }
//           source = selectedSource;
//         }
//       } else if (documentType == 'driving_license' ||
//           documentType == 'national_id') {
//         final selectedSource = await _showImageSourceDialog("Select Image");
//         if (selectedSource == null) {
//           return;
//         }
//         source = selectedSource;
//       }

//       final pickedFile = await picker.pickImage(source: source);

//       if (pickedFile != null) {
//         final File pickedImage = File(pickedFile.path);
//         final File? compressedImage = await compressImage(pickedImage);

//         if (compressedImage != null) {
//           setState(() {
//             switch (documentType) {
//               case 'citizenship_front':
//                 citizenshipFront = compressedImage;
//                 documentController.text = "Citizenship Front Selected";
//                 count = 1;
//                 break;
//               case 'citizenship_back':
//                 citizenshipBack = compressedImage;
//                 documentController.text = "Citizenship  Selected";
//                 count = 1;
//                 break;
//               case 'driving_license':
//                 drivingLicense = compressedImage;
//                 documentController.text = "Driving License  Selected";
//                 count = 2;
//                 break;
//               case 'national_id':
//                 nationalId = compressedImage;
//                 documentController.text = "National Id  Selected";
//                 count = 3;

//                 break;
//               default:
//                 break;
//             }

//             final side = documentType.split('_').last;
//             final message = 'Successfully Selected $side side';
//             ScaffoldMessenger.of(context).showSnackBar(
//               SnackBar(content: Text(message)),
//             );

//             Future.delayed(const Duration(seconds: 1), () {
//               if (documentType == 'citizenship_front') {
//                 _pickImage(source, 'citizenship');
//               }
//             });
//           });
//           consolelog('Selected Document Type: $documentType');
//         } else {
//           consolelog('Image compression failed');
//         }
//       } else {
//         consolelog('No image selected');
//       }
//     } catch (error) {
//       consolelog('Error picking image: $error');
//     }
//   }

//   Future<ImageSource?> _showImageSourceDialog(String title) async {
//     return await showDialog<ImageSource>(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           title: Text(title),
//           actions: [
//             ListTile(
//               leading: const Icon(Icons.camera),
//               title: const Text('Take a Picture'),
//               onTap: () {
//                 Navigator.pop(context, ImageSource.camera);
//               },
//             ),
//             ListTile(
//               leading: const Icon(Icons.photo),
//               title: const Text('Choose from Gallery'),
//               onTap: () {
//                 Navigator.pop(context, ImageSource.gallery);
//               },
//             ),
//           ],
//         );
//       },
//     );
//   }

//   Future<String?> _showCitizenshipSideDialog() async {
//     bool isBackEnabled = citizenshipFront != null;

//     return await showDialog<String>(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           title: const Text('Select Citizenship Side'),
//           actions: [
//             ListTile(
//               title: const Text('Front'),
//               enabled: citizenshipFront == null,
//               onTap: () {
//                 if (citizenshipFront != null) {
//                   errorToast(msg: "Already Selected (Front)");
//                 } else {
//                   Navigator.pop(context, 'front');
//                 }
//               },
//             ),
//             ListTile(
//               title: const Text('Back'),
//               enabled: isBackEnabled,
//               onTap: () async {
//                 if (isBackEnabled) {
//                   final selectedSource =
//                       await _showImageSourceDialog("Select Back Side ");
//                   if (selectedSource != null) {
//                     Navigator.pop(context, 'back');
//                   }
//                 }
//               },
//             ),
//           ],
//         );
//       },
//     );
//   }

//   /////////////////////////////////    registering user   //////////////////////////////////////////////////////////////////
//   bool validateUserForm({
//     required String fullName,
//     required String mobile,
//     required String address,
//     required String password,
//     required String otp,
//   }) {
//     if (fullName.isEmpty ||
//         mobile.isEmpty ||
//         address.isEmpty ||
//         password.isEmpty ||
//         otp.isEmpty) {
//       return false;
//     }
//     return true;
//   }

//   Future<void> registerUser({
//     required String fullName,
//     required String mobileNumber,
//     required String address,
//     required String password,
//     required bool workStatus,
//     required bool approval,
//   }) async {
//     try {
//       final Map<String, dynamic> requestData = {
//         "fullName": fullName,
//         "mobileNumber": mobileNumber,
//         "address": address,
//         "password": password,
//         "workStatus": workStatus,
//         "approval": approval,
//       };
//       final String jsonData = jsonEncode(requestData);

//       consolelog('User Request Data: $jsonData');

//       final response = await http
//           .post(
//             Uri.parse('$baseUrl/api/v1/auth/register'),
//             headers: <String, String>{
//               'Content-Type': 'application/json',
//             },
//             body: jsonData,
//           )
//           .timeout(const Duration(seconds: 20));

//       consolelog(response.statusCode);
//       isLoading.value = false;

//       if (response.statusCode == 201) {
//         successToast(msg: "Registered Successfully");
//         Get.offAll(() => const LoginScreen());
//       } else {
//         // Try to parse the error message safely
//         try {
//           final errorData = jsonDecode(response.body);
//           errorToast(msg: 'Error registering: ${errorData['message'] ?? response.body}');
//         } catch (e) {
//           errorToast(msg: 'Error registering: ${response.body}');
//         }
//       }
//     } on TimeoutException {
//       errorToast(msg: 'Request Timeout');
//       isLoading.value = false;
//     } on http.ClientException {
//       errorToast(msg: 'Client Error');
//       isLoading.value = false;
//     } catch (err) {
//       // Handle the format exception more gracefully
//       if (err is FormatException) {
//         errorToast(msg: 'Error parsing response');
//       } else {
//         errorToast(msg: 'Unexpected error');
//       }
//       consolelog('Error details: ${err.toString()}');
//       isLoading.value = false;
//     }
//   }

//   ////////////////////////// validating service provider /////////////////
//   bool validateServiceProviderForm({
//     required String fullName,
//     required String mobileNumber,
//     required String address,
//     required String password,
//     required String jobDetails,
//     required String latitude,
//     required String otp,
//   }) {
//     if (fullName.isEmpty ||
//         mobileNumber.isEmpty ||
//         address.isEmpty ||
//         password.isEmpty ||
//         jobDetails.isEmpty ||
//         latitude.isEmpty ||
//         otp.isEmpty) {
//       return false;
//     }
//     return true;
//   }

//   Future<void> registerService({
//     required String fullName,
//     required String mobileNumber,
//     required String address,
//     required String password,
//     required Map<String, dynamic> jobDetails,
//     required String latitude,
//     required bool workStatus,
//     required bool approval,
//     File? citizenshipFront,
//     File? citizenshipBack,
//     File? drivingLicense,
//     File? nationalId,
//   }) async {
//     try {
//       // Upload images and get URLs
//       if (citizenshipFront != null && citizenshipBack != null) {
//         if (citizenshipFrontUrl == null && citizenshipBackUrl == null) {
//           var result = await uploadImage('citizenshipFront', citizenshipFront);
//           citizenshipFrontUrl = result['citizenshipFront'];

//           var result1 = await uploadImage('citizenshipBack', citizenshipBack);
//           citizenshipBackUrl = result1['citizenshipBack'];
//         }
//       } else {
//         if (count == 1) {
//           errorToast(msg: 'Select both sides of citizenship');
//         }
//       }

//       if (drivingLicense != null) {
//         if (drivingLicenseUrl == null) {
//           var result = await uploadImage('drivingLicense', drivingLicense);
//           drivingLicenseUrl = result['drivingLicense'];
//           citizenshipFrontUrl = drivingLicenseUrl;
//         }
//       } else {
//         if (count == 2) {
//           errorToast(msg: 'Driving License not selected');
//         }
//       }

//       if (nationalId != null) {
//         if (nationalIdUrl == null) {
//           var result = await uploadImage('nationalId', nationalId);
//           nationalIdUrl = result['nationalId'];
//           citizenshipFrontUrl = nationalIdUrl;
//         }
//       } else {
//         if (count == 3) {
//           errorToast(msg: 'National Id not selected');
//         }
//       }

//       if (count == 0) {
//         errorToast(msg: 'Please select any one of the documents');
//       }

//       if (selectedJobCategoryId == null || selectedServiceIds.isEmpty) {
//         errorToast(msg: 'Please select your skills and sub-skills');
//         return;
//       }

//       final Map<String, dynamic> requestData = {
//         "fullName": fullName,
//         "mobileNumber": mobileNumber,
//         "address": address,
//         "password": password,
//         "jobDetails": {
//           "jobCategoryId": int.parse(selectedJobCategoryId!),
//           "serviceIds": selectedServiceIds.map(int.parse).toList(),
//         },
//         "latitude": latitude,
//         "workStatus": workStatus,
//         "approval": approval,
//         "imageUrlCitizenshipFront": citizenshipFrontUrl,
//         "imageUrlCitizenshipBack": citizenshipBackUrl,
//       };
//       final String jsonData = jsonEncode(requestData);

//       consolelog('Register Service Request Data: $jsonData');

//       if (drivingLicenseUrl != null ||
//           nationalIdUrl != null ||
//           (citizenshipBackUrl != null && citizenshipFrontUrl != null)) {
//         try {
//           final response = await http
//               .post(
//                 Uri.parse('$baseUrl/api/v1/auth/register'),
//                 headers: <String, String>{
//                   'Content-Type': 'application/json',
//                 },
//                 body: jsonData,
//               )
//               .timeout(const Duration(seconds: 20));

//           consolelog(response.statusCode);

//           if (response.statusCode == 201) {
//             successToast(msg: "Registered Successfully");
//             Get.offAll(() => const LoginScreen());
//             try {
//               final responseData = jsonDecode(response.body);
//               id = responseData['id'] ?? 0;
//             } catch (e) {
//               consolelog("Error parsing ID: $e");
//             }
//             consolelog(id);
//           } else {
//             count = 5;
//             try {
//               final errorData = jsonDecode(response.body);
//               errorToast(msg: 'Error registering: ${errorData['message'] ?? response.body}');
//             } catch (e) {
//               errorToast(msg: 'Error registering: ${response.body}');
//             }
//           }
//         } catch (err) {
//           if (err is FormatException) {
//             errorToast(msg: 'Error parsing response');
//           } else {
//             errorToast(msg: 'Unexpected error: ${err.toString()}');
//           }
//           consolelog('Error details: ${err.toString()}');
//           isLoading.value = false;
//         }
//       } else {
//         count1 = 5;
//       }
//     } on TimeoutException {
//       errorToast(msg: 'Request Timeout');
//       isLoading.value = false;
//     } on http.ClientException {
//       errorToast(msg: 'Client Error');
//       isLoading.value = false;
//     } catch (err) {
//       errorToast(msg: 'Unexpected error: ${err.toString()}');
//       isLoading.value = false;
//     }
//   }

// //districts , municipalities and ward
//   List<String> districts = ['Kathmandu', 'Bhaktapur', 'Lalitpur'];
//   Map<String, List<String>> municipalities = {
//     'Kathmandu': [
//       'Budhanilkantha',
//       'Chandragiri',
//       'Dakshinkali',
//       'Gokarneshwar',
//       'Kathmandu Metropolitan',
//       'Kageshwori Manohara',
//       'Kirtipur',
//       'Nagarjun',
//       'Shankharapur',
//       'Tarakeshwar',
//       'Tokha'
//     ],
//     'Bhaktapur': [
//       'Anantalingeshwar',
//       'Bhaktapur',
//       'Changunarayan',
//       'MadhyapurThimi',
//       'Nagarkot',
//       'Suryavinayak'
//     ],
//     'Lalitpur': [
//       'Bagmati',
//       'Godawari',
//       'Konjyosom',
//       'Lalitpur Metropolitan',
//       'Mahalaxmi',
//       'Mahankal'
//     ],
//   };
//   Map<String, List<String>> wards = {
//     'Budhanilkantha': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11',
//       'Ward 12',
//       'Ward 13'
//     ],
//     'Chandragiri': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11',
//       'Ward 12',
//       'Ward 13',
//       'Ward 14',
//       'Ward 15'
//     ],
//     'Dakshinkali': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9'
//     ],
//     'Gokarneshwar': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9'
//     ],
//     'Kathmandu Metropolitan': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11',
//       'Ward 12',
//       'Ward 13',
//       'Ward 14',
//       'Ward 15',
//       'Ward 17',
//       'Ward 18',
//       'Ward 19',
//       'Ward 20',
//       'Ward 21',
//       'Ward 22',
//       'Ward 23',
//       'Ward 24',
//       'Ward 25',
//       'Ward 26',
//       'Ward 27',
//       'Ward 28',
//       'Ward 29',
//       'Ward 30',
//       'Ward 31',
//       'Ward 32'
//     ],
//     'Kageshwori Manohara': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9'
//     ],
//     'Kirtipur': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10'
//     ],
//     'Nagarjun': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10'
//     ],
//     'Tokha': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11'
//     ],
//     'Shankharapur': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9'
//     ],
//     'Tarakeshwar': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11'
//     ],
//     'Bhaktapur': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10'
//     ],
//     'Anantalingeshwar': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11',
//       'Ward 12',
//       'Ward 13',
//       'Ward 14',
//       'Ward 15'
//     ],
//     'MadhyapurThimi': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9'
//     ],
//     'Suryavinayak': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11',
//       'Ward 12'
//     ],
//     'Nagarkot': ['Ward 1', 'Ward 2', 'Ward 3'],
//     'Changunarayan': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9'
//     ],
//     'Lalitpur Metropolitan': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11',
//       'Ward 12',
//       'Ward 13',
//       'Ward 14',
//       'Ward 15',
//       'Ward 17',
//       'Ward 18',
//       'Ward 19',
//       'Ward 20',
//       'Ward 21',
//       'Ward 22',
//       'Ward 23',
//       'Ward 24',
//       'Ward 25',
//       'Ward 26',
//       'Ward 27',
//       'Ward 28',
//       'Ward 29'
//     ],
//     'Mahalaxmi': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10'
//     ],
//     'Godawari': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11',
//       'Ward 12',
//       'Ward 13',
//       'Ward 14'
//     ],
//     'Konjyosom': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5'],
//     'Bagmati': [
//       'Ward 1',
//       'Ward 2',
//       'Ward 3',
//       'Ward 4',
//       'Ward 5',
//       'Ward 6',
//       'Ward 7',
//       'Ward 8',
//       'Ward 9',
//       'Ward 10',
//       'Ward 11',
//       'Ward 12'
//     ],
//     'Mahankal': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5', 'Ward 6'],
//   };


//   Widget buildMiniMap() {
//     // Parse the coordinates from the controller
//     LatLng? userLocation;
//     if (_workingaddressController.text.isNotEmpty) {
//       try {
//         List<String> coordinates = _workingaddressController.text.split(',');
//         if (coordinates.length == 2) {
//           double lat = double.parse(coordinates[0].trim());
//           double lng = double.parse(coordinates[1].trim());
//           userLocation = LatLng(lat, lng);
//         }
//       } catch (e) {
//         consolelog("Error parsing coordinates: $e");
//       }
//     }

//     return Container(
//       margin: const EdgeInsets.only(top: 8.0),
//       height: 180,
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(12.0),
//         border: Border.all(
//           color: const Color.fromRGBO(0, 131, 143, 0.5),
//           width: 1.5,
//         ),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.grey.withOpacity(0.2),
//             spreadRadius: 1,
//             blurRadius: 4,
//             offset: const Offset(0, 2),
//           ),
//         ],
//       ),
//       child: ClipRRect(
//         borderRadius: BorderRadius.circular(12.0),
//         child: Stack(
//           children: [
//             // If using Google Maps
//             userLocation != null
//                 ? GoogleMap(
//                     initialCameraPosition: CameraPosition(
//                       target:
//                           LatLng(userLocation.latitude, userLocation.longitude),
//                       zoom: 15,
//                     ),
//                     zoomControlsEnabled: false,
//                     mapToolbarEnabled: false,
//                     myLocationEnabled: true,
//                     myLocationButtonEnabled: false,
//                     markers: {
//                       Marker(
//                         markerId: const MarkerId('userLocation'),
//                         position: LatLng(
//                             userLocation.latitude, userLocation.longitude),
//                         infoWindow:
//                             const InfoWindow(title: 'Your Working Location'),
//                       ),
//                     },
//                     onMapCreated: (GoogleMapController controller) {
//                       // Store controller if needed
//                     },
//                   )
//                 : const Center(
//                     child: Text(
//                       'No location selected',
//                       style: TextStyle(color: Colors.grey),
//                     ),
//                   ),

//             // Coordinates display
//             if (userLocation != null)
//               Positioned(
//                 bottom: 8,
//                 left: 8,
//                 right: 8,
//                 child: Container(
//                   padding:
//                       const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
//                   decoration: BoxDecoration(
//                     color: Colors.white.withOpacity(0.9),
//                     borderRadius: BorderRadius.circular(8),
//                     boxShadow: [
//                       BoxShadow(
//                         color: Colors.black.withOpacity(0.1),
//                         blurRadius: 2,
//                         offset: const Offset(0, 1),
//                       ),
//                     ],
//                   ),
//                   child: Text(
//                     'Location: ${userLocation.latitude.toStringAsFixed(6)}, ${userLocation.longitude.toStringAsFixed(6)}',
//                     style: const TextStyle(
//                       fontSize: 12,
//                       fontWeight: FontWeight.w500,
//                       color: Color.fromRGBO(0, 131, 143, 1),
//                     ),
//                     textAlign: TextAlign.center,
//                   ),
//                 ),
//               ),

//             // Expand button
//             Positioned(
//               top: 8,
//               right: 8,
//               child: Container(
//                 decoration: BoxDecoration(
//                   color: Colors.white,
//                   borderRadius: BorderRadius.circular(8.0),
//                   boxShadow: [
//                     BoxShadow(
//                       color: Colors.black.withOpacity(0.1),
//                       spreadRadius: 1,
//                       blurRadius: 2,
//                       offset: const Offset(0, 1),
//                     ),
//                   ],
//                 ),
//                 child: IconButton(
//                   icon: const Icon(
//                     Icons.fullscreen,
//                     color: Color.fromRGBO(0, 131, 143, 1),
//                   ),
//                   onPressed: () async {
//                     permission = await Geolocator.requestPermission();
//                     if (permission == LocationPermission.deniedForever) {
//                       await Geolocator.openAppSettings();
//                     } else if (permission != LocationPermission.denied &&
//                         permission != LocationPermission.deniedForever) {
//                       widget.mapController.isMapLoading.value = true;
//                       position = await Geolocator.getCurrentPosition(
//                           desiredAccuracy: LocationAccuracy.high);

//                       LatLng selectedLocation =
//                           await Get.to(() => OpenMapScreen(
//                                         completeGoogleMapController:
//                                             completeGoogleMapController,
//                                         kGoogle: kGoogle,
//                                         marker: marker,
//                                         onpressed: onpressed,
//                                         onLocationSelected: (LatLng location) {
//                                           String formattedLocation =
//                                               '${location.latitude},${location.longitude}';
//                                           _workingaddressController.text =
//                                               formattedLocation;
//                                         },
//                                         initialOnpressed: false,
//                                       ));
//                     }
//                   },
//                   tooltip: 'Expand Map',
//                 ),
//               ),
//             ),

//             // Select location button
//             Positioned(
//               top: 8,
//               left: 8,
//               child: Container(
//                 decoration: BoxDecoration(
//                   color: Colors.white,
//                   borderRadius: BorderRadius.circular(8.0),
//                   boxShadow: [
//                     BoxShadow(
//                       color: Colors.black.withOpacity(0.1),
//                       spreadRadius: 1,
//                       blurRadius: 2,
//                       offset: const Offset(0, 1),
//                     ),
//                   ],
//                 ),
//                 child: IconButton(
//                   icon: const Icon(
//                     Icons.add_location_alt,
//                     color: Color.fromRGBO(0, 131, 143, 1),
//                   ),
//                   onPressed: () async {
//                     permission = await Geolocator.requestPermission();
//                     if (permission == LocationPermission.deniedForever) {
//                       await Geolocator.openAppSettings();
//                     } else if (permission != LocationPermission.denied &&
//                         permission != LocationPermission.deniedForever) {
//                       widget.mapController.isMapLoading.value = true;
//                       position = await Geolocator.getCurrentPosition(
//                           desiredAccuracy: LocationAccuracy.high);

//                       LatLng selectedLocation =
//                           await Get.to(() => OpenMapScreen(
//                                 completeGoogleMapController:
//                                     completeGoogleMapController,
//                                 kGoogle: kGoogle,
//                                 marker: marker,
//                                 onpressed: onpressed,
//                                 onLocationSelected: (LatLng location) {
//                                   String formattedLocation =
//                                       '${location.latitude},${location.longitude}';
//                                   _workingaddressController.text =
//                                       formattedLocation;
//                                 }, initialOnpressed: false,
//                               ));
//                     }
//                   },
//                   tooltip: 'Select Location',
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget buildWorkingAddressSection(Size size) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             const Text(
//               'Working Address',
//               style: TextStyle(
//                 color: Color.fromRGBO(0, 131, 143, 1),
//                 fontWeight: FontWeight.bold,
//                 fontSize: 16.0,
//               ),
//             ),
//             // Open Map button - always visible
//             ElevatedButton.icon(
//               onPressed: () async {
//                 permission = await Geolocator.requestPermission();
//                 if (permission == LocationPermission.deniedForever) {
//                   await Geolocator.openAppSettings();
//                 } else if (permission != LocationPermission.denied &&
//                     permission != LocationPermission.deniedForever) {
//                   widget.mapController.isMapLoading.value = true;
//                   position = await Geolocator.getCurrentPosition(
//                       desiredAccuracy: LocationAccuracy.high);

//                   await Get.to(() => OpenMapScreen(
//                         completeGoogleMapController:
//                             completeGoogleMapController,
//                         kGoogle: kGoogle,
//                         marker: marker,
//                         onpressed: onpressed,
//                         onLocationSelected: (LatLng location) {
//                           String formattedLocation =
//                               '${location.latitude},${location.longitude}';
//                           _workingaddressController.text = formattedLocation;
//                           setState(() {}); // Refresh UI to show map
//                         }, initialOnpressed: false,
//                       ));
//                 }
//               },
//               icon: const Icon(Icons.map, color: Colors.white, size: 16),
//               label: const Text('Open Map'),
//               style: ElevatedButton.styleFrom(
//                 backgroundColor: const Color.fromRGBO(0, 131, 143, 1),
//                 shape: RoundedRectangleBorder(
//                   borderRadius: BorderRadius.circular(8.0),
//                 ),
//                 minimumSize: const Size(100, 36),
//               ),
//             ),
//           ],
//         ),
//         const SizedBox(height: 8.0),

//         // Hidden field to store coordinates
//         Opacity(
//           opacity: 0,
//           child: Container(
//             height: 0,
//             child: TextFormField(
//               controller: _workingaddressController,
//               readOnly: true,
//             ),
//           ),
//         ),

//         // Show mini map or placeholder
//         _workingaddressController.text.isNotEmpty
//             ? buildMiniMap()
//             : Container(
//                 width: double.infinity,
//                 height: 120,
//                 decoration: BoxDecoration(
//                   color: Colors.grey.shade100,
//                   borderRadius: BorderRadius.circular(12.0),
//                   border: Border.all(
//                     color: Colors.grey.shade400,
//                     width: 1.0,
//                   ),
//                 ),
//                 child: const Center(
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       Icon(
//                         Icons.location_on,
//                         color: Color.fromRGBO(0, 131, 143, 1),
//                         size: 36,
//                       ),
//                       SizedBox(height: 8),
//                       Text(
//                         'No location selected',
//                         style: TextStyle(
//                           color: Colors.grey,
//                           fontSize: 14,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//       ],
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     Size size = MediaQuery.of(context).size;
//     return Scaffold(
//       appBar: myAppbar(context, true, "Register"),
//       body: SingleChildScrollView(
//         child: Padding(
//           // Use Padding or SafeArea to avoid overflow
//           padding: const EdgeInsets.symmetric(vertical: 20.0),
//           child: Container(
//             // Remove fixed height here
//             decoration: const BoxDecoration(
//               gradient: LinearGradient(
//                 colors: [
//                   Color.fromARGB(255, 255, 255, 255),
//                   Color.fromARGB(255, 255, 255, 255),
//                 ],
//                 begin: Alignment.topCenter,
//                 end: Alignment.bottomCenter,
//               ),
//             ),
//             child: Center(
//               child: Container(
//                 width: size.width * 0.97,
//                 padding: const EdgeInsets.all(20.0),
//                 decoration: BoxDecoration(
//                   color: Colors.white, // Set background color
//                   borderRadius: BorderRadius.circular(16.0), // Rounded corners
//                   boxShadow: const [
//                     BoxShadow(
//                       color: Colors.black26,
//                       blurRadius: 8.0,
//                       spreadRadius: 2.0,
//                     ),
//                   ],
//                 ),
//                 child: Form(
//                   key: _formkey,
//                   onChanged: () {
//                     setState(() {
//                       isFormFilled = _fullNameController.text.isNotEmpty &&
//                           _phoneController.text.isNotEmpty &&
//                           _addressController.text.isNotEmpty &&
//                           _passwordController.text.isNotEmpty &&
//                           _confirmPasswordController.text.isNotEmpty &&
//                           _otpController.text.isNotEmpty &&
//                           _skillController.text.isNotEmpty &&
//                           _workingaddressController.text.isNotEmpty &&
//                           isTermsChecked;
//                     });
//                   },
//                   child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.center,
//                       children: [
//                         SizedBox(height: size.height * 0.02),
//                         TextFormField(
//                         controller: _fullNameController,
//                         keyboardType: TextInputType.name,
//                         textCapitalization: TextCapitalization.words,
//                         inputFormatters: [
//                           FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z\s]')), // Only letters and spaces
//                         ],
//                         decoration: const InputDecoration(
//                           labelText: 'Full Name',
//                           labelStyle: TextStyle(
//                             color: Color.fromRGBO(0, 131, 143, 1)
//                           ),
//                           focusedBorder: OutlineInputBorder(
//                             borderSide: BorderSide(
//                               color: Color.fromRGBO(0, 131, 143, 1)
//                             ),
//                           ),
//                           prefixIcon: Icon(Icons.person, color: Color(0xFF007F83)),
//                         ),
//                         autovalidateMode: AutovalidateMode.onUserInteraction,
//                         validator: (value) {
//                           if (value == null || value.isEmpty) {
//                             return 'Please enter your full name';
//                           }
//                           if (value.trim().length < 2) {
//                             return 'Name must be at least 2 characters long';
//                           }
//                           if (!RegExp(r'^[a-zA-Z\s]+$').hasMatch(value)) {
//                             return 'Name can only contain letters and spaces';
//                           }
//                           return null;
//                         },
//                       ),
//                         SizedBox(height: size.height * 0.02),
//                         TextFormField(
//                           controller: _phoneController,
//                           decoration: const InputDecoration(
//                             labelText: 'Phone',
//                             labelStyle: TextStyle(
//                                 color: Color.fromRGBO(0, 131, 143, 1)),
//                             focusedBorder: OutlineInputBorder(
//                               borderSide: BorderSide(
//                                   color: Color.fromRGBO(0, 131, 143, 1)),
//                             ),
//                             prefixIcon: Icon(
//                               Icons.phone,
//                               color: Color.fromRGBO(0, 131, 143, 1),
//                             ),
//                           ),
//                           keyboardType: TextInputType.phone,
//                           autovalidateMode: AutovalidateMode.onUserInteraction,
//                           inputFormatters: [
//                             FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
//                             LengthLimitingTextInputFormatter(10),
//                           ],
//                           validator: (value) {
//                             if (value == null || value.isEmpty) {
//                               return 'Please enter your phone number';
//                             }
//                             if (value.length != 10) {
//                               return 'Phone number must be exactly 10 digits';
//                             }
//                             if (!RegExp(r'^(98|97|96)\d{8}$').hasMatch(value)) {
//                               return 'Phone number must start with 98, 97, or 96';
//                             }
//                             return null;
//                           },
//                         ),
//                         SizedBox(height: size.height * 0.02),
//                         buildAddressDropdown(),
//                         SizedBox(height: size.height * 0.02),
//                         TextFormField(
//                           controller: _passwordController,
//                           decoration: InputDecoration(
//                             labelText: 'Password',
//                             labelStyle: const TextStyle(
//                               color: Color.fromRGBO(0, 131, 143, 1),
//                             ),
//                             focusedBorder: const OutlineInputBorder(
//                               borderSide: BorderSide(
//                                 color: Color.fromRGBO(0, 131, 143, 1),
//                               ),
//                             ),
//                             prefixIcon: const Icon(
//                               Icons.lock,
//                               color: Color.fromRGBO(0, 131, 143, 1),
//                             ),
//                             suffixIcon: IconButton(
//                               icon: Icon(
//                                 _passwordVisible
//                                     ? Icons.visibility
//                                     : Icons.visibility_off,
//                                 color: const Color.fromRGBO(0, 131, 143, 1),
//                               ),
//                               onPressed: () {
//                                 setState(() {
//                                   _passwordVisible = !_passwordVisible;
//                                 });
//                               },
//                             ),
//                           ),
//                           obscureText: !_passwordVisible,
//                           autovalidateMode: AutovalidateMode.onUserInteraction,
//                           validator: (value) {
//                             if (value == null || value.isEmpty) {
//                               return 'Please enter a password';
//                             } else if (value.length < 8) {
//                               return 'Password must be at least 8 characters long';
//                             }
//                             return null;
//                           },
//                         ),
//                         SizedBox(height: size.height * 0.02),
//                         TextFormField(
//                           controller: _confirmPasswordController,
//                           decoration: InputDecoration(
//                             labelText: 'Confirm Password',
//                             labelStyle: const TextStyle(
//                               color: Color.fromRGBO(0, 131, 143, 1),
//                             ),
//                             focusedBorder: const OutlineInputBorder(
//                               borderSide: BorderSide(
//                                 color: Color.fromRGBO(0, 131, 143, 1),
//                               ),
//                             ),
//                             prefixIcon: const Icon(
//                               Icons.lock,
//                               color: Color.fromRGBO(0, 131, 143, 1),
//                             ),
//                             suffixIcon: IconButton(
//                               icon: Icon(
//                                 _confirmPasswordVisible
//                                     ? Icons.visibility
//                                     : Icons.visibility_off,
//                                 color: const Color.fromRGBO(0, 131, 143, 1),
//                               ),
//                               onPressed: () {
//                                 setState(() {
//                                   _confirmPasswordVisible =
//                                       !_confirmPasswordVisible;
//                                 });
//                               },
//                             ),
//                           ),
//                           obscureText: !_confirmPasswordVisible,
//                           autovalidateMode: AutovalidateMode.onUserInteraction,
//                           validator: (value) {
//                             if (value == null || value.isEmpty) {
//                               return 'Please confirm your password';
//                             } else if (value != _passwordController.text) {
//                               return 'Passwords do not match';
//                             }
//                             return null;
//                           },
//                         ),
//                         SizedBox(height: size.height * 0.02),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             Expanded(
//                               child: TextFormField(
//                                 controller: _otpController,
//                                 decoration: const InputDecoration(
//                                   labelText: 'OTP',
//                                   labelStyle: TextStyle(
//                                       color: Color.fromRGBO(0, 131, 143, 1)),
//                                   focusedBorder: OutlineInputBorder(
//                                     borderSide: BorderSide(
//                                         color: Color.fromRGBO(0, 131, 143, 1)),
//                                   ),
//                                   prefixIcon: Icon(Icons.lock_clock,
//                                       color: Color.fromRGBO(0, 131, 143, 1)),
//                                 ),
//                                 autovalidateMode:
//                                     AutovalidateMode.onUserInteraction,
//                                 validator: (value) {
//                                   if (value == null || value.isEmpty) {
//                                     return 'Please enter the OTP';
//                                   }
//                                   return null;
//                                 },
//                               ),
//                             ),
//                             const SizedBox(width: 16.0),
//                             ElevatedButton(
//                               onPressed: () async {
//                                 String mobile = _phoneController.text;
//                                 if (mobile.isNotEmpty) {
//                                   showLoadingDialog(context);
//                                   var request = http.Request(
//                                       'POST',
//                                       Uri.parse(
//                                           '$baseUrl/api/v1/auth/$mobile/mobileRegister'));

//                                   var response = await request.send();
//                                   var responseBody =
//                                       await response.stream.bytesToString();
//                                   Navigator.of(context, rootNavigator: true)
//                                       .pop();

//                                   if (response.statusCode == 200) {
//                                     showOtpInputDialog(context, () {
//                                       _otpController.text =
//                                           'OTP Verified Successfully';
//                                     });
//                                   } else {
//                                     errorToast(
//                                         msg:
//                                             '${jsonDecode(responseBody)['message']} ');
//                                   }
//                                 } else {
//                                   errorToast(msg: "Please Enter Mobile Number");
//                                 }
//                               },
//                               style: ElevatedButton.styleFrom(
//                                 backgroundColor:
//                                     const Color.fromRGBO(0, 131, 143, 1),
//                                 minimumSize: const Size(80, 40),
//                               ),
//                               child: const Text(
//                                 'Get OTP',
//                                 style: TextStyle(color: Colors.white),
//                               ),
//                             ),
//                           ],
//                         ),
//                         SizedBox(height: size.height * 0.02),
//                         const Center(
//                           child: Text(
//                             'Register as:',
//                             style: TextStyle(
//                               color: Color.fromRGBO(0, 131, 143, 1),
//                               fontSize: 16.0,
//                               fontWeight: FontWeight.w500,
//                             ),
//                           ),
//                         ),
//                         const SizedBox(height: 16.0),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             OutlinedButton(
//                               onPressed: () {
//                                 setState(() {
//                                   isServiceProvider = false;
//                                 });
//                               },
//                               style: OutlinedButton.styleFrom(
//                                 side: const BorderSide(
//                                     color: Color.fromRGBO(0, 131, 143, 1)),
//                                 minimumSize:
//                                     Size(size.width * 0.3, size.height * 0.05),
//                                 elevation: 0.0,
//                                 shape: RoundedRectangleBorder(
//                                   borderRadius: BorderRadius.circular(32),
//                                 ),
//                                 backgroundColor: isServiceProvider
//                                     ? Colors.white
//                                     : const Color.fromRGBO(0, 131, 143, 1),
//                               ),
//                               child: Text(
//                                 'User',
//                                 style: TextStyle(
//                                   color: isServiceProvider
//                                       ? const Color.fromRGBO(0, 131, 143, 1)
//                                       : Colors.white,
//                                 ),
//                               ),
//                             ),
//                             SizedBox(width: size.width * 0.03),
//                             OutlinedButton(
//                               onPressed: () {
//                                 setState(() {
//                                   isServiceProvider = true;
//                                 });
//                               },
//                               style: OutlinedButton.styleFrom(
//                                 side: const BorderSide(
//                                     color: Color.fromRGBO(0, 131, 143, 1)),
//                                 minimumSize:
//                                     Size(size.width * 0.3, size.height * 0.05),
//                                 elevation: 0.0,
//                                 shape: RoundedRectangleBorder(
//                                   borderRadius: BorderRadius.circular(32),
//                                 ),
//                                 backgroundColor: isServiceProvider
//                                     ? const Color.fromRGBO(0, 131, 143, 1)
//                                     : Colors.white,
//                               ),
//                               child: Text(
//                                 'Service Provider',
//                                 style: TextStyle(
//                                   color: isServiceProvider
//                                       ? Colors.white
//                                       : const Color.fromRGBO(0, 131, 143, 1),
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                         if (isServiceProvider) ...[
//                           const SizedBox(height: 16.0),
//                           const SizedBox(height: 16.0),
//                           buildSkillsDropdown(),
//                           SizedBox(height: size.height * 0.02),
//                           // Row(
//                           //   children: [
//                           //     Expanded(
//                           //       child: TextFormField(
//                           //         controller: _workingaddressController,
//                           //         decoration: const InputDecoration(
//                           //           labelText: 'Select your working address',
//                           //           labelStyle: TextStyle(
//                           //               color: Color.fromRGBO(0, 131, 143, 1)),
//                           //           focusedBorder: OutlineInputBorder(
//                           //             borderSide: BorderSide(
//                           //                 color:
//                           //                     Color.fromRGBO(0, 131, 143, 1)),
//                           //           ),
//                           //         ),
//                           //         autovalidateMode:
//                           //             AutovalidateMode.onUserInteraction,
//                           //         validator: (value) {
//                           //           if (value == null || value.isEmpty) {
//                           //             return 'Please enter your working address';
//                           //           }
//                           //           return null;
//                           //         },
//                           //       ),
//                           //     ),
//                           //     const SizedBox(width: 16.0),
//                           //     ElevatedButton(
//                           //     onPressed: () async {
//                           //       permission = await Geolocator.requestPermission();
//                           //       if (permission == LocationPermission.deniedForever) {
//                           //         await Geolocator.openAppSettings();
//                           //       } else if (permission != LocationPermission.denied &&
//                           //           permission != LocationPermission.deniedForever) {
//                           //         widget.mapController.isMapLoading.value = true;
//                           //         position = await Geolocator.getCurrentPosition(
//                           //           desiredAccuracy: LocationAccuracy.high,
//                           //         );
//                           //         // ignore: unused_local_variable
//                           //         LatLng selectedLocation = await Get.to(() => OpenMapScreen(
//                           //           completeGoogleMapController: completeGoogleMapController,
//                           //           kGoogle: kGoogle,
//                           //           marker: marker,
//                           //           onpressed: onpressed,
//                           //           initialOnpressed: true,  // Add this missing parameter
//                           //           onLocationSelected: (LatLng location) {
//                           //             String formattedLocation = '${location.latitude},${location.longitude}';
//                           //             _workingaddressController.text = formattedLocation;
//                           //           },
//                           //         ));
//                           //       }
//                           //     },
//                           //     style: ElevatedButton.styleFrom(
//                           //       backgroundColor: const Color.fromRGBO(0, 131, 143, 1),
//                           //       minimumSize: const Size(80, 40),
//                           //     ),
//                           //     child: const Text(
//                           //       'Open Map',
//                           //       style: TextStyle(color: Colors.white),
//                           //     ),
//                           //   ),
//                           //   ],
//                           // ),
//                           buildWorkingAddressSection(size),
//                           SizedBox(height: size.height * 0.02),
//                           Row(
//                             children: [
//                               Expanded(
//                                 child: _pickedImages.isEmpty
//                                     ? TextFormField(
//                                         controller: documentController,
//                                         readOnly: true, // Make the field read-only
//                                         decoration: const InputDecoration(
//                                           labelText: 'Upload your documents',
//                                           labelStyle: TextStyle(
//                                               color: Color.fromRGBO(
//                                                   0, 131, 143, 1)),
//                                           focusedBorder: OutlineInputBorder(
//                                             borderSide: BorderSide(
//                                                 color: Color.fromRGBO(
//                                                     0, 131, 143, 1)),
//                                           ),
//                                         ),
//                                       )
//                                     : Container(),
//                               ),
//                               InkWell(
//                                 onTap: () {
//                                   count = 0;
//                                   count1 = 0;
//                                   citizenshipBack = null;
//                                   citizenshipBackUrl = null;
//                                   citizenshipFrontUrl = null;
//                                   drivingLicenseUrl = null;
//                                   nationalIdUrl = null;
//                                   citizenshipFront = null;
//                                   documentController.text = "";

//                                   drivingLicense = null;
//                                   nationalId = null;
//                                   showModalBottomSheet(
//                                     context: context,
//                                     builder: (BuildContext context) {
//                                       return Column(
//                                         mainAxisSize: MainAxisSize.min,
//                                         children: <Widget>[
//                                           ListTile(
//                                             title: const Text('Citizenship'),
//                                             onTap: () {
//                                               Navigator.pop(context);

//                                               _pickImage(ImageSource.gallery,
//                                                   'citizenship');
//                                             },
//                                           ),
//                                           ListTile(
//                                             title:
//                                                 const Text('Driving License'),
//                                             onTap: () {
//                                               Navigator.pop(context);
//                                               _pickImage(ImageSource.gallery,
//                                                   'driving_license');
//                                             },
//                                           ),
//                                           ListTile(
//                                             title: const Text('National ID'),
//                                             onTap: () {
//                                               Navigator.pop(context);
//                                               _pickImage(ImageSource.gallery,
//                                                   'national_id');
//                                             },
//                                           ),
//                                         ],
//                                       );
//                                     },
//                                   );
//                                 },
//                                 child: Icon(
//                                   Icons.upload_file,
//                                   color: Colors.teal[800],
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ] else
//                           ...[],
//                         SizedBox(height: size.height * 0.01),
//                         buildViewTermsAndConditions(context),
//                         buildTermsCheckbox(),
//                         AppButton(
//                           onPressed: () async {
//                             if (!agreedToTerms && !isFormFilled) {
//                               ScaffoldMessenger.of(context).showSnackBar(
//                                 const SnackBar(
//                                   content: Text(
//                                       'Please agree to the Terms and Conditions and fill all the required fields.'),
//                                 ),
//                               );
//                               return;
//                             }
//                             if (!isServiceProvider) {
//                               isLoading.value = false;

//                               workStatus = false;
//                               approval = false;

//                               if (!validateUserForm(
//                                 fullName: _fullNameController.text,
//                                 mobile: _phoneController.text,
//                                 address: _addressController.text,
//                                 password: _passwordController.text,
//                                 otp: _otpController.text,
//                               )) {
//                                 ScaffoldMessenger.of(context).showSnackBar(
//                                   const SnackBar(
//                                     content: Text(
//                                         'Please fill in all required fields.'),
//                                   ),
//                                 );
//                                 return;
//                               }
//                               registerUser(
//                                   fullName: _fullNameController.text,
//                                   mobileNumber: _phoneController.text,
//                                   address: _addressController.text,
//                                   password: _passwordController.text,
//                                   workStatus: workStatus,
//                                   approval: approval);
//                             } else {
//                               isLoading.value = true;

//                               workStatus = true;
//                               approval = true;
//                               if (!validateServiceProviderForm(
//                                 fullName: _fullNameController.text,
//                                 mobileNumber: _phoneController.text,
//                                 address: _addressController.text,
//                                 password: _passwordController.text,
//                                 jobDetails: _skillController.text,
//                                 latitude: _workingaddressController.text,
//                                 otp: _otpController.text,
//                               )) {
//                                 ScaffoldMessenger.of(context).showSnackBar(
//                                   const SnackBar(
//                                     content: Text(
//                                         'Please fill in all required fields.'),
//                                   ),
//                                 );
//                                 return;
//                               }
//                               await registerService(
//                                 fullName: _fullNameController.text,
//                                 mobileNumber: _phoneController.text,
//                                 address: _addressController.text,
//                                 password: _passwordController.text,
//                                 jobDetails: {
//                                   "jobCategoryId":
//                                       int.parse(selectedJobCategoryId!),
//                                   "serviceIds": selectedServiceIds
//                                       .map(int.parse)
//                                       .toList(),
//                                 },
//                                 latitude: _workingaddressController.text,
//                                 workStatus: workStatus,
//                                 approval: approval,
//                                 citizenshipFront: citizenshipFront,
//                                 citizenshipBack: citizenshipBack,
//                                 drivingLicense: drivingLicense,
//                                 nationalId: nationalId,
//                               );
//                             }
//                             if (count1 != 5 && count1 != 6 && count == 0) {
// //Payment screen call
// //Id from resgitered User

//                               Navigator.pushReplacement(
//                                 context,
//                                 MaterialPageRoute(
//                                   builder: (context) => const LoginScreen(),
//                                 ),
//                               );
//                             } else if (count1 == 5) {
//                               if (count != 0) {
//                                 errorToast(
//                                     msg:
//                                         ' Document Uploading failed \n Please try again');
//                               }
//                             } else {
//                               successToast(msg: 'Registered Successfully');
//                             }
//                           },
//                           name: 'Sign Up',
//                         ),
//                         SizedBox(height: size.height * 0.01),
//                       ]),
//                 ),
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }

//   void showLoadingDialog(BuildContext context) {
//     showDialog(
//       context: context,
//       barrierDismissible: false,
//       builder: (BuildContext context) {
//         return const AlertDialog(
//           content: Row(
//             children: [
//               CircularProgressIndicator(),
//               SizedBox(width: 16),
//               Text('Sending OTP'),
//             ],
//           ),
//         );
//       },
//     );
//   }

//   Widget buildSkillsDropdown() {
//     return TextFormField(
//       onTap: () {
//         showSkillsDialog();
//       },
//       controller: _skillController,
//       readOnly: true,
//       decoration: const InputDecoration(
//         labelText: 'Select Your Job Field',
//         labelStyle: TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
//         focusedBorder: OutlineInputBorder(
//           borderSide: BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
//         ),
//       ),
//       validator: (value) {
//         if (value == null || value.isEmpty) {
//           return 'Please select your skills';
//         }
//         return null;
//       },
//     );
//   }

//   void showSkillsDialog() {
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           title: const Text('Select Your Job Field'),
//           content: SizedBox(
//             height: 700,
//             child: Scrollbar(
//               child: SingleChildScrollView(
//                 child: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     buildSkillsCheckboxList(),
//                   ],
//                 ),
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }

//   Widget buildSkillsCheckboxList() {
//     return FutureBuilder<List<Map<String, dynamic>>>(
//       future: fetchSkills(),
//       builder: (context, snapshot) {
//         if (snapshot.connectionState == ConnectionState.waiting) {
//           return const CircularProgressIndicator();
//         } else if (snapshot.hasError) {
//           return Text('Error: ${snapshot.error}');
//         } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
//           return const Text('No skills available');
//         }

//         List<Map<String, dynamic>> skills = snapshot.data!;
//         return Column(
//           children: skills.map((skill) {
//             String categoryId = skill['categoryId']!;
//             String categoryTitle = skill['categoryTitle']!;
//             return ListTile(
//               title: Text(categoryTitle),
//               onTap: () {
//                 setState(() {
//                   selectedJobCategoryId = categoryId;
//                   if (selectedMainSkill != categoryTitle) {
//                     selectedSubSkills[selectedMainSkill] = [];
//                     selectedSkills = [];
//                     _updateTextFieldText('');
//                   }
//                   selectedMainSkill = categoryTitle;
//                   Navigator.pop(context);
//                   showSubSkillsDialog(categoryId, categoryTitle);
//                 });
//               },
//             );
//           }).toList(),
//         );
//       },
//     );
//   }

//   void _updateTextFieldText(String mainSkill) {
//     if (selectedSkills.isNotEmpty) {
//       _skillController.text = '$mainSkill: ${selectedSkills.join(', ')}';
//     } else {
//       _skillController.text = '';
//     }
//   }

//   void showSubSkillsDialog(String categoryId, String mainSkill) {
//     selectedSkills = List.from(selectedSubSkills[mainSkill] ?? []);

//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return FutureBuilder<List<Map<String, dynamic>>>(
//           future: fetchSubSkills(categoryId),
//           builder: (context, snapshot) {
//             if (snapshot.connectionState == ConnectionState.waiting) {
//               return const AlertDialog(
//                 title: Text('Loading Sub Skills...'),
//                 content: CircularProgressIndicator(),
//               );
//             } else if (snapshot.hasError) {
//               return AlertDialog(
//                 title: const Text('Error'),
//                 content: Text('Error: ${snapshot.error}'),
//               );
//             } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
//               return const AlertDialog(
//                 title: Text('No Sub Skills Available'),
//                 content: Text('No sub skills available for this skill.'),
//               );
//             }

//             List<Map<String, dynamic>> subSkillsList = snapshot.data!;

//             return StatefulBuilder(
//               builder: (context, setState) {
//                 return AlertDialog(
//                   title: const Text('Select Sub Skills'),
//                   content: SizedBox(
//                     height: 300,
//                     child: Scrollbar(
//                       child: SingleChildScrollView(
//                         child: Column(
//                           mainAxisSize: MainAxisSize.min,
//                           children: subSkillsList.map((subSkill) {
//                             return CheckboxListTile(
//                               title: Text(subSkill['name']),
//                               value: selectedSkills.contains(subSkill['name']),
//                               onChanged: (value) {
//                                 setState(() {
//                                   if (value != null) {
//                                     if (value) {
//                                       selectedSkills.add(subSkill['name']);
//                                     } else {
//                                       selectedSkills.remove(subSkill['name']);
//                                     }
//                                   }
//                                   _updateTextFieldText(mainSkill);
//                                 });
//                               },
//                               controlAffinity: ListTileControlAffinity.leading,
//                             );
//                           }).toList(),
//                         ),
//                       ),
//                     ),
//                   ),
//                   actions: [
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         ElevatedButton(
//                           onPressed: () {
//                             setState(() {
//                               selectedSubSkills[mainSkill] =
//                                   List.from(selectedSkills);
//                               selectedServiceIds = subSkillsList
//                                   .where((subSkill) =>
//                                       selectedSkills.contains(subSkill['name']))
//                                   .map((subSkill) => subSkill['id'].toString())
//                                   .toList();
//                               _updateTextFieldText(mainSkill);
//                             });
//                             Navigator.pop(context);
//                           },
//                           style: ElevatedButton.styleFrom(
//                             backgroundColor:
//                                 const Color.fromRGBO(0, 131, 143, 1),
//                           ),
//                           child: const SizedBox(
//                             width: 80,
//                             height: 40,
//                             child: Center(
//                               child: Text(
//                                 'Done',
//                                 style: TextStyle(color: Colors.white),
//                               ),
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ],
//                 );
//               },
//             );
//           },
//         );
//       },
//     );
//   }

//   Widget buildAddressDropdown() {
//     return Row(
//       children: [
//         Expanded(
//           child: TextField(
//             onTap: () {
//               showDistrictMunicipalityWardDialog();
//             },
//             controller: _addressController,
//             readOnly: !isEditingAddress,
//             decoration: const InputDecoration(
//               labelText: 'Address',
//               labelStyle: TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
//               focusedBorder: OutlineInputBorder(
//                 borderSide: BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
//               ),
//               prefixIcon: Icon(
//                 Icons.home,
//                 color: Color.fromRGBO(0, 131, 143, 1),
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   void showDistrictMunicipalityWardDialog() {
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           title: const Text('Select Address'),
//           content: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               buildDropdown(
//                 label: 'District',
//                 items: districts,
//                 onChanged: (value) {
//                   setState(() {
//                     selectedDistrict = value;
//                     selectedMunicipality = '';
//                     selectedWard = '';
//                   });
//                   Navigator.pop(context);
//                   showMunicipalityDialog();
//                 },
//               ),
//             ],
//           ),
//         );
//       },
//     );
//   }

//   void showMunicipalityDialog() {
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           title: const Text('Select Municipality'),
//           content: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               if (selectedDistrict != null)
//                 buildDropdown(
//                   label: 'Municipality',
//                   items: municipalities[selectedDistrict!] ?? [],
//                   onChanged: (value) {
//                     setState(() {
//                       selectedMunicipality = value;
//                       selectedWard = '';
//                     });
//                     Navigator.pop(context);
//                     showWardDialog();
//                   },
//                 ),
//             ],
//           ),
//         );
//       },
//     );
//   }

//   void showWardDialog() {
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           title: const Text('Select Ward'),
//           content: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               if (selectedMunicipality != null)
//                 buildDropdown(
//                   label: 'Ward',
//                   items: wards[selectedMunicipality!] ?? [],
//                   onChanged: (value) {
//                     setState(() {
//                       selectedWard = value;
//                       _addressController.text =
//                           '$selectedDistrict, $selectedMunicipality, $selectedWard';
//                     });
//                     Navigator.pop(context);
//                   },
//                 ),
//             ],
//           ),
//         );
//       },
//     );
//   }

//   Widget buildDropdown({
//     required String label,
//     required List<String> items,
//     required Function(String) onChanged,
//   }) {
//     return DropdownButtonFormField(
//       decoration: InputDecoration(
//         labelText: label,
//         labelStyle: const TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
//         focusedBorder: const OutlineInputBorder(
//           borderSide: BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
//         ),
//       ),
//       value: selectedWard != null && items.contains(selectedWard)
//           ? selectedWard
//           : items.isNotEmpty
//               ? items.first
//               : null,
//       items: items
//           .map((item) => DropdownMenuItem(
//                 value: item,
//                 child: Text(
//                   item,
//                   style: const TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
//                 ),
//               ))
//           .toList(),
//       onChanged: (value) {
//         onChanged(value.toString());
//       },
//     );
//   }

//   Widget buildViewTermsAndConditions(BuildContext context) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         Flexible(
//           child: TextButton(
//             onPressed: () {
//               Navigator.push(
//                 context,
//                 MaterialPageRoute(
//                     builder: (context) => const PrivacypolicyScreen()),
//               );
//             },
//             child: const Text(
//               'View Terms and Conditions',
//               style: TextStyle(
//                 color: Color.fromRGBO(0, 131, 143, 1),
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   bool agreedToTerms = false;
//   Widget buildTermsCheckbox() {
//     return Center(
//       child: Transform.scale(
//         scale: 0.8, // Adjust the scale factor to make the checkbox smaller
//         child: CheckboxListTile(
//           value: agreedToTerms,
//           onChanged: (bool? value) {
//             setState(() {
//               agreedToTerms = value ?? false;
//             });
//           },
//           title: const FittedBox(
//             fit: BoxFit.scaleDown,
//             child: Text(
//               'I agree to the Terms and Conditions',
//               style: TextStyle(
//                 color: Color.fromRGBO(0, 131, 143, 1),
//                 fontWeight: FontWeight.bold,
//                 fontSize: 16.0,
//               ),
//             ),
//           ),
//           controlAffinity: ListTileControlAffinity.leading,
//         ),
//       ),
//     );
//   }
// }










import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:smartsewa/views/utils.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import 'package:image_picker/image_picker.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/network/services/authServices/auth_controller.dart';
import 'package:smartsewa/views/auth/login/login_screen.dart';

import 'package:smartsewa/views/user_screen/approval/open_map_screen.dart';
import 'package:smartsewa/views/widgets/buttons/app_buttons.dart';

import 'package:smartsewa/views/widgets/custom_toasts.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';

import 'package:http/http.dart' as http;
import 'package:timer_button_fork/timer_button_fork.dart';

import '../../user_screen/approval/map_controller.dart';
import '../../user_screen/drawer screen/privacypolicyscreen.dart';
import 'package:shared_preferences/shared_preferences.dart';

class UserRegistration extends StatefulWidget {
  final mapController = Get.put(MapController());
  UserRegistration({super.key});

  @override
  State<UserRegistration> createState() => _UserRegistrationState();
}

class _UserRegistrationState extends State<UserRegistration> {
  final controller = Get.put(AuthController());
  final GlobalKey<FormState> _formkey = GlobalKey<FormState>();

//////////////    controllers for all the form field      ////////////////////////////
  final TextEditingController _fullNameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final TextEditingController _otpController = TextEditingController();
  final TextEditingController _skillController = TextEditingController();
  final TextEditingController _workingaddressController =
      TextEditingController();
  // final TextEditingController _documentController = TextEditingController();

///////////////////// global variables ////////////////////////////////////
  bool isServiceProvider = false;
  Position? position;
  bool workStatus = false;
  bool approval = false;
  int id = 0;
  var isLoading = false.obs;
  bool isFormFilled = false;
  bool isTermsChecked = false;
  int count = 0, count1 = 0;
  String? citizenshipFrontUrl;
  String? citizenshipBackUrl;
  String? drivingLicenseUrl;
  String? nationalIdUrl;
  bool isEmailVerified = false;
  bool isPhoneVerified = false;
  bool _isPhoneFieldEnabled = true;
  bool _isButtonEnabled = false;
  bool _isGetOtpEnabled = true;
  bool _isOtpFieldEnabled = true;
//////////////    for select your address dropdown  ///////////////////////////////////////
  String? selectedDistrict;
  String? selectedMunicipality;
  String? selectedWard;
  String? editedAddress;
  bool isEditingAddress = false;

/////////////////////// password visible   ///////////////////////////////////////////
  bool _passwordVisible = false;
  bool _confirmPasswordVisible = false;

  //////////////////////////// OTP DIALOG /////////////////////

  void showOtpInputDialog(BuildContext context, Function onOtpVerified) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Enter OTP sent to Phone'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                maxLength: 6,
                keyboardType: TextInputType.number,
                controller: _otpController,
                decoration: const InputDecoration(
                  labelText: '6-digit OTP',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 8.0),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TimerButton(
                    onPressed: () async {
                      try {
                        String mobile = _phoneController.text;
                        var request = http.MultipartRequest(
                          'POST',
                          Uri.parse(
                              '$baseUrl/api/v1/auth/$mobile/mobileRegister'),
                        );

                        var response = await request
                            .send()
                            .timeout(const Duration(seconds: 20));

                        var responseBody =
                            await response.stream.bytesToString();

                        if (response.statusCode == 200) {
                          successToast(msg: 'OTP Resent Successfully');
                        } else {
                          errorToast(
                              msg: '${jsonDecode(responseBody)['message']} ');
                        }
                      } on TimeoutException {
                        errorToast(msg: 'Request Timeout');
                        isLoading.value = false;
                      } on http.ClientException {
                        errorToast(msg: 'Client Error');
                        isLoading.value = false;
                      } catch (err) {
                        errorToast(msg: 'Unexpected error: ${err.toString()}');
                        isLoading.value = false;
                      }
                    },
                    label: 'Resend',
                    timeOutInSeconds: 120,
                    color: const Color.fromARGB(255, 0, 131, 143),
                    disabledColor: const Color.fromARGB(255, 0, 131, 143),
                    disabledTextStyle: const TextStyle(color: Colors.white),
                  ),
                  const SizedBox(width: 4.0),
                  ElevatedButton(
                    onPressed: () async {
                      try {
                        String otp = _otpController.text;
                        String mobile = _phoneController.text;
                        var request = http.MultipartRequest(
                          'POST',
                          Uri.parse('$baseUrl/api/v1/auth/$mobile/verify-otp'),
                        );

                        request.fields.addAll({'otp': otp});

                        var response = await request
                            .send()
                            .timeout(const Duration(seconds: 20));

                        var responseBody =
                            await response.stream.bytesToString();

                        if (response.statusCode == 200) {
                          successToast(
                              msg: 'Phone Number Verified Successfully');
                          Get.back();
                          onOtpVerified();
                        } else {
                          errorToast(
                              msg: '${jsonDecode(responseBody)['message']} ');
                        }
                      } on TimeoutException {
                        errorToast(msg: 'Request Timeout');
                        isLoading.value = false;
                      } on http.ClientException {
                        errorToast(msg: 'Client Error');
                        isLoading.value = false;
                      } catch (err) {
                        errorToast(msg: 'Unexpected error: ${err.toString()}');
                        isLoading.value = false;
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color.fromRGBO(0, 131, 143, 1),
                    ),
                    child: const Text('Verify OTP'),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  //////////////////////////////for documents upload/////////////////////////////
  File? citizenshipFront;
  File? citizenshipBack;
  File? drivingLicense;
  File? nationalId;

  final List<File?> _pickedImages = [];
  Future<void> _pickImage(ImageSource source, String documentType) async {
    final picker = ImagePicker();

    try {
      if (documentType == 'citizenship') {
        final selectedSide = await _showCitizenshipSideDialog();
        if (selectedSide == null) {
          return;
        }
        if (selectedSide == 'front') {
          documentType = 'citizenship_front';
          final selectedSource =
              await _showImageSourceDialog("Select Front Side ");
          if (selectedSource == null) {
            return;
          }
          source = selectedSource;
        } else {
          documentType = 'citizenship_back';
          final selectedSource =
              await _showImageSourceDialog("Select Back Side ");
          if (selectedSource == null) {
            return;
          }
          source = selectedSource;
        }
      } else if (documentType == 'driving_license' ||
          documentType == 'national_id') {
        final selectedSource = await _showImageSourceDialog("Select Image");
        if (selectedSource == null) {
          return;
        }
        source = selectedSource;
      }

      final pickedFile = await picker.pickImage(source: source);

      if (pickedFile != null) {
        final File pickedImage = File(pickedFile.path);
        final File? compressedImage = await compressImage(pickedImage);

        if (compressedImage != null) {
          setState(() {
            switch (documentType) {
              case 'citizenship_front':
                citizenshipFront = compressedImage;
                documentController.text = "Citizenship Front Selected";
                count = 1;
                break;
              case 'citizenship_back':
                citizenshipBack = compressedImage;
                documentController.text = "Citizenship  Selected";
                count = 1;
                break;
              case 'driving_license':
                drivingLicense = compressedImage;
                documentController.text = "Driving License  Selected";
                count = 2;
                break;
              case 'national_id':
                nationalId = compressedImage;
                documentController.text = "National Id  Selected";
                count = 3;

                break;
              default:
                break;
            }

            final side = documentType.split('_').last;
            final message = 'Successfully Selected $side side';
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(message)),
            );

            Future.delayed(const Duration(seconds: 1), () {
              if (documentType == 'citizenship_front') {
                _pickImage(source, 'citizenship');
              }
            });
          });
          consolelog('Selected Document Type: $documentType');
        } else {
          consolelog('Image compression failed');
        }
      } else {
        consolelog('No image selected');
      }
    } catch (error) {
      consolelog('Error picking image: $error');
    }
  }

  Future<ImageSource?> _showImageSourceDialog(String title) async {
    return await showDialog<ImageSource>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          actions: [
            ListTile(
              leading: const Icon(Icons.camera),
              title: const Text('Take a Picture'),
              onTap: () {
                Navigator.pop(context, ImageSource.camera);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo),
              title: const Text('Choose from Gallery'),
              onTap: () {
                Navigator.pop(context, ImageSource.gallery);
              },
            ),
          ],
        );
      },
    );
  }

  Future<String?> _showCitizenshipSideDialog() async {
    bool isBackEnabled = citizenshipFront != null;

    return await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Citizenship Side'),
          actions: [
            ListTile(
              title: const Text('Front'),
              enabled: citizenshipFront == null,
              onTap: () {
                if (citizenshipFront != null) {
                  errorToast(msg: "Already Selected (Front)");
                } else {
                  Navigator.pop(context, 'front');
                }
              },
            ),
            ListTile(
              title: const Text('Back'),
              enabled: isBackEnabled,
              onTap: () async {
                if (isBackEnabled) {
                  final selectedSource =
                      await _showImageSourceDialog("Select Back Side ");
                  if (selectedSource != null) {
                    Navigator.pop(context, 'back');
                  }
                }
              },
            ),
          ],
        );
      },
    );
  }

  /////////////////////////////////    registering user   //////////////////////////////////////////////////////////////////
  bool validateUserForm({
    required String fullName,
    required String mobile,
    required String address,
    required String password,
    required String otp,
  }) {
    if (fullName.isEmpty ||
        mobile.isEmpty ||
        address.isEmpty ||
        password.isEmpty ||
        otp.isEmpty) {
      return false;
    }
    return true;
  }

  Future<void> registerUser({
    required String fullName,
    required String mobileNumber,
    required String address,
    required String password,
    required bool workStatus,
    required bool approval,
  }) async {
    try {
      final Map<String, dynamic> requestData = {
        "fullName": fullName,
        "mobileNumber": mobileNumber,
        "address": address,
        "password": password,
        "workStatus": workStatus,
        "approval": approval,
      };
      final String jsonData = jsonEncode(requestData);

      consolelog('User Request Data: $jsonData');

      final response = await http
          .post(
            Uri.parse('$baseUrl/api/v1/auth/register'),
            headers: <String, String>{
              'Content-Type': 'application/json',
            },
            body: jsonData,
          )
          .timeout(const Duration(seconds: 20));

      consolelog(response.statusCode);
      isLoading.value = false;

      if (response.statusCode == 201) {
        successToast(msg: "Registered Successfully");
        Get.offAll(() => const LoginScreen());
      } else {
        errorToast(msg: 'Error registering : ${response.body}');
      }
    } on TimeoutException {
      errorToast(msg: 'Request Timeout');
      isLoading.value = false;
    } on http.ClientException {
      errorToast(msg: 'Client Error');
      isLoading.value = false;
    } catch (err) {
      errorToast(msg: 'Unexpected error');
      isLoading.value = false;
    }
  }

  ////////////////////////// validating service provider /////////////////
  bool validateServiceProviderForm({
    required String fullName,
    required String mobileNumber,
    required String address,
    required String password,
    required String jobDetails,
    required String latitude,
    required String otp,
  }) {
    if (fullName.isEmpty ||
        mobileNumber.isEmpty ||
        address.isEmpty ||
        password.isEmpty ||
        jobDetails.isEmpty ||
        latitude.isEmpty ||
        otp.isEmpty) {
      return false;
    }
    return true;
  }

  Future<void> registerService({
    required String fullName,
    required String mobileNumber,
    required String address,
    required String password,
    required Map<String, dynamic> jobDetails,
    required String latitude,
    required bool workStatus,
    required bool approval,
    File? citizenshipFront,
    File? citizenshipBack,
    File? drivingLicense,
    File? nationalId,
  }) async {
    try {
      // Upload images and get URLs
      if (citizenshipFront != null && citizenshipBack != null) {
        if (citizenshipFrontUrl == null && citizenshipBackUrl == null) {
          var result = await uploadImage('citizenshipFront', citizenshipFront);
          citizenshipFrontUrl = result['citizenshipFront'];

          var result1 = await uploadImage('citizenshipBack', citizenshipBack);
          citizenshipBackUrl = result1['citizenshipBack'];
        }
      } else {
        if (count == 1) {
          errorToast(msg: 'Select both sides of citizenship');
        }
      }

      if (drivingLicense != null) {
        if (drivingLicenseUrl == null) {
          var result = await uploadImage('drivingLicense', drivingLicense);
          drivingLicenseUrl = result['drivingLicense'];
          citizenshipFrontUrl = drivingLicenseUrl;
        }
      } else {
        if (count == 2) {
          errorToast(msg: 'Driving License not selected');
        }
      }

      if (nationalId != null) {
        if (nationalIdUrl == null) {
          var result = await uploadImage('nationalId', nationalId);
          nationalIdUrl = result['nationalId'];
          citizenshipFrontUrl = nationalIdUrl;
        }
      } else {
        if (count == 3) {
          errorToast(msg: 'National Id not selected');
        }
      }

      if (count == 0) {
        errorToast(msg: 'Please select any one of the documents');
      }

      if (selectedJobCategoryId == null || selectedServiceIds.isEmpty) {
        errorToast(msg: 'Please select your skills and sub-skills');
        return;
      }

      final Map<String, dynamic> requestData = {
        "fullName": fullName,
        "mobileNumber": mobileNumber,
        "address": address,
        "password": password,
        "jobDetails": {
          "jobCategoryId": int.parse(selectedJobCategoryId!),
          "serviceIds": selectedServiceIds.map(int.parse).toList(),
        },
        "latitude": latitude,
        "workStatus": workStatus,
        "approval": approval,
        "imageUrlCitizenshipFront": citizenshipFrontUrl,
        "imageUrlCitizenshipBack": citizenshipBackUrl,
      };
      final String jsonData = jsonEncode(requestData);

      consolelog('Register Service Request Data: $jsonData');

      if (drivingLicenseUrl != null ||
          nationalIdUrl != null ||
          (citizenshipBackUrl != null && citizenshipFrontUrl != null)) {
        final response = await http
            .post(
              Uri.parse('$baseUrl/api/v1/auth/register'),
              headers: <String, String>{
                'Content-Type': 'application/json',
              },
              body: jsonData,
            )
            .timeout(const Duration(seconds: 20));

        consolelog(response.statusCode);

        if (response.statusCode == 201) {
          successToast(msg: "Registered Successfully");
          Get.offAll(() => const LoginScreen());
          id = jsonDecode(response.body)['id'];
          consolelog(id);
        } else {
          count = 5;
          errorToast(msg: 'Error registering : ${response.body}');
        }
      } else {
        count1 = 5;
      }
    } on TimeoutException {
      errorToast(msg: 'Request Timeout');
      isLoading.value = false;
    } on http.ClientException {
      errorToast(msg: 'Client Error');
      isLoading.value = false;
    } catch (err) {
      errorToast(msg: 'Unexpected error: ${err.toString()}');
      isLoading.value = false;
    }
  }

//districts , municipalities and ward
  List<String> districts = [
    'Achham',
    'Arghakhanchi',
    'Baglung',
    'Baitadi',
    'Bajhang',
    'Bajura',
    'Banke',
    'Bara',
    'Bardiya',
    'Bhaktapur',
    'Bhojpur',
    'Chitwan',
    'Dadeldhura',
    'Dailekh',
    'Dang',
    'Darchula',
    'Dhading',
    'Dhankuta',
    'Dhanusha',
    'Dolakha',
    'Dolpa',
    'Doti',
    'East Rukum',
    'Gorkha',
    'Gulmi',
    'Humla',
    'Ilam',
    'Jajarkot',
    'Jhapa',
    'Jumla',
    'Kailali',
    'Kalikot',
    'Kalikot',
    'Kanchanpur',
    'Kapilvastu',
    'Kaski',
    'Kathmandu',
    'Kavrepalanchok',
    'Khotang',
    'Lalitpur',
    'Lamjung',
    'Mahottari',
    'Makwanpur',
    'Manang',
    'Morang',
    'Mugu',
    'Mustang',
    'Myagdi',
    'Nawalpur',
    'Nuwakot',
    'Okhaldhunga',
    'Palpa',
    'Panchthar',
    'Parbat',
    'Parsa',
    'Pyuthan',
    'Ramechhap',
    'Rasuwa',
    'Rautahat',
    'Rolpa',
    'Rukum West',
    'Rupandehi',
    'Salyan',
    'Sankhuwasabha',
    'Saptari',
    'Sarlahi',
    'Sindhuli',
    'Sindhupalchok',
    'Siraha',
    'Solukhumbu',
    'Sunsari',
    'Surkhet',
    'Syangja',
    'Tanahun',
    'Taplejung',
    'Tehrathum',
    'Udayapur',
    'Western Rukum'
  ];

  Map<String, List<String>> municipalities = {
    'Achham': [
      'Mangalsen',
      'Turmakhad',
      'Chaurpati',
      'Ramaroshan',
      'Panchadewal Binayak',
      'Mellekh',
      'Sanphebagar',
    ],
    'Arghakhanchi': [
      'Sandhikharka',
      'Bhumikasthan',
      'Sharana',
      'Chhatrakot',
      'Panini',
      'Malarani',
    ],
    'Baglung': [
      'Baglung',
      'Galkot',
      'Tara Khola',
      'Bareng',
      'Badigad',
      'Jaimuni',
    ],
    'Baitadi': [
      'Dashrathchand',
      'Patan',
      'Melauli',
      'Shivanath',
      'Pancheshwor',
      'Mahakali',
      'Dilasaini',
    ],
    'Bajhang': [
      'Jaya Prithvi',
      'Talkot',
      'Masta',
      'Surma',
      'Chabbispur',
      'Durgathali',
    ],
    'Bajura': [
      'Budhiganga',
      'Chhededaha',
      'Swamikartik',
      'Himali',
      'Triveni',
    ],
    'Banke': [
      'Nepalgunj',
      'Narainapur',
      'Janaki',
      'Duduwa',
      'Khajura',
      'Baijanath',
      'Rapti Sonari',
    ],
    'Bara': [
      'Kalaiya',
      'Jitpur Simara',
      'Simraungadh',
      'Kolhabi',
      'Mahagadhimai',
      'Parwanipur',
      'Pheta',
      'Prasauni',
    ],
    'Bardiya': [
      'Gulariya',
      'Rajapur',
      'Thakurbaba',
      'Barbardiya National Park',
      'Badhaiyatal',
    ],
    'Bhaktapur': [
      'Anantalingeshwar',
      'Bhaktapur',
      'Changunarayan',
      'MadhyapurThimi',
      'Nagarkot',
      'Suryavinayak'
    ],
    'Bhojpur': [
      'Bhojpur',
      'Shadananda',
      'Hatuwagadhi',
      'Arun',
      'Ramprasad Rai',
      'Khotang',
    ],
    'Chitwan': [
      'Bharatpur',
      'Khairahani',
      'Ratnanagar',
      'Kalika',
      'Khairhani',
      'Rapti',
    ],
    'Dadeldhura': [
      'Dadeldhura',
      'Malikarjun',
      'Asta',
      'Ajayameru',
      'Bhageshwar',
    ],
    'Dailekh': [
      'Dullu',
      'Aathbis',
      'Chamunda Bindrasaini',
      'Narayan',
      'Dullu',
      'Mahabu',
      'Bhagawatimai',
    ],
    'Dang': [
      'Ghorahi',
      'Tulsipur',
      'Lamahi',
      'Rapti',
      'Gadhawa',
      'Banglachuli',
      'Shantinagar',
    ],
    'Darchula': [
      'Darchula',
      'Lekam',
      'Malikarjun',
      'Api',
      'Jayaprithvi',
      'Mahakali',
    ],
    'Dhading': [
      'Dhading',
      'Gajuri',
      'Benighat Rorang',
      'Rubenchal',
      'Netrawati',
      'Galchhi',
    ],
    'Dhankuta': [
      'Dhankuta',
      'Mahalaxmi',
      'Chaubise',
      'Sangurigadhi',
      'Pakhribas',
    ],
    'Dhanusha': [
      'Janakpur',
      'Mithila Bihari',
      'Sabaila',
      'Kamala',
      'Chhireshwarnath',
      'Ganeshman Charnath',
    ],
    'Dolakha': [
      'Charikot',
      'Bigu',
      'Kalinchok',
      'Melung',
      'Gaurishankar',
      'Bigu',
    ],
    'Dolpa': [
      'Dolpa',
      'Thuli Bheri',
      'Mudkechula',
      'Tripurasundari',
      'Jagadulla',
    ],
    'Doti': [
      'Dipayal Silgadhi',
      'Jorayal',
      'Mala',
      'Shikhar',
      'K.I. Singh',
    ],
    'East Rukum': [
      'Putha Uttarganga',
      'Sisne',
      'Nalgad',
      'Sani Bheri',
      'Aathbis',
    ],
    'Gorkha': [
      'Gorkha',
      'Palungtar',
      'Ajirkot',
      'Barpak Sulikot',
      'Gandaki',
      'Sulikot',
    ],
    'Gulmi': [
      'Resunga',
      'Chandrakot',
      'Madane',
      'Musikot',
      'Isma',
    ],
    'Humla': [
      'Simkot',
      'Chankheli',
      'Nama',
      'Kharpunath',
      'Mugum Karmarong',
    ],
    'Ilam': [
      'Ilam',
      'Mai',
      'Fikkal',
      'Chulachuli',
      'Mangsebung',
    ],
    'Jajarkot': [
      'Jajarkot',
      'Kankal',
      'Bheri',
      'Kulung',
      'Chhedagad',
    ],
    'Jhapa': [
      'Bhadrapur',
      'Mechinagar',
      'Kamal',
      'Kachankawal',
      'Arjundhara',
    ],
    'Jumla': [
      'Jumla',
      'Tatopani',
      'Chandannath',
      'Tila',
    ],
    'Kailali': [
      'Dhangadhi',
      'Gauriganga',
      'Joshipur',
      'Tikapur',
      'Kailari',
      'Janaki',
      'Kamala',
    ],
    'Kalikot': [
      'Manma',
      'Raskot',
      'Palata',
      'Khandachakra',
      'Sanni Triveni',
    ],
    'Kanchanpur': [
      'Mahendranagar',
      'Bhimdatta',
      'Punarbas',
      'Bedkot',
      'Krishnapur',
    ],
    'Kapilvastu': [
      'Kapilvastu',
      'Banganga',
      'Buddhabhumi',
      'Shivaraj',
      'Mayadevi',
    ],
    'Kaski': [
      'Pokhara',
      'Annapurna',
      'Machhapuchhre',
      'Rupa',
      'Madi',
    ],
    'Kavrepalanchok': [
      'Dhulikhel',
      'Panauti',
      'Banepa',
      'Chaurideurali',
      'Mandandeupur',
    ],
    'Khotang': [
      'Diktel',
      'Halesi',
      'Rautamai',
      'Sakela',
      'Barahapokhari',
    ],
    'Lamjung': [
      'Besisahar',
      'Dudhpokhari',
      'Madhya',
      'Rainas',
      'Sundarbazar',
    ],
    'Mahottari': [
      'Jaleshwor',
      'Manara',
      'Pipra',
      'Balawa',
      'Ekdara',
    ],
    'Makwanpur': [
      'Hetauda',
      'Thaha',
      'Makawanpurgadhi',
      'Manahari',
      'Bagmati',
    ],
    'Manang': [
      'Chame',
      'Narpa Bhumi',
    ],
    'Morang': [
      'Biratnagar',
      'Rangeli',
      'Belbari',
      'Pathari',
      'Budhiganga',
    ],
    'Mugu': [
      'Mugu',
      'Karnali',
      'Soru',
    ],
    'Mustang': [
      'Jomsom',
      'Lomanthang',
      'Thasang',
    ],
    'Myagdi': [
      'Beni',
      'Annapurna',
      'Malika',
    ],
    'Nawalpur': [
      'Kawasoti',
      'Sunwal',
      'Devchuli',
    ],
    'Nuwakot': [
      'Bidur',
      'Shivapuri',
      'Belkot',
      'Trishuli',
      'Likhu',
    ],
    'Okhaldhunga': [
      'Okhaldhunga',
      'Siddhicharan',
      'Chishankhugadhi',
    ],
    'Palpa': [
      'Tansen',
      'Rambha',
      'Ribdikot',
      'Rainadevi Chhahara',
    ],
    'Panchthar': [
      'Phidim',
      'Kummayak',
      'Hilung',
      'Yangwarak',
    ],
    'Parbat': [
      'Phalebas',
      'Jaljala',
      'Modi',
    ],
    'Parsa': [
      'Birgunj',
      'Pokhariya',
      'Bahudarmai',
      'Chhipaharmai',
    ],
    'Pyuthan': [
      'Pyuthan',
      'Gaumukhi',
      'Naugarh',
      'Rohini',
    ],
    'Ramechhap': [
      'Manthali',
      'Umakunda',
      'Gokulganga',
      'Sunapati',
    ],
    'Rasuwa': [
      'Dhunche',
      'Kalika',
      'Uttargaya',
    ],
    'Rautahat': [
      'Gaur',
      'Ishanath',
      'Rajpur',
      'Paroha',
      'Garuda',
    ],
    'Rolpa': [
      'Rolpa',
      'Libang',
      'Runtigadhi',
    ],
    'Rukum West': [
      'Musikot',
      'Chaurjahari',
      'Sani Bheri',
    ],
    'Rupandehi': [
      'Siddharthanagar',
      'Butwal',
      'Bharatpur',
      'Devdaha',
      'Kanchan',
    ],
    'Salyan': [
      'Salyan',
      'Sharada',
      'Bagchaur',
    ],
    'Sankhuwasabha': [
      'Khandbari',
      'Chainpur',
      'Madi',
      'Phedap',
    ],
    'Saptari': [
      'Agnisair Krishna Savaran',
      'Kanchanrup',
      'Khadak',
      'Chhinnamasta',
      'Dakneshwork',
      'Tirahut',
      'Tilathi Koiladi',
      'Rajgadh',
      'Bode Barsain',
      'Mahadeva',
      'Rajbiraj',
      'Rupani',
      'Balan Bihul',
      'Bishnupur',
      'Shambhunath',
      'Saptakoshi',
      'Surunga',
      'Hanumannagar Kankalini'
    ],
    'Sarlahi': [
      'Ishworpur',
      'Chandranagar',
      'Barahathawa',
      'Bagmati',
      'Malangawa',
      'Lalbandi',
      'Hariwan',
      'Kabilasi',
      'Kaudena',
      'Godaita',
      'Chakraghatta',
      'Dhankaul',
      'Parsa',
      'Balara',
      'Basbariya',
      'Bramhapuri',
      'Ramnagar',
      'Bishnu',
      'Haripur',
      'Haripurwa'
    ],
    'Sindhuli': [
      'Kamalamai',
      'Golanjor',
      'Ghanglekh',
      'Tinpatan',
      'Dudhouli',
      'Phikkal',
      'Marin',
      'Sunkoshi',
      'Hariharpurgadhi'
    ],
    'Sindhupalchok': [
      'Chautara',
      'Jugal',
      'Tripurasundari',
      'Pachpokhari-Thangpal',
      'Balefi',
      'Bhotekoshi',
      'Melamchi',
      'Indrawati',
      'Lisangkhu-Pakhar',
      'Bhrhabise',
      'Sunkoshi',
      'Helambu'
    ],
    'Siraha': [
      'Lahan',
      'Arnama',
      'Dhangadhimai',
    ],
    'Solukhumbu': [
      'Solu Dudhkunda',
      'Khumbu Pasanglhamu',
    ],
    'Sunsari': [
      'Inaruwa',
      'Dharan',
      'Barah',
      'Koshi',
    ],
    'Surkhet': [
      'Birendranagar',
      'Chaukune',
      'Bheriganga',
    ],
    'Syangja': [
      'Putalibazar',
      'Waling',
      'Bhirkot',
    ],
    'Tanahun': [
      'Bhanu',
      'Shuklagandaki',
      'Byas',
    ],
    'Taplejung': [
      'Phungling',
      'Mikwakhola',
      'Maiwakhola',
    ],
    'Tehrathum': [
      'Myanglung',
      'Aathrai',
    ],
    'Udayapur': [
      'Gaighat',
      'Chaudandigadhi',
      'Rautamai',
    ],
    'Western Rukum': [
      'Chaurjahari',
      'Tribeni',
      'Sani Bheri',
    ],
    'Kathmandu': [
      'Budhanilkantha',
      'Chandragiri',
      'Dakshinkali',
      'Gokarneshwar',
      'Kathmandu Metropolitan',
      'Kageshwori Manohara',
      'Kirtipur',
      'Nagarjun',
      'Shankharapur',
      'Tarakeshwar',
      'Tokha'
    ],
    'Lalitpur': [
      'Bagmati',
      'Godawari',
      'Konjyosom',
      'Lalitpur Metropolitan',
      'Mahalaxmi',
      'Mahankal'
    ],
  };
  Map<String, List<String>> wards = {
    'Bagmati': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Godawari': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14'
    ],
    'Konjyosom': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5'],
    'Lalitpur Metropolitan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17',
      'Ward 18',
      'Ward 19',
      'Ward 20',
      'Ward 21',
      'Ward 22',
      'Ward 23',
      'Ward 24',
      'Ward 25',
      'Ward 26',
      'Ward 27',
      'Ward 28',
      'Ward 29'
    ],
    'Mahalaxmi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Mahankal': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5', 'Ward 6'],
    'Kamalamai': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14'
    ],
    'Golanjor': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Ghanglekh': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5'],
    'Tinpatan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],
    'Dudhouli': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14'
    ],
    'Phikkal': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5', 'Ward 6'],
    'Marin': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Sunkoshi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Hariharpurgadhi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Chautara': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14'
    ],
    'Jugal': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Tripurasundari': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6'
    ],
    'Pachpokhari-Thangpal': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Balefi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Bhotekoshi': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5'],
    'Melamchi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13'
    ],
    'Indrawati': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12'
    ],
    'Lisangkhu-Pakhar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Bhrhabise': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Sunkoshi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Helambu': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Ishworpur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Chandranagar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Barahathawa': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17',
      'Ward 18'
    ],
    'Bagmati': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12'
    ],
    'Malangawa': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12'
    ],
    'Lalbandi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17'
    ],
    'Hariwan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],
    'Kabilasi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Kaudena': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Godaita': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12'
    ],
    'Chakraghatta': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Dhankaul': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Parsa': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5', 'Ward 6'],
    'Balara': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],
    'Basbariya': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5', 'Ward 6'],
    'Bramhapuri': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Ramnagar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Bishnu': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Haripur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Haripurwa': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Agnisair Krishna Savaran': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6'
    ],
    'Kanchanrup': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12'
    ],
    'Khadak': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],
    'Chhinnamasta': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Dakneshwork': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Tirahut': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5'],
    'Tilathi Koiladi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Rajgadh': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5', 'Ward 6'],
    'Bode Barsain': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Mahadeva': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5', 'Ward 6'],
    'Rajbiraj': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16'
    ],
    'Rupani': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5', 'Ward 6'],
    'Balan Bihul': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5', 'Ward 6'],
    'Bishnupur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Shambhunath': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12'
    ],
    'Saptakoshi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],
    'Surunga': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],
    'Hanumannagar Kankalini': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14'
    ],

    'Budhanilkantha': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13'
    ],
    'Chandragiri': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Dakshinkali': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Gokarneshwar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Kathmandu Metropolitan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17',
      'Ward 18',
      'Ward 19',
      'Ward 20',
      'Ward 21',
      'Ward 22',
      'Ward 23',
      'Ward 24',
      'Ward 25',
      'Ward 26',
      'Ward 27',
      'Ward 28',
      'Ward 29',
      'Ward 30',
      'Ward 31',
      'Ward 32'
    ],
    'Kageshwori Manohara': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Kirtipur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Nagarjun': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Shankharapur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Tarakeshwar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],
    'Tokha': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],

    // Achham
    'Mangalsen': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Turmakhad': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Chaurpati': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Ramaroshan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Panchadewal Binayak': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Mellekh': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Sanphebagar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    // Arghakhanchi
    'Sandhikharka': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Bhumikasthan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Sharana': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Chhatrakot': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Panini': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Malarani': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    // Baglung
    'Baglung': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Galkot': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Tara Khola': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Bareng': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Badigad': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Jaimuni': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    // Baitadi
    'Dashrathchand': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Patan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Melauli': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Shivanath': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Pancheshwor': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Mahakali': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Dilasaini': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    'Jaya Prithvi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Talkot': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Masta': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Surma': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Chabbispur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Durgathali': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    // Bajura
    'Budhiganga': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Chhededaha': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Swamikartik': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Himali': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Triveni': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    // Banke
    'Nepalgunj': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Narainapur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Janaki': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Duduwa': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Khajura': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Baijanath': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Rapti Sonari': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Kalaiya': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17',
      'Ward 18',
      'Ward 19',
      'Ward 20',
      'Ward 21',
      'Ward 22',
      'Ward 23',
      'Ward 24',
      'Ward 25',
      'Ward 26',
      'Ward 27'
    ],
    'Jitpur Simara': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17',
      'Ward 18',
      'Ward 19'
    ],
    'Simraungadh': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Kolhabi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],
    'Mahagadhimai': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Parwanipur': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5'],
    'Pheta': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Prasauni': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],

    // Bardiya
    'Gulariya': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],
    'Rajapur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Thakurbaba': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Barbardiya National Park': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Badhaiyatal': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    // Bhaktapur
    'Anantalingeshwar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Bhaktapur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Changunarayan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'MadhyapurThimi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Nagarkot': ['Ward 1', 'Ward 2', 'Ward 3'],
    'Suryavinayak': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12'
    ],

    // Bhojpur
    'Bhojpur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Shadananda': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],
    'Hatuwagadhi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Arun': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Ramprasad Rai': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Khotang': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],

    'Bharatpur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17',
      'Ward 18',
      'Ward 19',
      'Ward 20',
      'Ward 21',
      'Ward 22',
      'Ward 23',
      'Ward 24',
      'Ward 25',
      'Ward 26',
      'Ward 27',
      'Ward 28',
      'Ward 29'
    ],
    'Khairahani': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Ratnanagar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Kalika': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Khairhani': [
      // Appears to be a duplicate/misspelled entry
      'Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5',
      'Ward 6', 'Ward 7', 'Ward 8', 'Ward 9'
    ],
    // 'Rapti': [
    //   'Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5',
    //   'Ward 6', 'Ward 7', 'Ward 8', 'Ward 9'
    // ],

    // Dadeldhura
    'Dadeldhura': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    // 'Malikarjun': [
    //   'Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5',
    //   'Ward 6', 'Ward 7'
    // ],
    'Asta': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5'],
    'Ajayameru': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Bhageshwar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Dullu': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Aathbis_EastRukum': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Chamunda Bindrasaini': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Narayan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Mahabu': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Bhagawatimai': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],

    // Dang
    'Ghorahi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17',
      'Ward 18',
      'Ward 19'
    ],
    'Tulsipur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17',
      'Ward 18',
      'Ward 19'
    ],
    'Lamahi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Rapti': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Gadhawa': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Banglachuli': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Shantinagar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],

    'Darchula': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Lekam': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Malikarjun': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Api': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Jayaprithvi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Mahakali_Darchula': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],

    // Dhading
    'Dhading': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Gajuri': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Benighat Rorang': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Rubenchal': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Netrawati': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Galchhi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Dhankuta': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Mahalaxmi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Chaubise': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Sangurigadhi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Pakhribas': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    // Dhanusha
    'Janakpur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17',
      'Ward 18',
      'Ward 19',
      'Ward 20',
      'Ward 21'
    ],
    'Mithila Bihari': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Sabaila': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
    ],
    'Kamala': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Chhireshwarnath': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Ganeshman Charnath': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    // Dolakha
    'Charikot': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Bigu': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Kalinchok': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Melung': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Gaurishankar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    // Dolpa
    'Dolpa': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Thuli Bheri': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Mudkechula': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Tripurasundari': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Jagadulla': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    'Dipayal Silgadhi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Jorayal': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Mala': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Shikhar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'K.I. Singh': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    // East Rukum
    'Putha Uttarganga': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Sisne': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Nalgad': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Sani Bheri_EastRukum': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Aathbis': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Gorkha': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Palungtar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Ajirkot': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Barpak Sulikot': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Gandaki': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Sulikot': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    // Gulmi
    'Resunga': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Chandrakot': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Madane': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Musikot': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Isma': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Simkot': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Chankheli': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Nama': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Kharpunath': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Mugum Karmarong': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    // Ilam
    'Ilam': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Mai': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Fikkal': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Chulachuli': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Mangsebung': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Jumla': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Tatopani': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Chandannath': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Tila': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    // Part 15: Kailali
    'Dhangadhi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Gauriganga': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Joshipur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Tikapur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Kailari': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Janaki': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5'],
    'Kamala': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],

    // Part 16: Kalikot
    'Manma': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Raskot': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Palata': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Khandachakra': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Sanni Triveni': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6'
    ],

    // Part 17: Kanchanpur
    'Mahendranagar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Bhimdatta': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Punarbas': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Bedkot': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Krishnapur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    // Part 18: Kapilvastu
    'Kapilvastu': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Banganga': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Buddhabhumi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Shivaraj': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Mayadevi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],

    // Part 19: Kaski
    'Pokhara': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17',
      'Ward 18',
      'Ward 19',
      'Ward 20',
      'Ward 21',
      'Ward 22',
      'Ward 23',
      'Ward 24',
      'Ward 25',
    ],
    'Annapurna': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Machhapuchhre': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Rupa': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Madi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Dhulikhel': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Panauti': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Banepa': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],
    'Chaurideurali': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Mandandeupur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    // Part 21: Khotang
    'Diktel': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13'
    ],
    'Halesi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Rautamai': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Sakela': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Barahapokhari': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5'],
    'Besisahar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12'
    ],
    'Dudhpokhari': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Madhya': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Rainas': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Sundarbazar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],

    // Part 23: Mahottari
    'Jaleshwor': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12'
    ],
    'Manara': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Pipra': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Balawa': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Ekdara': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5', 'Ward 6'],
    'Hetauda': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Thaha': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Makawanpurgadhi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Manahari': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Bagmati': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13'
    ],

    // Part 25: Manang
    'Chame': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Narpa Bhumi': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4'],
    'Biratnagar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17',
      'Ward 18',
      'Ward 19',
      'Ward 20',
      'Ward 21',
      'Ward 22',
      'Ward 23',
      'Ward 24',
      'Ward 25',
      'Ward 26',
      'Ward 27'
    ],
    'Rangeli': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Belbari': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14'
    ],
    'Pathari': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12'
    ],
    'Budhiganga': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5'],

    // Part 26: Mugu
    'Mugu': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Karnali': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Soru': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5'],

    // Part 26: Mustang
    'Jomsom': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Lomanthang': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Thasang': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5', 'Ward 6'],
    'Beni': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13'
    ],
    'Annapurna': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Malika': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],

    // Part 27: Nawalpur
    'Kawasoti': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Sunwal': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Devchuli': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],

    // Part 27: Nuwakot
    'Bidur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Shivapuri': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Belkot': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Trishuli': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11'
    ],
    'Likhu': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Okhaldhunga': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14'
    ],
    'Siddhicharan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Chishankhugadhi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],

    // Part 28: Palpa
    'Tansen': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Rambha': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Ribdikot': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10'
    ],
    'Rainadevi Chhahara': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    // Part 28: Panchthar
    'Phidim': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13'
    ],
    'Kummayak': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5'],
    'Hilung': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Yangwarak': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Phalebas': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Jaljala': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Modi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    // Part 29: Parsa
    'Birgunj': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17',
      'Ward 18',
      'Ward 19',
      'Ward 20',
      'Ward 21',
      'Ward 22',
      'Ward 23',
      'Ward 24',
      'Ward 25'
    ],
    'Pokhariya': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Bahudarmai': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Chhipaharmai': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],

    // Part 29: Pyuthan
    'Pyuthan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Gaumukhi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Naugarh': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Rohini': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Manthali': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Umakunda': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9'
    ],
    'Gokulganga': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Sunapati': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4', 'Ward 5', 'Ward 6'],

    // Part 30: Rasuwa
    'Dhunche': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Kalika': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Uttargaya': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
    ],

    // Part 30: Rautahat
    'Gaur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17',
      'Ward 18',
      'Ward 19',
      'Ward 20',
    ],
    'Ishanath': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],
    'Rajpur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
    ],
    'Paroha': ['Ward 1', 'Ward 2', 'Ward 3', 'Ward 4'],
    'Garuda': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7'
    ],

    // Part 30: Rolpa
    'Rolpa': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15'
    ],
    'Libang': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8'
    ],
    'Runtigadhi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
    ],
    'Musikot': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12'
    ],
    'Chaurjahari': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
    ],
    'Sani Bheri': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
    ],

    // Part 31: Rupandehi
    'Siddharthanagar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17',
      'Ward 18',
      'Ward 19',
      'Ward 20',
    ],
    'Butwal': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
    ],
    'Bharatpur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
    ],
    'Devdaha': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
    ],
    'Kanchan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
    ],

    // Part 31: Salyan
    'Salyan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
    ],
    'Sharada': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
    ],
    'Bagchaur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
    ],

    // Part 31: Sankhuwasabha
    'Khandbari': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
    ],
    'Chainpur': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
    ],
    'Madi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
    ],
    'Phedap': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
    ],
    'Lahan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
      'Ward 16',
      'Ward 17',
      'Ward 18',
      'Ward 19',
      'Ward 20',
    ],
    'Arnama': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
    ],
    'Dhangadhimai': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
    ],

    // Part 33: Solukhumbu
    'Solu Dudhkunda': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
    ],
    'Khumbu Pasanglhamu': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
    ],

    // Part 33: Sunsari
    'Inaruwa': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
    ],
    'Dharan': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
    ],
    'Barah': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
    ],
    'Koshi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
    ],

    // Part 33: Surkhet
    'Birendranagar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
    ],
    'Chaukune': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
    ],
    'Bheriganga': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
    ],
    'Putalibazar': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
    ],
    'Waling': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
    ],
    'Bhirkot': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
    ],

    // Part 35: Tanahun
    'Bhanu': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
    ],
    'Shuklagandaki': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
    ],
    'Byas': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
    ],

    // Part 36: Taplejung
    'Phungling': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
    ],
    'Mikwakhola': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
    ],
    'Maiwakhola': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
    ],

    // Part 37: Tehrathum
    'Myanglung': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
    ],
    'Aathrai': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
    ],

    // Part 38: Udayapur
    'Gaighat': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
      'Ward 11',
      'Ward 12',
      'Ward 13',
      'Ward 14',
      'Ward 15',
    ],
    'Chaudandigadhi': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
    ],
    'Rautamai': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
    ],

    // Part 39: Western Rukum
    'Chaurjahari_WesternRukum': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
      'Ward 6',
      'Ward 7',
      'Ward 8',
      'Ward 9',
      'Ward 10',
    ],
    'Tribeni': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
    ],
    'Sani Bheri_WesternRukum': [
      'Ward 1',
      'Ward 2',
      'Ward 3',
      'Ward 4',
      'Ward 5',
    ]
  };

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return Scaffold(
      appBar: myAppbar(context, true, "Register"),
      body: SingleChildScrollView(
        child: Padding(
          // Use Padding or SafeArea to avoid overflow
          padding: const EdgeInsets.symmetric(vertical: 20.0),
          child: Container(
            // Remove fixed height here
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color.fromARGB(255, 255, 255, 255),
                  Color.fromARGB(255, 255, 255, 255),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Center(
              child: Container(
                width: size.width * 0.97,
                padding: const EdgeInsets.all(20.0),
                decoration: BoxDecoration(
                  color: Colors.white, // Set background color
                  borderRadius: BorderRadius.circular(16.0), // Rounded corners
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.black26,
                      blurRadius: 8.0,
                      spreadRadius: 2.0,
                    ),
                  ],
                ),
                child: Form(
                  key: _formkey,
                  onChanged: () {
                    setState(() {
                      isFormFilled = _fullNameController.text.isNotEmpty &&
                          _phoneController.text.isNotEmpty &&
                          _addressController.text.isNotEmpty &&
                          _passwordController.text.isNotEmpty &&
                          _confirmPasswordController.text.isNotEmpty &&
                          _otpController.text.isNotEmpty &&
                          _skillController.text.isNotEmpty &&
                          _workingaddressController.text.isNotEmpty &&
                          isTermsChecked;
                    });
                  },
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(height: size.height * 0.02),
                        TextFormField(
                          controller: _fullNameController,
                          decoration: const InputDecoration(
                            labelText: 'Full Name',
                            labelStyle: TextStyle(
                                color: Color.fromRGBO(0, 131, 143, 1)),
                            focusedBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                  color: Color.fromRGBO(0, 131, 143, 1)),
                            ),
                            prefixIcon:
                                Icon(Icons.person, color: Color(0xFF007F83)),
                          ),
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your full name';
                            }
                            return null;
                          },
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'[a-zA-Z\s]')),
                          ],
                          keyboardType: TextInputType.name,
                        ),
                        SizedBox(height: size.height * 0.02),
                        // TextFormField(
                        //     controller: _phoneController,
                        //     enabled: _isPhoneFieldEnabled,
                        //     decoration: const InputDecoration(
                        //       labelText: 'Phone',
                        //       labelStyle: TextStyle(
                        //           color: Color.fromRGBO(0, 131, 143, 1)),
                        //       focusedBorder: OutlineInputBorder(
                        //         borderSide: BorderSide(
                        //             color: Color.fromRGBO(0, 131, 143, 1)),
                        //       ),
                        //       prefixIcon: Icon(
                        //         Icons.phone,
                        //         color: Color.fromRGBO(0, 131, 143, 1),
                        //       ),
                        //     ),
                        //     keyboardType: TextInputType.phone,
                        //     autovalidateMode:
                        //         AutovalidateMode.onUserInteraction,
                        //     inputFormatters: [
                        //       FilteringTextInputFormatter.allow(
                        //           RegExp(r'[0-9]')),
                        //       LengthLimitingTextInputFormatter(10),
                        //     ],
                        //     onChanged: (value){
                        //       setState(() {
                        //         _isButtonEnabled = value.length == 10 && RegExp(r'^(98|97|96)\d{8}$').hasMatch(value);
                        //       });
                        //     },
                        //     validator: (value) {
                        //       if (value == null || value.isEmpty) {
                        //         // _isButtonEnabled = false;
                        //         return 'Please enter your phone number';
                        //       }
                        //       if (value.length != 10) {
                        //         // _isButtonEnabled = false;
                        //         return 'Phone number must be exactly 10 digits';
                        //       } else {
                        //         _isButtonEnabled = true;
                        //       }
                        //       if (!RegExp(r'^(98|97|96)\d{8}$')
                        //           .hasMatch(value)) {
                        //         // _isButtonEnabled = false;
                        //         return 'Phone number must start with 98, 97, or 96';
                        //       }

                        //       return null;
                        //     }
                        //     ),

                        TextFormField(
                          controller: _phoneController,
                          enabled: _isPhoneFieldEnabled,
                          decoration: const InputDecoration(
                            labelText: 'Phone',
                            labelStyle: TextStyle(
                                color: Color.fromRGBO(0, 131, 143, 1)),
                            focusedBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                  color: Color.fromRGBO(0, 131, 143, 1)),
                            ),
                            prefixIcon: Icon(Icons.phone,
                                color: Color.fromRGBO(0, 131, 143, 1)),
                          ),
                          keyboardType: TextInputType.phone,
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                            LengthLimitingTextInputFormatter(10),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _isButtonEnabled = value.length == 10;
                            });
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your phone number';
                            }
                            if (value.length != 10) {
                              return 'Phone number must be exactly 10 digits';
                            }
                            if (!RegExp(r'^(98|97|96)\d{8}$').hasMatch(value)) {
                              return 'Phone number must start with 98, 97, or 96';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: size.height * 0.02),
                        buildAddressDropdown(),
                        SizedBox(height: size.height * 0.02),
                        TextFormField(
                          controller: _passwordController,
                          decoration: InputDecoration(
                            labelText: 'Password',
                            labelStyle: const TextStyle(
                              color: Color.fromRGBO(0, 131, 143, 1),
                            ),
                            focusedBorder: const OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Color.fromRGBO(0, 131, 143, 1),
                              ),
                            ),
                            prefixIcon: const Icon(
                              Icons.lock,
                              color: Color.fromRGBO(0, 131, 143, 1),
                            ),
                            suffixIcon: IconButton(
                              icon: Icon(
                                _passwordVisible
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                                color: const Color.fromRGBO(0, 131, 143, 1),
                              ),
                              onPressed: () {
                                setState(() {
                                  _passwordVisible = !_passwordVisible;
                                });
                              },
                            ),
                          ),
                          obscureText: !_passwordVisible,
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a password';
                            } else if (value.length < 8) {
                              return 'Password must be at least 8 characters long';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: size.height * 0.02),
                        TextFormField(
                          controller: _confirmPasswordController,
                          decoration: InputDecoration(
                            labelText: 'Confirm Password',
                            labelStyle: const TextStyle(
                              color: Color.fromRGBO(0, 131, 143, 1),
                            ),
                            focusedBorder: const OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Color.fromRGBO(0, 131, 143, 1),
                              ),
                            ),
                            prefixIcon: const Icon(
                              Icons.lock,
                              color: Color.fromRGBO(0, 131, 143, 1),
                            ),
                            suffixIcon: IconButton(
                              icon: Icon(
                                _confirmPasswordVisible
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                                color: const Color.fromRGBO(0, 131, 143, 1),
                              ),
                              onPressed: () {
                                setState(() {
                                  _confirmPasswordVisible =
                                      !_confirmPasswordVisible;
                                });
                              },
                            ),
                          ),
                          obscureText: !_confirmPasswordVisible,
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please confirm your password';
                            } else if (value != _passwordController.text) {
                              return 'Passwords do not match';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: size.height * 0.02),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: _otpController,
                                enabled: _isOtpFieldEnabled,
                                decoration: const InputDecoration(
                                  labelText: 'OTP',
                                  labelStyle: TextStyle(
                                      color: Color.fromRGBO(0, 131, 143, 1)),
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: Color.fromRGBO(0, 131, 143, 1)),
                                  ),
                                  prefixIcon: Icon(Icons.lock_clock,
                                      color: Color.fromRGBO(0, 131, 143, 1)),
                                ),
                                autovalidateMode:
                                    AutovalidateMode.onUserInteraction,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter the OTP';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            const SizedBox(width: 16.0),
                            // ElevatedButton(
                            //   onPressed: _isButtonEnabled
                            //       ? () async {
                            //           String mobile = _phoneController.text;
                            //           if (mobile.isNotEmpty) {
                            //             showLoadingDialog(context);
                            //             var request = http.Request(
                            //                 'POST',
                            //                 Uri.parse(
                            //                     '$baseUrl/api/v1/auth/$mobile/mobileRegister'));

                            //             var response = await request.send();
                            //             var responseBody = await response.stream
                            //                 .bytesToString();
                            //             Navigator.of(context,
                            //                     rootNavigator: true)
                            //                 .pop();

                            //             if (response.statusCode == 200) {
                            //               setState(() {
                            //                 _isPhoneFieldEnabled = false;
                            //               });
                            //               showOtpInputDialog(context, () {
                            //                 _otpController.text =
                            //                     'OTP Verified Successfully';
                            //               });
                            //             } else {
                            //               errorToast(
                            //                   msg:
                            //                       '${jsonDecode(responseBody)['message']} ');
                            //             }
                            //           } else {
                            //             errorToast(
                            //                 msg: "Please Enter Mobile Number");
                            //           }
                            //         }
                            //       : null,
                            //   style: ElevatedButton.styleFrom(
                            //     backgroundColor:
                            //         const Color.fromRGBO(0, 131, 143, 1),
                            //     minimumSize: const Size(80, 40),
                            //   ),
                            //   child: const Text(
                            //     'Get OTP',
                            //     style: TextStyle(color: Colors.white),
                            //   ),
                            // ),

                            ElevatedButton(
                              onPressed: (_isButtonEnabled && _isGetOtpEnabled)
                                  ? () async {
                                      String mobile = _phoneController.text;
                                      if (mobile.isNotEmpty) {
                                        showLoadingDialog(context);
                                        var request = http.Request(
                                            'POST',
                                            Uri.parse(
                                                '$baseUrl/api/v1/auth/$mobile/mobileRegister'));

                                        var response = await request.send();
                                        var responseBody = await response.stream
                                            .bytesToString();
                                        Navigator.of(context,
                                                rootNavigator: true)
                                            .pop();

                                        if (response.statusCode == 200) {
                                          setState(() {
                                            _isPhoneFieldEnabled = false;
                                          });
                                          showOtpInputDialog(context, () {
                                            setState(() {
                                              _otpController.text =
                                                  'OTP Verified Successfully';
                                              _isGetOtpEnabled = false;
                                              _isOtpFieldEnabled = false;
                                            });
                                          });
                                        } else {
                                          errorToast(
                                              msg:
                                                  '${jsonDecode(responseBody)['message']} ');
                                        }
                                      } else {
                                        errorToast(
                                            msg: "Please Enter Mobile Number");
                                      }
                                    }
                                  : null,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: _isGetOtpEnabled
                                    ? const Color.fromRGBO(0, 131, 143, 1)
                                    : Colors.grey,
                                minimumSize: const Size(80, 40),
                              ),
                              child: const Text(
                                'Get OTP',
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: size.height * 0.02),
                        const Center(
                          child: Text(
                            'Register as:',
                            style: TextStyle(
                              color: Color.fromRGBO(0, 131, 143, 1),
                              fontSize: 16.0,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16.0),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            OutlinedButton(
                              onPressed: () {
                                setState(() {
                                  isServiceProvider = false;
                                });
                              },
                              style: OutlinedButton.styleFrom(
                                side: const BorderSide(
                                    color: Color.fromRGBO(0, 131, 143, 1)),
                                minimumSize:
                                    Size(size.width * 0.3, size.height * 0.05),
                                elevation: 0.0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(32),
                                ),
                                backgroundColor: isServiceProvider
                                    ? Colors.white
                                    : const Color.fromRGBO(0, 131, 143, 1),
                              ),
                              child: Text(
                                'User',
                                style: TextStyle(
                                  color: isServiceProvider
                                      ? const Color.fromRGBO(0, 131, 143, 1)
                                      : Colors.white,
                                ),
                              ),
                            ),
                            SizedBox(width: size.width * 0.03),
                            OutlinedButton(
                              onPressed: () {
                                setState(() {
                                  isServiceProvider = true;
                                });
                              },
                              style: OutlinedButton.styleFrom(
                                side: const BorderSide(
                                    color: Color.fromRGBO(0, 131, 143, 1)),
                                minimumSize:
                                    Size(size.width * 0.3, size.height * 0.05),
                                elevation: 0.0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(32),
                                ),
                                backgroundColor: isServiceProvider
                                    ? const Color.fromRGBO(0, 131, 143, 1)
                                    : Colors.white,
                              ),
                              child: Text(
                                'Service Provider',
                                style: TextStyle(
                                  color: isServiceProvider
                                      ? Colors.white
                                      : const Color.fromRGBO(0, 131, 143, 1),
                                ),
                              ),
                            ),
                          ],
                        ),
                        if (isServiceProvider) ...[
                          const SizedBox(height: 16.0),
                          const SizedBox(height: 16.0),
                          buildSkillsDropdown(),
                          SizedBox(height: size.height * 0.02),
                          // Row(
                          //   children: [
                          //     Expanded(
                          //       child: TextFormField(
                          //         controller: _workingaddressController,
                          //         decoration: const InputDecoration(
                          //           labelText: 'Select your working address',
                          //           labelStyle: TextStyle(
                          //               color: Color.fromRGBO(0, 131, 143, 1)),
                          //           focusedBorder: OutlineInputBorder(
                          //             borderSide: BorderSide(
                          //                 color:
                          //                     Color.fromRGBO(0, 131, 143, 1)),
                          //           ),
                          //         ),
                          //         autovalidateMode:
                          //             AutovalidateMode.onUserInteraction,
                          //         validator: (value) {
                          //           if (value == null || value.isEmpty) {
                          //             return 'Please enter your working address';
                          //           }
                          //           return null;
                          //         },
                          //       ),
                          //     ),
                          //     const SizedBox(width: 16.0),
                          //     ElevatedButton(
                          //       onPressed: () async {
                          //         permission =
                          //             await Geolocator.requestPermission();
                          //         if (permission ==
                          //             LocationPermission.deniedForever) {
                          //           await Geolocator.openAppSettings();
                          //         } else if (permission !=
                          //                 LocationPermission.denied &&
                          //             permission !=
                          //                 LocationPermission.deniedForever) {
                          //           widget.mapController.isMapLoading.value =
                          //               true;
                          //           position =
                          //               await Geolocator.getCurrentPosition(
                          //             desiredAccuracy: LocationAccuracy.high,
                          //           );

                          //           // ignore: unused_local_variable
                          //           LatLng selectedLocation =
                          //               await Get.to(() => OpenMapScreen(
                          //                     completeGoogleMapController:
                          //                         completeGoogleMapController,
                          //                     kGoogle: kGoogle,
                          //                     marker: marker,
                          //                     onpressed: onpressed,
                          //                     onLocationSelected:
                          //                         (LatLng location) {
                          //                       String formattedLocation =
                          //                           '${location.latitude},${location.longitude}';

                          //                       _workingaddressController.text =
                          //                           formattedLocation;
                          //                     },
                          //                   ));
                          //         }
                          //       },
                          //       style: ElevatedButton.styleFrom(
                          //         backgroundColor:
                          //             const Color.fromRGBO(0, 131, 143, 1),
                          //         minimumSize: const Size(80, 40),
                          //       ),
                          //       child: const Text(
                          //         'Open Map',
                          //         style: TextStyle(color: Colors.white),
                          //       ),
                          //     ),
                          //   ],
                          // ),

                          buildWorkingAddressSection(size),
                          SizedBox(height: size.height * 0.02),
                          Row(
                            children: [
                              Expanded(
                                child: _pickedImages.isEmpty
                                    ? TextFormField(
                                        controller: documentController,
                                        readOnly: true,
                                        decoration: const InputDecoration(
                                          labelText: 'Upload your documents',
                                          labelStyle: TextStyle(
                                              color: Color.fromRGBO(
                                                  0, 131, 143, 1)),
                                          focusedBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                                color: Color.fromRGBO(
                                                    0, 131, 143, 1)),
                                          ),
                                        ),
                                      )
                                    : Container(),
                              ),
                              InkWell(
                                onTap: () {
                                  count = 0;
                                  count1 = 0;
                                  citizenshipBack = null;
                                  citizenshipBackUrl = null;
                                  citizenshipFrontUrl = null;
                                  drivingLicenseUrl = null;
                                  nationalIdUrl = null;
                                  citizenshipFront = null;
                                  documentController.text = "";

                                  drivingLicense = null;
                                  nationalId = null;
                                  showModalBottomSheet(
                                    context: context,
                                    builder: (BuildContext context) {
                                      return Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: <Widget>[
                                          ListTile(
                                            title: const Text('Citizenship'),
                                            onTap: () {
                                              Navigator.pop(context);

                                              _pickImage(ImageSource.gallery,
                                                  'citizenship');
                                            },
                                          ),
                                          ListTile(
                                            title:
                                                const Text('Driving License'),
                                            onTap: () {
                                              Navigator.pop(context);
                                              _pickImage(ImageSource.gallery,
                                                  'driving_license');
                                            },
                                          ),
                                          ListTile(
                                            title: const Text('National ID'),
                                            onTap: () {
                                              Navigator.pop(context);
                                              _pickImage(ImageSource.gallery,
                                                  'national_id');
                                            },
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                },
                                child: Icon(
                                  Icons.upload_file,
                                  color: Colors.teal[800],
                                ),
                              ),
                            ],
                          ),
                        ] else
                          ...[],
                        SizedBox(height: size.height * 0.01),
                        buildViewTermsAndConditions(context),
                        buildTermsCheckbox(),
                        AppButton(
                          onPressed: () async {
                            if (!agreedToTerms && !isFormFilled) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                      'Please agree to the Terms and Conditions and fill all the required fields.'),
                                ),
                              );
                              return;
                            }
                            if (!isServiceProvider) {
                              isLoading.value = false;

                              workStatus = false;
                              approval = false;

                              if (!validateUserForm(
                                fullName: _fullNameController.text,
                                mobile: _phoneController.text,
                                address: _addressController.text,
                                password: _passwordController.text,
                                otp: _otpController.text,
                              )) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                        'Please fill in all required fields.'),
                                  ),
                                );
                                return;
                              }
                              registerUser(
                                  fullName: _fullNameController.text,
                                  mobileNumber: _phoneController.text,
                                  address: _addressController.text,
                                  password: _passwordController.text,
                                  workStatus: workStatus,
                                  approval: approval);
                            } else {
                              isLoading.value = true;

                              workStatus = true;
                              approval = true;
                              if (!validateServiceProviderForm(
                                fullName: _fullNameController.text,
                                mobileNumber: _phoneController.text,
                                address: _addressController.text,
                                password: _passwordController.text,
                                jobDetails: _skillController.text,
                                latitude: _workingaddressController.text,
                                otp: _otpController.text,
                              )) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                        'Please fill in all required fields.'),
                                  ),
                                );
                                return;
                              }
                              await registerService(
                                fullName: _fullNameController.text,
                                mobileNumber: _phoneController.text,
                                address: _addressController.text,
                                password: _passwordController.text,
                                jobDetails: {
                                  "jobCategoryId":
                                      int.parse(selectedJobCategoryId!),
                                  "serviceIds": selectedServiceIds
                                      .map(int.parse)
                                      .toList(),
                                },
                                latitude: _workingaddressController.text,
                                workStatus: workStatus,
                                approval: approval,
                                citizenshipFront: citizenshipFront,
                                citizenshipBack: citizenshipBack,
                                drivingLicense: drivingLicense,
                                nationalId: nationalId,
                              );
                            }
                            if (count1 != 5 && count1 != 6 && count == 0) {
//Payment screen call
//Id from resgitered User

                              Navigator.pushReplacement(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const LoginScreen(),
                                ),
                              );
                            } else if (count1 == 5) {
                              if (count != 0) {
                                errorToast(
                                    msg:
                                        ' Document Uploading failed \n Please try again');
                              }
                            } else {
                              successToast(msg: 'Registered Successfully');
                            }
                          },
                          name: 'Sign Up',
                        ),
                        SizedBox(height: size.height * 0.08),
                      ]),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Sending OTP'),
            ],
          ),
        );
      },
    );
  }

  // Widget buildWorkingAddressSection(Size size) {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       Row(
  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         children: [
  //           const Text(
  //             'Working Address',
  //             style: TextStyle(
  //               color: Color.fromRGBO(0, 131, 143, 1),
  //               fontWeight: FontWeight.bold,
  //               fontSize: 16.0,
  //             ),
  //           ),
  //           // Open Map button - always visible
  //           ElevatedButton.icon(
  //             onPressed: () => _openMapScreen(),
  //             icon: const Icon(Icons.map, color: Colors.white, size: 16),
  //             label: const Text('Open Map'),
  //             style: ElevatedButton.styleFrom(
  //               backgroundColor: const Color.fromRGBO(0, 131, 143, 1),
  //               shape: RoundedRectangleBorder(
  //                 borderRadius: BorderRadius.circular(8.0),
  //               ),
  //               minimumSize: const Size(100, 36),
  //             ),
  //           ),
  //         ],
  //       ),
  //       const SizedBox(height: 8.0),

  //       // Hidden field to store coordinates
  //       Opacity(
  //         opacity: 0,
  //         child: Container(
  //           height: 0,
  //           child: TextFormField(
  //             controller: _workingaddressController,
  //             readOnly: true,
  //           ),
  //         ),
  //       ),

  //       // Show mini map or placeholder
  //       _workingaddressController.text.isNotEmpty
  //           ? buildMiniMap()
  //           : GestureDetector(
  //               onTap: () => _openMapScreen(),
  //               child: Container(
  //                 width: double.infinity,
  //                 height: 120,
  //                 decoration: BoxDecoration(
  //                   color: Colors.grey.shade100,
  //                   borderRadius: BorderRadius.circular(12.0),
  //                   border: Border.all(
  //                     color: Colors.grey.shade400,
  //                     width: 1.0,
  //                   ),
  //                 ),
  //                 child: const Center(
  //                   child: Column(
  //                     mainAxisAlignment: MainAxisAlignment.center,
  //                     children: [
  //                       Icon(
  //                         Icons.location_on,
  //                         color: Color.fromRGBO(0, 131, 143, 1),
  //                         size: 36,
  //                       ),
  //                       SizedBox(height: 8),
  //                       Text(
  //                         'No location selected',
  //                         style: TextStyle(
  //                           color: Colors.grey,
  //                           fontSize: 14,
  //                         ),
  //                       ),
  //                       SizedBox(height: 4),
  //                       Text(
  //                         'Tap to select location',
  //                         style: TextStyle(
  //                           color: Color.fromRGBO(0, 131, 143, 1),
  //                           fontSize: 12,
  //                           fontWeight: FontWeight.w500,
  //                         ),
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //               ),
  //             ),
  //     ],
  //   );
  // }

  // Widget buildMiniMap() {
  //   LatLng? userLocation;
  //   if (_workingaddressController.text.isNotEmpty) {
  //     try {
  //       List<String> coordinates = _workingaddressController.text.split(',');
  //       if (coordinates.length == 2) {
  //         double lat = double.parse(coordinates[0].trim());
  //         double lng = double.parse(coordinates[1].trim());
  //         userLocation = LatLng(lat, lng);
  //       }
  //     } catch (e) {
  //       consolelog("Error parsing coordinates: $e");
  //     }
  //   }

  //   return GestureDetector(
  //     onTap: () => _openMapScreen(),
  //     child: Container(
  //       margin: const EdgeInsets.only(top: 8.0),
  //       height: 180,
  //       decoration: BoxDecoration(
  //         borderRadius: BorderRadius.circular(12.0),
  //         border: Border.all(
  //           color: const Color.fromRGBO(0, 131, 143, 0.5),
  //           width: 1.5,
  //         ),
  //         boxShadow: [
  //           BoxShadow(
  //             color: Colors.grey.withOpacity(0.2),
  //             spreadRadius: 1,
  //             blurRadius: 4,
  //             offset: const Offset(0, 2),
  //           ),
  //         ],
  //       ),
  //       child: ClipRRect(
  //         borderRadius: BorderRadius.circular(12.0),
  //         child: Stack(
  //           children: [
  //             // If using Google Maps
  //             userLocation != null
  //                 ? GoogleMap(
  //                     initialCameraPosition: CameraPosition(
  //                       target: LatLng(
  //                           userLocation.latitude, userLocation.longitude),
  //                       zoom: 15,
  //                     ),
  //                     zoomControlsEnabled: false,
  //                     mapToolbarEnabled: false,
  //                     myLocationEnabled: true,
  //                     myLocationButtonEnabled: false,
  //                     markers: {
  //                       Marker(
  //                         markerId: const MarkerId('userLocation'),
  //                         position: LatLng(
  //                             userLocation.latitude, userLocation.longitude),
  //                         infoWindow:
  //                             const InfoWindow(title: 'Your Working Location'),
  //                       ),
  //                     },
  //                     onMapCreated: (GoogleMapController controller) {
  //                       // Store controller if needed
  //                     },
  //                   )
  //                 : const Center(
  //                     child: Text(
  //                       'No location selected',
  //                       style: TextStyle(color: Colors.grey),
  //                     ),
  //                   ),

  //             // Coordinates display
  //             if (userLocation != null)
  //               Positioned(
  //                 bottom: 8,
  //                 left: 8,
  //                 right: 8,
  //                 child: Container(
  //                   padding:
  //                       const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
  //                   decoration: BoxDecoration(
  //                     color: Colors.white.withOpacity(0.9),
  //                     borderRadius: BorderRadius.circular(8),
  //                     boxShadow: [
  //                       BoxShadow(
  //                         color: Colors.black.withOpacity(0.1),
  //                         blurRadius: 2,
  //                         offset: const Offset(0, 1),
  //                       ),
  //                     ],
  //                   ),
  //                   child: Text(
  //                     'Location: ${userLocation.latitude.toStringAsFixed(6)}, ${userLocation.longitude.toStringAsFixed(6)}',
  //                     style: const TextStyle(
  //                       fontSize: 12,
  //                       fontWeight: FontWeight.w500,
  //                       color: Color.fromRGBO(0, 131, 143, 1),
  //                     ),
  //                     textAlign: TextAlign.center,
  //                   ),
  //                 ),
  //               ),
  //             Positioned(
  //               top: 8,
  //               right: 8,
  //               child: Container(
  //                 decoration: BoxDecoration(
  //                   color: Colors.white,
  //                   borderRadius: BorderRadius.circular(8.0),
  //                   boxShadow: [
  //                     BoxShadow(
  //                       color: Colors.black.withOpacity(0.1),
  //                       spreadRadius: 1,
  //                       blurRadius: 2,
  //                       offset: const Offset(0, 1),
  //                     ),
  //                   ],
  //                 ),
  //                 child: IconButton(
  //                   icon: const Icon(
  //                     Icons.fullscreen,
  //                     color: Color.fromRGBO(0, 131, 143, 1),
  //                   ),
  //                   onPressed: () => _openMapScreen(),
  //                   tooltip: 'Expand Map',
  //                 ),
  //               ),
  //             ),

  //             // Select location button
  //             Positioned(
  //               top: 8,
  //               left: 8,
  //               child: Container(
  //                 decoration: BoxDecoration(
  //                   color: Colors.white,
  //                   borderRadius: BorderRadius.circular(8.0),
  //                   boxShadow: [
  //                     BoxShadow(
  //                       color: Colors.black.withOpacity(0.1),
  //                       spreadRadius: 1,
  //                       blurRadius: 2,
  //                       offset: const Offset(0, 1),
  //                     ),
  //                   ],
  //                 ),
  //                 child: IconButton(
  //                   icon: const Icon(
  //                     Icons.add_location_alt,
  //                     color: Color.fromRGBO(0, 131, 143, 1),
  //                   ),
  //                   onPressed: () => _openMapScreen(),
  //                   tooltip: 'Select Location',
  //                 ),
  //               ),
  //             ),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }

  // // Extracted method to handle map opening logic
  // Future<void> _openMapScreen() async {
  //   permission = await Geolocator.requestPermission();
  //   if (permission == LocationPermission.deniedForever) {
  //     await Geolocator.openAppSettings();
  //   } else if (permission != LocationPermission.denied &&
  //       permission != LocationPermission.deniedForever) {
  //     widget.mapController.isMapLoading.value = true;
  //     position = await Geolocator.getCurrentPosition(
  //         desiredAccuracy: LocationAccuracy.high);

  //     await Get.to(() => OpenMapScreen(
  //           completeGoogleMapController: completeGoogleMapController,
  //           kGoogle: kGoogle,
  //           marker: marker,
  //           onpressed: onpressed,
  //           onLocationSelected: (LatLng location) {
  //             String formattedLocation =
  //                 '${location.latitude},${location.longitude}';
  //             _workingaddressController.text = formattedLocation;
  //             setState(() {}); // Refresh UI to show map
  //           },
  //         ));
  //   }
  // }

  Widget buildWorkingAddressSection(Size size) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Working Address',
              style: TextStyle(
                color: Color.fromRGBO(0, 131, 143, 1),
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
            // Open Map button - always visible
            ElevatedButton.icon(
              onPressed: () => _openMapScreen(),
              icon: const Icon(Icons.map, color: Colors.white, size: 16),
              label: const Text('Open Map'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color.fromRGBO(0, 131, 143, 1),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                minimumSize: const Size(100, 36),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8.0),

        // Hidden field to store coordinates
        Opacity(
          opacity: 0,
          child: Container(
            height: 0,
            child: TextFormField(
              controller: _workingaddressController,
              readOnly: true,
            ),
          ),
        ),

        // Show mini map or placeholder
        _workingaddressController.text.isNotEmpty
            ? buildMiniMap()
            : GestureDetector(
                onTap: () => _openMapScreen(),
                child: Container(
                  width: double.infinity,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12.0),
                    border: Border.all(
                      color: Colors.grey.shade400,
                      width: 1.0,
                    ),
                  ),
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.location_on,
                          color: Color.fromRGBO(0, 131, 143, 1),
                          size: 36,
                        ),
                        SizedBox(height: 8),
                        Text(
                          'No location selected',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 14,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          'Tap to select location',
                          style: TextStyle(
                            color: Color.fromRGBO(0, 131, 143, 1),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
      ],
    );
  }

  Widget buildMiniMap() {
    LatLng? userLocation;
    if (_workingaddressController.text.isNotEmpty) {
      try {
        List<String> coordinates = _workingaddressController.text.split(',');
        if (coordinates.length == 2) {
          double lat = double.parse(coordinates[0].trim());
          double lng = double.parse(coordinates[1].trim());
          userLocation = LatLng(lat, lng);
        }
      } catch (e) {
        consolelog("Error parsing coordinates: $e");
      }
    }

    return GestureDetector(
      onTap: () => _openMapScreen(),
      child: Container(
        margin: const EdgeInsets.only(top: 8.0),
        height: 180,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(
            color: const Color.fromRGBO(0, 131, 143, 0.5),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12.0),
          child: Stack(
            children: [
              // If using Google Maps
              userLocation != null
                  ? GoogleMap(
                      initialCameraPosition: CameraPosition(
                        target: LatLng(
                            userLocation.latitude, userLocation.longitude),
                        zoom: 15,
                      ),
                      zoomControlsEnabled: false,
                      mapToolbarEnabled: false,
                      myLocationEnabled: true,
                      myLocationButtonEnabled: false,
                      markers: {
                        Marker(
                          markerId: const MarkerId('userLocation'),
                          position: LatLng(
                              userLocation.latitude, userLocation.longitude),
                          infoWindow:
                              const InfoWindow(title: 'Your Working Location'),
                        ),
                      },
                      onMapCreated: (GoogleMapController controller) {
                        // Store controller if needed
                      },
                    )
                  : const Center(
                      child: Text(
                        'No location selected',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ),

              // Coordinates display
              if (userLocation != null)
                Positioned(
                  bottom: 8,
                  left: 8,
                  right: 8,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Text(
                      'Location: ${userLocation.latitude.toStringAsFixed(6)}, ${userLocation.longitude.toStringAsFixed(6)}',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Color.fromRGBO(0, 131, 143, 1),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8.0),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        spreadRadius: 1,
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: const Icon(
                      Icons.fullscreen,
                      color: Color.fromRGBO(0, 131, 143, 1),
                    ),
                    onPressed: () => _openMapScreen(),
                    tooltip: 'Expand Map',
                  ),
                ),
              ),

              // Select location button
              Positioned(
                top: 8,
                left: 8,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8.0),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        spreadRadius: 1,
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: const Icon(
                      Icons.add_location_alt,
                      color: Color.fromRGBO(0, 131, 143, 1),
                    ),
                    onPressed: () => _openMapScreen(),
                    tooltip: 'Select Location',
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

// Updated _openMapScreen method with proper caching
  Future<void> _openMapScreen() async {
    permission = await Geolocator.requestPermission();
    if (permission == LocationPermission.deniedForever) {
      await Geolocator.openAppSettings();
    } else if (permission != LocationPermission.denied &&
        permission != LocationPermission.deniedForever) {
      widget.mapController.isMapLoading.value = true;
      position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);

      await Get.to(() => OpenMapScreen(
            completeGoogleMapController: completeGoogleMapController,
            kGoogle: kGoogle,
            initialOnpressed: false,
            marker: marker,
            onpressed: onpressed,
            onLocationSelected: (LatLng location) async {
              String formattedLocation =
                  '${location.latitude},${location.longitude}';
              _workingaddressController.text = formattedLocation;
              await _saveLocationToCache(location);

              setState(() {});
            },
          ));
    }
  }
  Future<void> _saveLocationToCache(LatLng location) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String locationString = '${location.latitude},${location.longitude}';
      await prefs.setString('working_address_location', locationString);
      consolelog('Location saved to cache: $locationString');
    } catch (e) {
      consolelog('Error saving location to cache: $e');
    }
  }

// Add this method to load cached location when widget initializes
  Future<void> _loadCachedLocation() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? cachedLocation = prefs.getString('working_address_location');

      if (cachedLocation != null && cachedLocation.isNotEmpty) {
        _workingaddressController.text = cachedLocation;
        setState(() {}); // Refresh UI to show cached location
        consolelog('Loaded cached location: $cachedLocation');
      }
    } catch (e) {
      consolelog('Error loading cached location: $e');
    }
  }
  Future<void> saveWorkingAddressPermanently() async {
    try {
      if (_workingaddressController.text.isNotEmpty) {
        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setString(
            'saved_working_address', _workingaddressController.text);
        consolelog(
            'Working address saved permanently: ${_workingaddressController.text}');
      }
    } catch (e) {
      consolelog('Error saving working address permanently: $e');
    }
  }

// Add this method to load permanently saved address
  Future<void> _loadSavedWorkingAddress() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? savedAddress = prefs.getString('saved_working_address');

      if (savedAddress != null && savedAddress.isNotEmpty) {
        _workingaddressController.text = savedAddress;
        setState(() {});
        consolelog('Loaded saved working address: $savedAddress');
      }
    } catch (e) {
      consolelog('Error loading saved working address: $e');
    }
  }

  // Widget buildWorkingAddressSection(Size size) {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       Row(
  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         children: [
  //           const Text(
  //             'Working Address',
  //             style: TextStyle(
  //               color: Color.fromRGBO(0, 131, 143, 1),
  //               fontWeight: FontWeight.bold,
  //               fontSize: 16.0,
  //             ),
  //           ),
  //           // Open Map button - always visible
  //           ElevatedButton.icon(
  //             onPressed: () async {
  //               permission = await Geolocator.requestPermission();
  //               if (permission == LocationPermission.deniedForever) {
  //                 await Geolocator.openAppSettings();
  //               } else if (permission != LocationPermission.denied &&
  //                   permission != LocationPermission.deniedForever) {
  //                 widget.mapController.isMapLoading.value = true;
  //                 position = await Geolocator.getCurrentPosition(
  //                     desiredAccuracy: LocationAccuracy.high);

  //                 await Get.to(() => OpenMapScreen(
  //                       completeGoogleMapController:
  //                           completeGoogleMapController,
  //                       kGoogle: kGoogle,
  //                       marker: marker,
  //                       onpressed: onpressed,
  //                       onLocationSelected: (LatLng location) {
  //                         String formattedLocation =
  //                             '${location.latitude},${location.longitude}';
  //                         _workingaddressController.text = formattedLocation;
  //                         setState(() {}); // Refresh UI to show map
  //                       },
  //                     ));
  //               }
  //             },
  //             icon: const Icon(Icons.map, color: Colors.white, size: 16),
  //             label: const Text('Open Map'),
  //             style: ElevatedButton.styleFrom(
  //               backgroundColor: const Color.fromRGBO(0, 131, 143, 1),
  //               shape: RoundedRectangleBorder(
  //                 borderRadius: BorderRadius.circular(8.0),
  //               ),
  //               minimumSize: const Size(100, 36),
  //             ),
  //           ),
  //         ],
  //       ),
  //       const SizedBox(height: 8.0),

  //       // Hidden field to store coordinates
  //       Opacity(
  //         opacity: 0,
  //         child: Container(
  //           height: 0,
  //           child: TextFormField(
  //             controller: _workingaddressController,
  //             readOnly: true,
  //           ),
  //         ),
  //       ),

  //       // Show mini map or placeholder
  //       _workingaddressController.text.isNotEmpty
  //           ? buildMiniMap()
  //           : Container(
  //               width: double.infinity,
  //               height: 120,
  //               decoration: BoxDecoration(
  //                 color: Colors.grey.shade100,
  //                 borderRadius: BorderRadius.circular(12.0),
  //                 border: Border.all(
  //                   color: Colors.grey.shade400,
  //                   width: 1.0,
  //                 ),
  //               ),
  //               child: const Center(
  //                 child: Column(
  //                   mainAxisAlignment: MainAxisAlignment.center,
  //                   children: [
  //                     Icon(
  //                       Icons.location_on,
  //                       color: Color.fromRGBO(0, 131, 143, 1),
  //                       size: 36,
  //                     ),
  //                     SizedBox(height: 8),
  //                     Text(
  //                       'No location selected',
  //                       style: TextStyle(
  //                         color: Colors.grey,
  //                         fontSize: 14,
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //               ),
  //             ),
  //     ],
  //   );
  // }

  // Widget buildMiniMap() {
  //   LatLng? userLocation;
  //   if (_workingaddressController.text.isNotEmpty) {
  //     try {
  //       List<String> coordinates = _workingaddressController.text.split(',');
  //       if (coordinates.length == 2) {
  //         double lat = double.parse(coordinates[0].trim());
  //         double lng = double.parse(coordinates[1].trim());
  //         userLocation = LatLng(lat, lng);
  //       }
  //     } catch (e) {
  //       consolelog("Error parsing coordinates: $e");
  //     }
  //   }

  //   return Container(
  //     margin: const EdgeInsets.only(top: 8.0),
  //     height: 180,
  //     decoration: BoxDecoration(
  //       borderRadius: BorderRadius.circular(12.0),
  //       border: Border.all(
  //         color: const Color.fromRGBO(0, 131, 143, 0.5),
  //         width: 1.5,
  //       ),
  //       boxShadow: [
  //         BoxShadow(
  //           color: Colors.grey.withOpacity(0.2),
  //           spreadRadius: 1,
  //           blurRadius: 4,
  //           offset: const Offset(0, 2),
  //         ),
  //       ],
  //     ),
  //     child: ClipRRect(
  //       borderRadius: BorderRadius.circular(12.0),
  //       child: Stack(
  //         children: [
  //           // If using Google Maps
  //           userLocation != null
  //               ? GoogleMap(
  //                   initialCameraPosition: CameraPosition(
  //                     target:
  //                         LatLng(userLocation.latitude, userLocation.longitude),
  //                     zoom: 15,
  //                   ),
  //                   zoomControlsEnabled: false,
  //                   mapToolbarEnabled: false,
  //                   myLocationEnabled: true,
  //                   myLocationButtonEnabled: false,
  //                   markers: {
  //                     Marker(
  //                       markerId: const MarkerId('userLocation'),
  //                       position: LatLng(
  //                           userLocation.latitude, userLocation.longitude),
  //                       infoWindow:
  //                           const InfoWindow(title: 'Your Working Location'),
  //                     ),
  //                   },
  //                   onMapCreated: (GoogleMapController controller) {
  //                     // Store controller if needed
  //                   },
  //                 )
  //               : const Center(
  //                   child: Text(
  //                     'No location selected',
  //                     style: TextStyle(color: Colors.grey),
  //                   ),
  //                 ),

  //           // Coordinates display
  //           if (userLocation != null)
  //             Positioned(
  //               bottom: 8,
  //               left: 8,
  //               right: 8,
  //               child: Container(
  //                 padding:
  //                     const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
  //                 decoration: BoxDecoration(
  //                   color: Colors.white.withOpacity(0.9),
  //                   borderRadius: BorderRadius.circular(8),
  //                   boxShadow: [
  //                     BoxShadow(
  //                       color: Colors.black.withOpacity(0.1),
  //                       blurRadius: 2,
  //                       offset: const Offset(0, 1),
  //                     ),
  //                   ],
  //                 ),
  //                 child: Text(
  //                   'Location: ${userLocation.latitude.toStringAsFixed(6)}, ${userLocation.longitude.toStringAsFixed(6)}',
  //                   style: const TextStyle(
  //                     fontSize: 12,
  //                     fontWeight: FontWeight.w500,
  //                     color: Color.fromRGBO(0, 131, 143, 1),
  //                   ),
  //                   textAlign: TextAlign.center,
  //                 ),
  //               ),
  //             ),
  //           Positioned(
  //             top: 8,
  //             right: 8,
  //             child: Container(
  //               decoration: BoxDecoration(
  //                 color: Colors.white,
  //                 borderRadius: BorderRadius.circular(8.0),
  //                 boxShadow: [
  //                   BoxShadow(
  //                     color: Colors.black.withOpacity(0.1),
  //                     spreadRadius: 1,
  //                     blurRadius: 2,
  //                     offset: const Offset(0, 1),
  //                   ),
  //                 ],
  //               ),
  //               child: IconButton(
  //                 icon: const Icon(
  //                   Icons.fullscreen,
  //                   color: Color.fromRGBO(0, 131, 143, 1),
  //                 ),
  //                 onPressed: () async {
  //                   permission = await Geolocator.requestPermission();
  //                   if (permission == LocationPermission.deniedForever) {
  //                     await Geolocator.openAppSettings();
  //                   } else if (permission != LocationPermission.denied &&
  //                       permission != LocationPermission.deniedForever) {
  //                     widget.mapController.isMapLoading.value = true;
  //                     position = await Geolocator.getCurrentPosition(
  //                         desiredAccuracy: LocationAccuracy.high);

  //                     LatLng selectedLocation =
  //                         await Get.to(() => OpenMapScreen(
  //                               completeGoogleMapController:
  //                                   completeGoogleMapController,
  //                               kGoogle: kGoogle,
  //                               marker: marker,
  //                               onpressed: onpressed,
  //                               onLocationSelected: (LatLng location) {
  //                                 String formattedLocation =
  //                                     '${location.latitude},${location.longitude}';
  //                                 _workingaddressController.text =
  //                                     formattedLocation;
  //                               },
  //                             ));
  //                   }
  //                 },
  //                 tooltip: 'Expand Map',
  //               ),
  //             ),
  //           ),

  //           // Select location button
  //           Positioned(
  //             top: 8,
  //             left: 8,
  //             child: Container(
  //               decoration: BoxDecoration(
  //                 color: Colors.white,
  //                 borderRadius: BorderRadius.circular(8.0),
  //                 boxShadow: [
  //                   BoxShadow(
  //                     color: Colors.black.withOpacity(0.1),
  //                     spreadRadius: 1,
  //                     blurRadius: 2,
  //                     offset: const Offset(0, 1),
  //                   ),
  //                 ],
  //               ),
  //               child: IconButton(
  //                 icon: const Icon(
  //                   Icons.add_location_alt,
  //                   color: Color.fromRGBO(0, 131, 143, 1),
  //                 ),
  //                 onPressed: () async {
  //                   permission = await Geolocator.requestPermission();
  //                   if (permission == LocationPermission.deniedForever) {
  //                     await Geolocator.openAppSettings();
  //                   } else if (permission != LocationPermission.denied &&
  //                       permission != LocationPermission.deniedForever) {
  //                     widget.mapController.isMapLoading.value = true;
  //                     position = await Geolocator.getCurrentPosition(
  //                         desiredAccuracy: LocationAccuracy.high);

  //                     LatLng selectedLocation =
  //                         await Get.to(() => OpenMapScreen(
  //                               completeGoogleMapController:
  //                                   completeGoogleMapController,
  //                               kGoogle: kGoogle,
  //                               marker: marker,
  //                               onpressed: onpressed,
  //                               onLocationSelected: (LatLng location) {
  //                                 String formattedLocation =
  //                                     '${location.latitude},${location.longitude}';
  //                                 _workingaddressController.text =
  //                                     formattedLocation;
  //                               },
  //                             ));
  //                   }
  //                 },
  //                 tooltip: 'Select Location',
  //               ),
  //             ),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  // Widget buildSkillsDropdown() {
  //   return TextFormField(
  //     onTap: () {
  //       showSkillsDialog();
  //     },
  //     controller: _skillController,
  //     readOnly: true,
  //     decoration: const InputDecoration(
  //       labelText: 'Select Your Job Field',
  //       labelStyle: TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
  //       focusedBorder: OutlineInputBorder(
  //         borderSide: BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
  //       ),
  //     ),
  //     validator: (value) {
  //       if (value == null || value.isEmpty) {
  //         return 'Please select your skills';
  //       }
  //       return null;
  //     },
  //   );
  // }

  // void showSkillsDialog() {
  //   showDialog(
  //     context: context,
  //     builder: (BuildContext context) {
  //       return AlertDialog(
  //         title: const Text('Select Your Job Field'),
  //         content: SizedBox(
  //           height: 700,
  //           child: Scrollbar(
  //             child: SingleChildScrollView(
  //               child: Column(
  //                 mainAxisSize: MainAxisSize.min,
  //                 children: [
  //                   buildSkillsCheckboxList(),
  //                 ],
  //               ),
  //             ),
  //           ),
  //         ),
  //       );
  //     },
  //   );
  // }

  // Widget buildSkillsCheckboxList() {
  //   return FutureBuilder<List<Map<String, dynamic>>>(
  //     future: fetchSkills(),
  //     builder: (context, snapshot) {
  //       if (snapshot.connectionState == ConnectionState.waiting) {
  //         return const CircularProgressIndicator();
  //       } else if (snapshot.hasError) {
  //         return Text('Error: ${snapshot.error}');
  //       } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
  //         return const Text('No skills available');
  //       }

  //       List<Map<String, dynamic>> skills = snapshot.data!;
  //       return Column(
  //         children: skills.map((skill) {
  //           String categoryId = skill['categoryId']!;
  //           String categoryTitle = skill['categoryTitle']!;
  //           return ListTile(
  //             title: Text(categoryTitle),
  //             onTap: () {
  //               setState(() {
  //                 selectedJobCategoryId = categoryId;
  //                 if (selectedMainSkill != categoryTitle) {
  //                   selectedSubSkills[selectedMainSkill] = [];
  //                   selectedSkills = [];
  //                   _updateTextFieldText('');
  //                 }
  //                 selectedMainSkill = categoryTitle;
  //                 Navigator.pop(context);
  //                 showSubSkillsDialog(categoryId, categoryTitle);
  //               });
  //             },
  //           );
  //         }).toList(),
  //       );
  //     },
  //   );
  // }

  // void _updateTextFieldText(String mainSkill) {
  //   if (selectedSkills.isNotEmpty) {
  //     _skillController.text = '$mainSkill: ${selectedSkills.join(', ')}';
  //   } else {
  //     _skillController.text = '';
  //   }
  // }

  // void showSubSkillsDialog(String categoryId, String mainSkill) {
  //   selectedSkills = List.from(selectedSubSkills[mainSkill] ?? []);

  //   showDialog(
  //     context: context,
  //     builder: (BuildContext context) {
  //       return FutureBuilder<List<Map<String, dynamic>>>(
  //         future: fetchSubSkills(categoryId),
  //         builder: (context, snapshot) {
  //           if (snapshot.connectionState == ConnectionState.waiting) {
  //             return const AlertDialog(
  //               title: Text('Loading Sub Skills...'),
  //               content: CircularProgressIndicator(),
  //             );
  //           } else if (snapshot.hasError) {
  //             return AlertDialog(
  //               title: const Text('Error'),
  //               content: Text('Error: ${snapshot.error}'),
  //             );
  //           } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
  //             return const AlertDialog(
  //               title: Text('No Sub Skills Available'),
  //               content: Text('No sub skills available for this skill.'),
  //             );
  //           }

  //           List<Map<String, dynamic>> subSkillsList = snapshot.data!;

  //           return StatefulBuilder(
  //             builder: (context, setState) {
  //               return AlertDialog(
  //                 title: const Text('Select Sub Skills'),
  //                 content: SizedBox(
  //                   height: 300,
  //                   child: Scrollbar(
  //                     child: SingleChildScrollView(
  //                       child: Column(
  //                         mainAxisSize: MainAxisSize.min,
  //                         children: subSkillsList.map((subSkill) {
  //                           return CheckboxListTile(
  //                             title: Text(subSkill['name']),
  //                             value: selectedSkills.contains(subSkill['name']),
  //                             onChanged: (value) {
  //                               setState(() {
  //                                 if (value != null) {
  //                                   if (value) {
  //                                     selectedSkills.add(subSkill['name']);
  //                                   } else {
  //                                     selectedSkills.remove(subSkill['name']);
  //                                   }
  //                                 }
  //                                 _updateTextFieldText(mainSkill);
  //                               });
  //                             },
  //                             controlAffinity: ListTileControlAffinity.leading,
  //                           );
  //                         }).toList(),
  //                       ),
  //                     ),
  //                   ),
  //                 ),
  //                 actions: [
  //                   Row(
  //                     mainAxisAlignment: MainAxisAlignment.center,
  //                     children: [
  //                       ElevatedButton(
  //                         onPressed: () {
  //                           setState(() {
  //                             selectedSubSkills[mainSkill] =
  //                                 List.from(selectedSkills);
  //                             selectedServiceIds = subSkillsList
  //                                 .where((subSkill) =>
  //                                     selectedSkills.contains(subSkill['name']))
  //                                 .map((subSkill) => subSkill['id'].toString())
  //                                 .toList();
  //                             _updateTextFieldText(mainSkill);
  //                           });
  //                           Navigator.pop(context);
  //                         },
  //                         style: ElevatedButton.styleFrom(
  //                           backgroundColor:
  //                               const Color.fromRGBO(0, 131, 143, 1),
  //                         ),
  //                         child: const SizedBox(
  //                           width: 80,
  //                           height: 40,
  //                           child: Center(
  //                             child: Text(
  //                               'Done',
  //                               style: TextStyle(color: Colors.white),
  //                             ),
  //                           ),
  //                         ),
  //                       ),
  //                     ],
  //                   ),
  //                 ],
  //               );
  //             },
  //           );
  //         },
  //       );
  //     },
  //   );
  // }

  Widget buildSkillsDropdown() {
  return Container(
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withOpacity(0.1),
          spreadRadius: 1,
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: TextFormField(
      onTap: () {
        showSkillsDialog();
      },
      controller: _skillController,
      readOnly: true,
      style: TextStyle(
        fontSize: MediaQuery.of(context).size.width * 0.04,
        fontWeight: FontWeight.w500,
        color: Colors.black87,
      ),
      decoration: InputDecoration(
        labelText: 'Select Your Job Field',
        labelStyle: TextStyle(
          color: const Color.fromRGBO(0, 131, 143, 1),
          fontSize: MediaQuery.of(context).size.width * 0.04,
          fontWeight: FontWeight.w500,
        ),
        hintText: 'Tap to select your skills',
        hintStyle: TextStyle(
          color: Colors.grey.shade500,
          fontSize: MediaQuery.of(context).size.width * 0.035,
        ),
        filled: true,
        fillColor: Colors.white,
        prefixIcon: Icon(
          Icons.work_outline,
          color: const Color.fromRGBO(0, 131, 143, 1),
          size: MediaQuery.of(context).size.width * 0.06,
        ),
        suffixIcon: Icon(
          Icons.keyboard_arrow_down,
          color: const Color.fromRGBO(0, 131, 143, 1),
          size: MediaQuery.of(context).size.width * 0.06,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Colors.grey.shade300,
            width: 1.5,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color.fromRGBO(0, 131, 143, 1),
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Colors.red,
            width: 1.5,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Colors.red,
            width: 2,
          ),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: MediaQuery.of(context).size.width * 0.04,
          vertical: MediaQuery.of(context).size.height * 0.02,
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please select your skills';
        }
        return null;
      },
    ),
  );
}

void showSkillsDialog() {
  showDialog(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext context) {
      return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        elevation: 8,
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
            maxWidth: MediaQuery.of(context).size.width * 0.9,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                spreadRadius: 2,
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with back button
              Container(
                padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.05),
                decoration: const BoxDecoration(
                  color: Color.fromRGBO(0, 131, 143, 1),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Container(
                        padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.arrow_back_ios,
                          color: Colors.white,
                          size: MediaQuery.of(context).size.width * 0.05,
                        ),
                      ),
                    ),
                    SizedBox(width: MediaQuery.of(context).size.width * 0.04),
                    Expanded(
                      child: Text(
                        'Select Your Job Field',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: MediaQuery.of(context).size.width * 0.05,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Content
              Flexible(
                child: Container(
                  padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.05),
                  child: Scrollbar(
                    thumbVisibility: true,
                    child: SingleChildScrollView(
                      child: buildSkillsCheckboxList(),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    },
  );
}

Widget buildSkillsCheckboxList() {
  return FutureBuilder<List<Map<String, dynamic>>>(
    future: fetchSkills(),
    builder: (context, snapshot) {
      if (snapshot.connectionState == ConnectionState.waiting) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.25,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: MediaQuery.of(context).size.width * 0.08,
                  height: MediaQuery.of(context).size.width * 0.08,
                  child: const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Color.fromRGBO(0, 131, 143, 1),
                    ),
                    strokeWidth: 3,
                  ),
                ),
                SizedBox(height: MediaQuery.of(context).size.height * 0.02),
                Text(
                  'Loading skills...',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width * 0.04,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        );
      } else if (snapshot.hasError) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.25,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: MediaQuery.of(context).size.width * 0.12,
                  color: Colors.red,
                ),
                SizedBox(height: MediaQuery.of(context).size.height * 0.02),
                Text(
                  'Error: ${snapshot.error}',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width * 0.04,
                    color: Colors.red,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.25,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.work_off,
                  size: MediaQuery.of(context).size.width * 0.12,
                  color: Colors.grey,
                ),
                SizedBox(height: MediaQuery.of(context).size.height * 0.02),
                Text(
                  'No skills available',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width * 0.04,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        );
      }

      List<Map<String, dynamic>> skills = snapshot.data!;
      return Column(
        children: skills.map((skill) {
          String categoryId = skill['categoryId']!;
          String categoryTitle = skill['categoryTitle']!;
          
          return Container(
            margin: EdgeInsets.only(bottom: MediaQuery.of(context).size.height * 0.015),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey.shade200,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.08),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ListTile(
              contentPadding: EdgeInsets.symmetric(
                horizontal: MediaQuery.of(context).size.width * 0.05,
                vertical: MediaQuery.of(context).size.height * 0.01,
              ),
              leading: Container(
                padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02),
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(0, 131, 143, 1).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.category,
                  color: const Color.fromRGBO(0, 131, 143, 1),
                  size: MediaQuery.of(context).size.width * 0.05,
                ),
              ),
              title: Text(
                categoryTitle,
                style: TextStyle(
                  fontSize: MediaQuery.of(context).size.width * 0.04,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              trailing: Icon(
                Icons.arrow_forward_ios,
                color: const Color.fromRGBO(0, 131, 143, 1),
                size: MediaQuery.of(context).size.width * 0.04,
              ),
              onTap: () {
                setState(() {
                  selectedJobCategoryId = categoryId;
                  if (selectedMainSkill != categoryTitle) {
                    selectedSubSkills[selectedMainSkill] = [];
                    selectedSkills = [];
                    _updateTextFieldText('');
                  }
                  selectedMainSkill = categoryTitle;
                });
                // Don't close the main dialog, just show sub skills
                showSubSkillsDialog(categoryId, categoryTitle);
              },
            ),
          );
        }).toList(),
      );
    },
  );
}

void _updateTextFieldText(String mainSkill) {
  if (selectedSkills.isNotEmpty) {
    _skillController.text = '$mainSkill: ${selectedSkills.join(', ')}';
  } else {
    _skillController.text = '';
  }
}

void showSubSkillsDialog(String categoryId, String mainSkill) {
  selectedSkills = List.from(selectedSubSkills[mainSkill] ?? []);

  showDialog(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext context) {
      return FutureBuilder<List<Map<String, dynamic>>>(
        future: fetchSubSkills(categoryId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              backgroundColor: Colors.white,
              child: Container(
                padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.08),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.08,
                      height: MediaQuery.of(context).size.width * 0.08,
                      child: const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Color.fromRGBO(0, 131, 143, 1),
                        ),
                        strokeWidth: 3,
                      ),
                    ),
                    SizedBox(height: MediaQuery.of(context).size.height * 0.02),
                    Text(
                      'Loading Sub Skills...',
                      style: TextStyle(
                        fontSize: MediaQuery.of(context).size.width * 0.04,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
            );
          } else if (snapshot.hasError) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              backgroundColor: Colors.white,
              child: Container(
                padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.08),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: MediaQuery.of(context).size.width * 0.12,
                      color: Colors.red,
                    ),
                    SizedBox(height: MediaQuery.of(context).size.height * 0.02),
                    Text(
                      'Error',
                      style: TextStyle(
                        fontSize: MediaQuery.of(context).size.width * 0.045,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(height: MediaQuery.of(context).size.height * 0.01),
                    Text(
                      'Error: ${snapshot.error}',
                      style: TextStyle(
                        fontSize: MediaQuery.of(context).size.width * 0.035,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              backgroundColor: Colors.white,
              child: Container(
                padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.08),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.work_off,
                      size: MediaQuery.of(context).size.width * 0.12,
                      color: Colors.grey,
                    ),
                    SizedBox(height: MediaQuery.of(context).size.height * 0.02),
                    Text(
                      'No Sub Skills Available',
                      style: TextStyle(
                        fontSize: MediaQuery.of(context).size.width * 0.045,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(height: MediaQuery.of(context).size.height * 0.01),
                    Text(
                      'No sub skills available for this category.',
                      style: TextStyle(
                        fontSize: MediaQuery.of(context).size.width * 0.035,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
          }

          List<Map<String, dynamic>> subSkillsList = snapshot.data!;

          return StatefulBuilder(
            builder: (context, setState) {
              return Dialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                elevation: 8,
                backgroundColor: Colors.transparent,
                child: Container(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.8,
                    maxWidth: MediaQuery.of(context).size.width * 0.9,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        spreadRadius: 2,
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header with back button
                      Container(
                        padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.05),
                        decoration: const BoxDecoration(
                          color: Color.fromRGBO(0, 131, 143, 1),
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20),
                          ),
                        ),
                        child: Row(
                          children: [
                            GestureDetector(
                              onTap: () {
                                // Close sub skills dialog and show main skills dialog again
                                Navigator.pop(context);
                                showSkillsDialog();
                              },
                              child: Container(
                                padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.arrow_back_ios,
                                  color: Colors.white,
                                  size: MediaQuery.of(context).size.width * 0.05,
                                ),
                              ),
                            ),
                            SizedBox(width: MediaQuery.of(context).size.width * 0.04),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Select Sub Skills',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: MediaQuery.of(context).size.width * 0.045,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    mainSkill,
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.8),
                                      fontSize: MediaQuery.of(context).size.width * 0.035,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Content
                      Flexible(
                        child: Container(
                          padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.05),
                          child: Scrollbar(
                            thumbVisibility: true,
                            child: SingleChildScrollView(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: subSkillsList.map((subSkill) {
                                  return Container(
                                    margin: EdgeInsets.only(bottom: MediaQuery.of(context).size.height * 0.01),
                                    decoration: BoxDecoration(
                                      color: selectedSkills.contains(subSkill['name'])
                                          ? const Color.fromRGBO(0, 131, 143, 1).withOpacity(0.1)
                                          : Colors.grey.shade50,
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: selectedSkills.contains(subSkill['name'])
                                            ? const Color.fromRGBO(0, 131, 143, 1)
                                            : Colors.grey.shade200,
                                        width: 1,
                                      ),
                                    ),
                                    child: CheckboxListTile(
                                      contentPadding: EdgeInsets.symmetric(
                                        horizontal: MediaQuery.of(context).size.width * 0.04,
                                        vertical: MediaQuery.of(context).size.height * 0.005,
                                      ),
                                      title: Text(
                                        subSkill['name'],
                                        style: TextStyle(
                                          fontSize: MediaQuery.of(context).size.width * 0.038,
                                          fontWeight: selectedSkills.contains(subSkill['name'])
                                              ? FontWeight.w600
                                              : FontWeight.w500,
                                          color: selectedSkills.contains(subSkill['name'])
                                              ? const Color.fromRGBO(0, 131, 143, 1)
                                              : Colors.black87,
                                        ),
                                      ),
                                      value: selectedSkills.contains(subSkill['name']),
                                      onChanged: (value) {
                                        setState(() {
                                          if (value != null) {
                                            if (value) {
                                              selectedSkills.add(subSkill['name']);
                                            } else {
                                              selectedSkills.remove(subSkill['name']);
                                            }
                                          }
                                          _updateTextFieldText(mainSkill);
                                        });
                                      },
                                      controlAffinity: ListTileControlAffinity.leading,
                                      activeColor: const Color.fromRGBO(0, 131, 143, 1),
                                      checkColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                            ),
                          ),
                        ),
                      ),
                      
                      // Footer with action button
                      Container(
                        padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.05),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: const BorderRadius.only(
                            bottomLeft: Radius.circular(20),
                            bottomRight: Radius.circular(20),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color.fromRGBO(0, 131, 143, 1).withOpacity(0.3),
                                    spreadRadius: 1,
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: ElevatedButton(
                                onPressed: () {
                                  setState(() {
                                    selectedSubSkills[mainSkill] = List.from(selectedSkills);
                                    selectedServiceIds = subSkillsList
                                        .where((subSkill) => selectedSkills.contains(subSkill['name']))
                                        .map((subSkill) => subSkill['id'].toString())
                                        .toList();
                                    _updateTextFieldText(mainSkill);
                                  });
                                  // Close sub skills dialog and main skills dialog
                                  Navigator.pop(context);
                                  Navigator.pop(context);
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color.fromRGBO(0, 131, 143, 1),
                                  foregroundColor: Colors.white,
                                  padding: EdgeInsets.symmetric(
                                    horizontal: MediaQuery.of(context).size.width * 0.08,
                                    vertical: MediaQuery.of(context).size.height * 0.02,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  elevation: 0,
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.check,
                                      size: MediaQuery.of(context).size.width * 0.05,
                                    ),
                                    SizedBox(width: MediaQuery.of(context).size.width * 0.02),
                                    Text(
                                      'Done (${selectedSkills.length})',
                                      style: TextStyle(
                                        fontSize: MediaQuery.of(context).size.width * 0.04,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      );
    },
  );
}

  // Widget buildAddressDropdown() {
  //   return Row(
  //     children: [
  //       Expanded(
  //         child: TextField(
  //           onTap: () {
  //             showDistrictMunicipalityWardDialog();
  //           },
  //           controller: _addressController,
  //           readOnly: !isEditingAddress,
  //           decoration: const InputDecoration(
  //             labelText: 'Address',
  //             labelStyle: TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
  //             focusedBorder: OutlineInputBorder(
  //               borderSide: BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
  //             ),
  //             prefixIcon: Icon(
  //               Icons.home,
  //               color: Color.fromRGBO(0, 131, 143, 1),
  //             ),
  //           ),
  //         ),
  //       ),
  //     ],
  //   );
  // }

  // void showDistrictMunicipalityWardDialog() {
  //   showDialog(
  //     context: context,
  //     builder: (BuildContext context) {
  //       return AlertDialog(
  //         title: const Text('Select Address'),
  //         content: Column(
  //           mainAxisSize: MainAxisSize.min,
  //           children: [
  //             buildDropdown(
  //               label: 'District',
  //               items: districts,
  //               onChanged: (value) {
  //                 setState(() {
  //                   selectedDistrict = value;
  //                   selectedMunicipality = '';
  //                   selectedWard = '';
  //                 });
  //                 Navigator.pop(context);
  //                 showMunicipalityDialog();
  //               },
  //             ),
  //           ],
  //         ),
  //       );
  //     },
  //   );
  // }

  // void showMunicipalityDialog() {
  //   showDialog(
  //     context: context,
  //     builder: (BuildContext context) {
  //       return AlertDialog(
  //         title: const Text('Select Municipality'),
  //         content: Column(
  //           mainAxisSize: MainAxisSize.min,
  //           children: [
  //             if (selectedDistrict != null)
  //               buildDropdown(
  //                 label: 'Municipality',
  //                 items: municipalities[selectedDistrict!] ?? [],
  //                 onChanged: (value) {
  //                   setState(() {
  //                     selectedMunicipality = value;
  //                     selectedWard = '';
  //                   });
  //                   Navigator.pop(context);
  //                   showWardDialog();
  //                 },
  //               ),
  //           ],
  //         ),
  //       );
  //     },
  //   );
  // }

  // void showWardDialog() {
  //   showDialog(
  //     context: context,
  //     builder: (BuildContext context) {
  //       return AlertDialog(
  //         title: const Text('Select Ward'),
  //         content: Column(
  //           mainAxisSize: MainAxisSize.min,
  //           children: [
  //             if (selectedMunicipality != null)
  //               buildDropdown(
  //                 label: 'Ward',
  //                 items: wards[selectedMunicipality!] ?? [],
  //                 onChanged: (value) {
  //                   setState(() {
  //                     selectedWard = value;
  //                     _addressController.text =
  //                         '$selectedDistrict, $selectedMunicipality, $selectedWard';
  //                   });
  //                   Navigator.pop(context);
  //                 },
  //               ),
  //           ],
  //         ),
  //       );
  //     },
  //   );
  // }

  // Widget buildDropdown({
  //   required String label,
  //   required List<String> items,
  //   required Function(String) onChanged,
  // }) {
  //   return DropdownButtonFormField(
  //     decoration: InputDecoration(
  //       labelText: label,
  //       labelStyle: const TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
  //       focusedBorder: const OutlineInputBorder(
  //         borderSide: BorderSide(color: Color.fromRGBO(0, 131, 143, 1)),
  //       ),
  //     ),
  //     value: selectedWard != null && items.contains(selectedWard)
  //         ? selectedWard
  //         : items.isNotEmpty
  //             ? items.first
  //             : null,
  //     items: items
  //         .map((item) => DropdownMenuItem(
  //               value: item,
  //               child: Text(
  //                 item,
  //                 style: const TextStyle(color: Color.fromRGBO(0, 131, 143, 1)),
  //               ),
  //             ))
  //         .toList(),
  //     onChanged: (value) {
  //       onChanged(value.toString());
  //     },
  //   );
  // }
  Widget buildAddressDropdown() {
  return LayoutBuilder(
    builder: (context, constraints) {
      final screenWidth = MediaQuery.of(context).size.width;
      final screenHeight = MediaQuery.of(context).size.height;
      final isSmallScreen = screenWidth < 600;
      
      return Container(
        margin: EdgeInsets.symmetric(
          vertical: isSmallScreen ? 6.0 : 8.0,
          horizontal: isSmallScreen ? 0 : 4.0,
        ),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                onTap: () {
                  showDistrictMunicipalityWardDialog();
                },
                controller: _addressController,
                readOnly: !isEditingAddress,
                decoration: InputDecoration(
                  labelText: 'Address',
                  hintText: 'Select your address',
                  filled: true,
                  fillColor: Colors.white,
                  labelStyle: TextStyle(
                    color: const Color.fromRGBO(0, 131, 143, 1),
                    fontSize: isSmallScreen ? 14 : 16,
                    fontWeight: FontWeight.w500,
                  ),
                  hintStyle: TextStyle(
                    color: Colors.grey[600],
                    fontSize: isSmallScreen ? 13 : 14,
                  ),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: isSmallScreen ? 12 : 16,
                    vertical: isSmallScreen ? 14 : 16,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                    borderSide: const BorderSide(
                      color: Color.fromRGBO(0, 131, 143, 1),
                      width: 2,
                    ),
                  ),
                  prefixIcon: Icon(
                    Icons.location_on,
                    color: const Color.fromRGBO(0, 131, 143, 1),
                    size: isSmallScreen ? 20 : 24,
                  ),
                  suffixIcon: Icon(
                    Icons.arrow_drop_down,
                    color: const Color.fromRGBO(0, 131, 143, 1),
                    size: isSmallScreen ? 20 : 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    },
  );
}

void showDistrictMunicipalityWardDialog() {
  showDialog(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext context) {
      final screenWidth = MediaQuery.of(context).size.width;
      final screenHeight = MediaQuery.of(context).size.height;
      final isSmallScreen = screenWidth < 600;
      final isTablet = screenWidth >= 600 && screenWidth < 1200;
      
      return Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
        ),
        insetPadding: EdgeInsets.all(isSmallScreen ? 16 : 24),
        child: Container(
          width: isSmallScreen 
              ? screenWidth * 0.9 
              : isTablet 
                  ? screenWidth * 0.7 
                  : 500,
          constraints: BoxConstraints(
            maxHeight: screenHeight * 0.8,
            maxWidth: isSmallScreen ? screenWidth * 0.95 : 600,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
          ),
          padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Select District',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 18 : 20,
                      fontWeight: FontWeight.bold,
                      color: const Color.fromRGBO(0, 131, 143, 1),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close,
                      color: Colors.grey,
                      size: isSmallScreen ? 20 : 24,
                    ),
                    padding: EdgeInsets.all(isSmallScreen ? 4 : 8),
                  ),
                ],
              ),
              SizedBox(height: isSmallScreen ? 16 : 20),
              // Progress indicator
              Row(
                children: [
                  _buildProgressDot(true, '1', isSmallScreen),
                  _buildProgressLine(false, isSmallScreen),
                  _buildProgressDot(false, '2', isSmallScreen),
                  _buildProgressLine(false, isSmallScreen),
                  _buildProgressDot(false, '3', isSmallScreen),
                ],
              ),
              SizedBox(height: isSmallScreen ? 16 : 20),
              // Dropdown
              Flexible(
                child: buildDropdown(
                  label: 'District',
                  items: districts,
                  onChanged: (value) {
                    setState(() {
                      selectedDistrict = value;
                      selectedMunicipality = '';
                      selectedWard = '';
                    });
                    Navigator.pop(context);
                    showMunicipalityDialog();
                  },
                ),
              ),
              SizedBox(height: isSmallScreen ? 16 : 20),
              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'Cancel',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: isSmallScreen ? 14 : 16,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    },
  );
}

void showMunicipalityDialog() {
  showDialog(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext context) {
      final screenWidth = MediaQuery.of(context).size.width;
      final screenHeight = MediaQuery.of(context).size.height;
      final isSmallScreen = screenWidth < 600;
      final isTablet = screenWidth >= 600 && screenWidth < 1200;
      
      return Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
        ),
        insetPadding: EdgeInsets.all(isSmallScreen ? 16 : 24),
        child: Container(
          width: isSmallScreen 
              ? screenWidth * 0.9 
              : isTablet 
                  ? screenWidth * 0.7 
                  : 500,
          constraints: BoxConstraints(
            maxHeight: screenHeight * 0.8,
            maxWidth: isSmallScreen ? screenWidth * 0.95 : 600,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
          ),
          padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                          showDistrictMunicipalityWardDialog();
                        },
                        icon: Icon(
                          Icons.arrow_back,
                          color: const Color.fromRGBO(0, 131, 143, 1),
                          size: isSmallScreen ? 20 : 24,
                        ),
                        padding: EdgeInsets.all(isSmallScreen ? 4 : 8),
                      ),
                      SizedBox(width: isSmallScreen ? 4 : 8),
                      Text(
                        'Select Municipality',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 18 : 20,
                          fontWeight: FontWeight.bold,
                          color: const Color.fromRGBO(0, 131, 143, 1),
                        ),
                      ),
                    ],
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close,
                      color: Colors.grey,
                      size: isSmallScreen ? 20 : 24,
                    ),
                    padding: EdgeInsets.all(isSmallScreen ? 4 : 8),
                  ),
                ],
              ),
              SizedBox(height: isSmallScreen ? 16 : 20),
              // Progress indicator
              Row(
                children: [
                  _buildProgressDot(true, '1', isSmallScreen),
                  _buildProgressLine(true, isSmallScreen),
                  _buildProgressDot(true, '2', isSmallScreen),
                  _buildProgressLine(false, isSmallScreen),
                  _buildProgressDot(false, '3', isSmallScreen),
                ],
              ),
              SizedBox(height: isSmallScreen ? 8 : 12),
              // Selected district info
              if (selectedDistrict != null)
                Container(
                  padding: EdgeInsets.all(isSmallScreen ? 10 : 12),
                  decoration: BoxDecoration(
                    color: const Color.fromRGBO(0, 131, 143, 0.1),
                    borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: const Color.fromRGBO(0, 131, 143, 1),
                        size: isSmallScreen ? 14 : 16,
                      ),
                      SizedBox(width: isSmallScreen ? 6 : 8),
                      Text(
                        'District: $selectedDistrict',
                        style: TextStyle(
                          color: const Color.fromRGBO(0, 131, 143, 1),
                          fontWeight: FontWeight.w500,
                          fontSize: isSmallScreen ? 13 : 14,
                        ),
                      ),
                    ],
                  ),
                ),
              SizedBox(height: isSmallScreen ? 16 : 20),
              // Dropdown
              if (selectedDistrict != null)
                Flexible(
                  child: buildDropdown(
                    label: 'Municipality',
                    items: municipalities[selectedDistrict!] ?? [],
                    onChanged: (value) {
                      setState(() {
                        selectedMunicipality = value;
                        selectedWard = '';
                      });
                      Navigator.pop(context);
                      showWardDialog();
                    },
                  ),
                ),
              SizedBox(height: isSmallScreen ? 16 : 20),
              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      showDistrictMunicipalityWardDialog();
                    },
                    icon: Icon(
                      Icons.arrow_back,
                      size: isSmallScreen ? 16 : 18,
                    ),
                    label: Text(
                      'Back',
                      style: TextStyle(fontSize: isSmallScreen ? 14 : 16),
                    ),
                    style: TextButton.styleFrom(
                      foregroundColor: const Color.fromRGBO(0, 131, 143, 1),
                    ),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'Cancel',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: isSmallScreen ? 14 : 16,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    },
  );
}

void showWardDialog() {
  showDialog(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext context) {
      final screenWidth = MediaQuery.of(context).size.width;
      final screenHeight = MediaQuery.of(context).size.height;
      final isSmallScreen = screenWidth < 600;
      final isTablet = screenWidth >= 600 && screenWidth < 1200;
      
      return Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
        ),
        insetPadding: EdgeInsets.all(isSmallScreen ? 16 : 24),
        child: Container(
          width: isSmallScreen 
              ? screenWidth * 0.9 
              : isTablet 
                  ? screenWidth * 0.7 
                  : 500,
          constraints: BoxConstraints(
            maxHeight: screenHeight * 0.8,
            maxWidth: isSmallScreen ? screenWidth * 0.95 : 600,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
          ),
          padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                          showMunicipalityDialog();
                        },
                        icon: Icon(
                          Icons.arrow_back,
                          color: const Color.fromRGBO(0, 131, 143, 1),
                          size: isSmallScreen ? 20 : 24,
                        ),
                        padding: EdgeInsets.all(isSmallScreen ? 4 : 8),
                      ),
                      SizedBox(width: isSmallScreen ? 4 : 8),
                      Text(
                        'Select Ward',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 18 : 20,
                          fontWeight: FontWeight.bold,
                          color: const Color.fromRGBO(0, 131, 143, 1),
                        ),
                      ),
                    ],
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close,
                      color: Colors.grey,
                      size: isSmallScreen ? 20 : 24,
                    ),
                    padding: EdgeInsets.all(isSmallScreen ? 4 : 8),
                  ),
                ],
              ),
              SizedBox(height: isSmallScreen ? 16 : 20),
              // Progress indicator
              Row(
                children: [
                  _buildProgressDot(true, '1', isSmallScreen),
                  _buildProgressLine(true, isSmallScreen),
                  _buildProgressDot(true, '2', isSmallScreen),
                  _buildProgressLine(true, isSmallScreen),
                  _buildProgressDot(true, '3', isSmallScreen),
                ],
              ),
              SizedBox(height: isSmallScreen ? 8 : 12),
              // Selected info
              Column(
                children: [
                  if (selectedDistrict != null)
                    Container(
                      margin: EdgeInsets.only(bottom: isSmallScreen ? 6 : 8),
                      padding: EdgeInsets.all(isSmallScreen ? 10 : 12),
                      decoration: BoxDecoration(
                        color: const Color.fromRGBO(0, 131, 143, 0.1),
                        borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: const Color.fromRGBO(0, 131, 143, 1),
                            size: isSmallScreen ? 14 : 16,
                          ),
                          SizedBox(width: isSmallScreen ? 6 : 8),
                          Text(
                            'District: $selectedDistrict',
                            style: TextStyle(
                              color: const Color.fromRGBO(0, 131, 143, 1),
                              fontWeight: FontWeight.w500,
                              fontSize: isSmallScreen ? 13 : 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (selectedMunicipality != null)
                    Container(
                      margin: EdgeInsets.only(bottom: isSmallScreen ? 6 : 8),
                      padding: EdgeInsets.all(isSmallScreen ? 10 : 12),
                      decoration: BoxDecoration(
                        color: const Color.fromRGBO(0, 131, 143, 0.1),
                        borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: const Color.fromRGBO(0, 131, 143, 1),
                            size: isSmallScreen ? 14 : 16,
                          ),
                          SizedBox(width: isSmallScreen ? 6 : 8),
                          Text(
                            'Municipality: $selectedMunicipality',
                            style: TextStyle(
                              color: const Color.fromRGBO(0, 131, 143, 1),
                              fontWeight: FontWeight.w500,
                              fontSize: isSmallScreen ? 13 : 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              SizedBox(height: isSmallScreen ? 16 : 20),
              // Dropdown
              if (selectedMunicipality != null)
                Flexible(
                  child: buildDropdown(
                    label: 'Ward',
                    items: wards[selectedMunicipality!] ?? [],
                    onChanged: (value) {
                      setState(() {
                        selectedWard = value;
                        _addressController.text =
                            '$selectedDistrict, $selectedMunicipality, $selectedWard';
                      });
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text('Address selected successfully!'),
                          backgroundColor: const Color.fromRGBO(0, 131, 143, 1),
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              SizedBox(height: isSmallScreen ? 16 : 20),
              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      showMunicipalityDialog();
                    },
                    icon: Icon(
                      Icons.arrow_back,
                      size: isSmallScreen ? 16 : 18,
                    ),
                    label: Text(
                      'Back',
                      style: TextStyle(fontSize: isSmallScreen ? 14 : 16),
                    ),
                    style: TextButton.styleFrom(
                      foregroundColor: const Color.fromRGBO(0, 131, 143, 1),
                    ),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'Cancel',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: isSmallScreen ? 14 : 16,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    },
  );
}

Widget buildDropdown({
  required String label,
  required List<String> items,
  required Function(String) onChanged,
}) {
  return Builder(
    builder: (context) {
      final screenWidth = MediaQuery.of(context).size.width;
      final isSmallScreen = screenWidth < 600;
      
      return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: DropdownButtonFormField<String>(
          decoration: InputDecoration(
            labelText: label,
            filled: true,
            fillColor: Colors.white,
            labelStyle: TextStyle(
              color: const Color.fromRGBO(0, 131, 143, 1),
              fontWeight: FontWeight.w500,
              fontSize: isSmallScreen ? 14 : 16,
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: isSmallScreen ? 12 : 16,
              vertical: isSmallScreen ? 12 : 16,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
              borderSide: BorderSide.none,
            ),
            prefixIcon: Icon(
              _getIconForLabel(label),
              color: const Color.fromRGBO(0, 131, 143, 1),
              size: isSmallScreen ? 18 : 20,
            ),
          ),
          value: null,
          hint: Text(
            'Choose $label',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: isSmallScreen ? 13 : 14,
            ),
          ),
          dropdownColor: Colors.white,
          items: items.map((item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Container(
                padding: EdgeInsets.symmetric(
                  vertical: isSmallScreen ? 2 : 4,
                ),
                child: Text(
                  item,
                  style: TextStyle(
                    color: const Color.fromRGBO(0, 131, 143, 1),
                    fontSize: isSmallScreen ? 13 : 14,
                  ),
                ),
              ),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              onChanged(value);
            }
          },
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: const Color.fromRGBO(0, 131, 143, 1),
            size: isSmallScreen ? 20 : 24,
          ),
        ),
      );
    },
  );
}

// Helper method for progress indicator
Widget _buildProgressDot(bool isActive, String number, bool isSmallScreen) {
  return Container(
    width: isSmallScreen ? 24 : 28,
    height: isSmallScreen ? 24 : 28,
    decoration: BoxDecoration(
      color: isActive 
          ? const Color.fromRGBO(0, 131, 143, 1) 
          : Colors.grey[300],
      shape: BoxShape.circle,
    ),
    child: Center(
      child: Text(
        number,
        style: TextStyle(
          color: isActive ? Colors.white : Colors.grey[600],
          fontSize: isSmallScreen ? 10 : 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    ),
  );
}

Widget _buildProgressLine(bool isActive, bool isSmallScreen) {
  return Expanded(
    child: Container(
      height: 2,
      margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 4 : 8),
      color: isActive 
          ? const Color.fromRGBO(0, 131, 143, 1) 
          : Colors.grey[300],
    ),
  );
}
IconData _getIconForLabel(String label) {
  switch (label.toLowerCase()) {
    case 'district':
      return Icons.map;
    case 'municipality':
      return Icons.location_city;
    case 'ward':
      return Icons.home;
    default:
      return Icons.place;
  }
}
  
  Widget buildViewTermsAndConditions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Flexible(
          child: TextButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => const PrivacypolicyScreen()),
              );
            },
            child: const Text(
              'View Terms and Conditions',
              style: TextStyle(
                color: Color.fromRGBO(0, 131, 143, 1),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  bool agreedToTerms = false;
  Widget buildTermsCheckbox() {
    return Center(
      child: Transform.scale(
        scale: 0.8,
        child: CheckboxListTile(
          value: agreedToTerms,
          onChanged: (bool? value) {
            setState(() {
              agreedToTerms = value ?? false;
            });
          },
          title: const FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              'I agree to the Terms and Conditions',
              style: TextStyle(
                color: Color.fromRGBO(0, 131, 143, 1),
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
          ),
          controlAffinity: ListTileControlAffinity.leading,
        ),
      ),
    );
  }
}
