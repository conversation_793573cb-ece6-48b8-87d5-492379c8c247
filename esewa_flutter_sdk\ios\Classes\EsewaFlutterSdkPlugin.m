#import "EsewaFlutterSdkPlugin.h"
#if __has_include(<esewa_flutter_sdk/esewa_flutter_sdk-Swift.h>)
#import <esewa_flutter_sdk/esewa_flutter_sdk-Swift.h>
#else
// Support project import fallback if the generated compatibility header
// is not copied when this plugin is created as a library.
// https://forums.swift.org/t/swift-static-libraries-dont-copy-generated-objective-c-header/19816
#import "esewa_flutter_sdk-Swift.h"
#endif

@implementation EsewaFlutterSdkPlugin
+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
    [SwiftEsewaFlutterSdkPlugin registerWithRegistrar:registrar];
}
@end
