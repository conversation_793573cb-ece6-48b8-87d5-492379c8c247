import 'package:flutter/material.dart';
import 'package:smartsewa/views/user_screen/showExtraServices/OfferDetailPage.dart';
import 'package:smartsewa/views/user_screen/showExtraServices/Postofferpage.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/services.dart';

const primaryColor = Color.fromARGB(240, 0, 131, 143);

class OffersPage extends StatefulWidget {
  const OffersPage({Key? key}) : super(key: key);

  @override
  State<OffersPage> createState() => _OffersPageState();
}

class _OffersPageState extends State<OffersPage> {
  int _currentCarouselIndex = 0;
  final List<String> carouselItems = [
    'assets/featured_offer.jpg',
    'assets/solar_panel.jpg',
    'assets/mobile_repair.jpg',
    'assets/food.jpg',
  ];

  @override
  Widget build(BuildContext context) {
    // Get screen dimensions
    final Size screenSize = MediaQuery.of(context).size;
    final double screenWidth = screenSize.width;
    final double screenHeight = screenSize.height;
    final bool isTablet = screenWidth > 600;
    final bool isDesktop = screenWidth > 900;

    // Adaptive spacing values
    final double horizontalPadding = screenWidth * 0.04;
    final double verticalPadding = screenHeight * 0.02;

    // Responsive font sizes
    final double titleFontSize = isDesktop ? 20 : (isTablet ? 18 : 16);
    final double descriptionFontSize = isDesktop ? 16 : (isTablet ? 14 : 12);
    final double smallFontSize = isDesktop ? 14 : (isTablet ? 12 : 10);

    // Responsive carousel height
    final double carouselHeight =
        screenHeight * (isDesktop ? 0.25 : (isTablet ? 0.2 : 0.15));

    // Responsive grid layout
    final int gridCrossAxisCount = isDesktop ? 4 : (isTablet ? 3 : 2);
    final double childAspectRatio = isDesktop ? 0.65 : (isTablet ? 0.7 : 0.6);

    // Responsive padding
    final EdgeInsets cardPadding =
        EdgeInsets.all(isDesktop ? 16 : (isTablet ? 12 : 8));

    return LayoutBuilder(
      builder: (context, constraints) {
        return Scaffold(
          backgroundColor: Colors.grey[100],
          appBar: myAppbar(context, true, 'Offers'),
          body: OrientationBuilder(
            builder: (context, orientation) {
              final bool isLandscape = orientation == Orientation.landscape;

              return SafeArea(
                child: Column(
                  children: [
                    // Search bar
                    _buildSearchBar(context, horizontalPadding),

                    // Carousel section
                    _buildCarousel(
                      context: context,
                      items: carouselItems,
                      height: carouselHeight,
                      titleFontSize: titleFontSize,
                      isLandscape: isLandscape,
                    ),

                    // Scrollable grid section
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: horizontalPadding,
                          vertical: verticalPadding,
                        ),
                        child: GridView.builder(
                          physics: const BouncingScrollPhysics(),
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: isLandscape && !isDesktop
                                ? gridCrossAxisCount + 1
                                : gridCrossAxisCount,
                            crossAxisSpacing: horizontalPadding * 0.75,
                            mainAxisSpacing: verticalPadding * 0.75,
                            childAspectRatio: isLandscape
                                ? childAspectRatio * 1.2
                                : childAspectRatio,
                          ),
                          itemCount: offers.length,
                          itemBuilder: (context, index) {
                            return _buildOfferCard(
                              context: context,
                              offer: offers[index],
                              titleFontSize: titleFontSize,
                              descriptionFontSize: descriptionFontSize,
                              smallFontSize: smallFontSize,
                              padding: cardPadding,
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          floatingActionButton: _buildResponsiveFloatingActionButton(
            context: context,
            isTablet: isTablet,
            fontSize: smallFontSize * 1.3,
          ),
        );
      },
    );
  }

  Widget _buildSearchBar(BuildContext context, double horizontalPadding) {
    final Size size = MediaQuery.of(context).size;
    final double iconSize = size.width > 600 ? 24 : 20;

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: size.height * 0.01,
      ),
      child: TextField(
        style: TextStyle(fontSize: size.width > 600 ? 16 : 14),
        decoration: InputDecoration(
          hintText: 'Search offers...',
          prefixIcon: Icon(Icons.search, color: primaryColor, size: iconSize),
          filled: true,
          fillColor: Colors.white,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(size.width * 0.03),
            borderSide: BorderSide.none,
          ),
          contentPadding: EdgeInsets.symmetric(
            vertical: size.height * 0.012,
            horizontal: size.width * 0.02,
          ),
        ),
      ),
    );
  }

  Widget _buildCarousel({
    required BuildContext context,
    required List<String> items,
    required double height,
    required double titleFontSize,
    required bool isLandscape,
  }) {
    final Size size = MediaQuery.of(context).size;
    final double adjustedHeight = isLandscape ? height * 1.5 : height;

    return Column(
      children: [
        CarouselSlider(
          options: CarouselOptions(
            height: adjustedHeight,
            viewportFraction: isLandscape ? 0.6 : 0.9,
            enlargeCenterPage: true,
            autoPlay: true,
            autoPlayInterval: const Duration(seconds: 3),
            autoPlayAnimationDuration: const Duration(milliseconds: 800),
            onPageChanged: (index, reason) {
              setState(() {
                _currentCarouselIndex = index;
              });
            },
          ),
          items: items.map((item) {
            return Builder(
              builder: (BuildContext context) {
                return Container(
                  width: MediaQuery.of(context).size.width,
                  margin: EdgeInsets.symmetric(horizontal: size.width * 0.015),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(size.width * 0.04),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 8,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(size.width * 0.04),
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        Image.asset(
                          item,
                          fit: BoxFit.cover,
                        ),
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withOpacity(0.7)
                              ],
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: size.height * 0.02,
                          left: size.width * 0.04,
                          child: Text(
                            'Special Offer',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: titleFontSize,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          }).toList(),
        ),
        SizedBox(height: size.height * 0.015),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: items.asMap().entries.map((entry) {
            return Container(
              width: 8.0,
              height: 8.0,
              margin: const EdgeInsets.symmetric(horizontal: 4.0),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _currentCarouselIndex == entry.key
                    ? primaryColor
                    : primaryColor.withOpacity(0.4),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildOfferCard({
    required BuildContext context,
    required OfferItem offer,
    required double titleFontSize,
    required double descriptionFontSize,
    required double smallFontSize,
    required EdgeInsets padding,
  }) {
    final Size size = MediaQuery.of(context).size;
    final double borderRadius = size.width * 0.06;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(borderRadius),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => OfferDetailPage(offer: offer),
              ),
            );
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image section
              Expanded(
                flex: 3,
                child: Hero(
                  tag: 'offer_image_${offer.title}',
                  child: ClipRRect(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(borderRadius),
                    ),
                    child: Image.asset(
                      offer.imageUrl,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
              // Content section
              Expanded(
                flex: 2,
                child: Padding(
                  padding: padding,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Title
                      Text(
                        offer.title,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: titleFontSize,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: size.height * 0.005),

                      // Description
                      Text(
                        offer.description,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: descriptionFontSize,
                          height: 1.4,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: size.height * 0.008),

                      // Expiry date
                      Row(
                        children: [
                          Icon(
                            Icons.calendar_today_rounded,
                            color: Colors.grey[600],
                            size: smallFontSize * 1.2,
                          ),
                          SizedBox(width: size.width * 0.01),
                          Text(
                            'Expires: ${offer.expiryDate}',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: smallFontSize,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: size.height * 0.005),

                      // View count
                      Row(
                        children: [
                          Icon(
                            Icons.visibility_rounded,
                            color: Colors.grey[600],
                            size: smallFontSize * 1.2,
                          ),
                          SizedBox(width: size.width * 0.01),
                          Text(
                            '${offer.seenCount} Views',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: smallFontSize,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildResponsiveFloatingActionButton({
    required BuildContext context,
    required bool isTablet,
    required double fontSize,
  }) {
    final Size size = MediaQuery.of(context).size;

    return FloatingActionButton.extended(
      onPressed: () => Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const PostOfferPage()),
      ),
      backgroundColor: primaryColor,
      icon: Icon(Icons.add, color: Colors.white, size: isTablet ? 24 : 20),
      label: Text(
        'Create Offer',
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: fontSize,
        ),
      ),
      extendedPadding: EdgeInsets.symmetric(
        horizontal: size.width * 0.03,
        vertical: size.height * 0.01,
      ),
    );
  }
}

class OfferItem {
  final String imageUrl;
  final String title;
  final String description;
  final String expiryDate;
  final int seenCount;

  OfferItem({
    required this.imageUrl,
    required this.title,
    required this.description,
    required this.expiryDate,
    required this.seenCount,
  });
}

final List<OfferItem> offers = [
  OfferItem(
    imageUrl: 'assets/solar_panel.jpg',
    title: 'Solar Company Ltd',
    description: 'Flat 15% OFF on solar.',
    expiryDate: '15 Dec 2024',
    seenCount: 120,
  ),
  OfferItem(
    imageUrl: 'assets/mobile_repair.jpg',
    title: 'Mobile Repair',
    description: '20% OFF on servicing.',
    expiryDate: '16 Dec 2024',
    seenCount: 80,
  ),
  OfferItem(
    imageUrl: 'assets/food.jpg',
    title: 'KFC Deals',
    description: 'Enjoy 15% OFF.',
    expiryDate: '14 Dec 2024',
    seenCount: 150,
  ),
];
