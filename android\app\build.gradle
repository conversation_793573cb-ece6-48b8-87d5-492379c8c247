plugins {
    id "com.android.application"
    id "dev.flutter.flutter-gradle-plugin"
    id "kotlin-android"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '6'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '6.0'
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace "com.smartsewa.service"
    compileSdkVersion 35  // Changed from 34 to 35
    ndkVersion "25.1.8937393"  // Explicit NDK version
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17  // Updated from 1.8
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'  // Add this block for Kotlin
    }

    defaultConfig {
        applicationId "com.smartsewa.service"
        minSdkVersion 22
        targetSdkVersion 35  // Changed from 34 to 35
        versionCode 36
        versionName "1.0.63"
        multiDexEnabled true  // Add this if using many dependencies
    }
       signingConfigs {
       release {
           keyAlias keystoreProperties['keyAlias']
           keyPassword keystoreProperties['keyPassword']
           storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
           storePassword keystoreProperties['storePassword']
       }
   }
  buildTypes {
    release {
        signingConfig signingConfigs.release

        // ✅ This enables symbol generation for native .so files
        ndk {
            debugSymbolLevel = 'FULL'  // or use 'FULL' for complete symbols
        }

        // Optional but recommended:
        shrinkResources true
        minifyEnabled true
        proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }
}
}

dependencies {
    implementation 'androidx.fragment:fragment:1.6.2'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:1.8.10"
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'com.facebook.android:facebook-android-sdk:[8,9)'
}

flutter {
    source '../..'
}