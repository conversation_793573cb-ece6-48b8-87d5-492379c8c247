// import 'dart:convert';
// import 'dart:io';
// import 'dart:developer';
// import 'dart:async';

// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:http/http.dart' as http;
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smartsewa/core/development/console.dart';
// import 'package:smartsewa/core/enum.dart';
// import 'package:smartsewa/core/states.dart';
// import 'package:smartsewa/network/base_client.dart';
// import 'package:smartsewa/network/models/common_response_model.dart';
// import 'package:smartsewa/views/user_screen/main_screen.dart';
// import 'package:smartsewa/views/widgets/Welcome%20Screen/splashscreen.dart';
// import 'package:smartsewa/views/widgets/custom_toasts.dart';
// import '../../../views/auth/login/login_screen.dart';

// import '../userdetails/current_user_controller.dart';

// class AuthController extends GetxController {
//   String? fullName;
//   String? mobileNumber;
//   String? address;

//   String? email;
//   String? password;
//   String? companyName;
//   String? citizenshipNum;
//   String? issuedDate;
//   String? latitude;
//   String? expiryDate;
//   bool? expiryCheck;
//   var workStatus = false;
//   String? serviceProvided;
//   bool? onlineStatus;

//   bool? role;
//   String? firstName;
//   String? lastName;
//   String? apptoken;
//   var isLoading = false.obs;
//   String baseUrl = BaseClient().baseUrl;
//   var isLogged = false.obs;
//   final pickedJobFieldName = ''.obs;

//   TextEditingController otpController = TextEditingController();

//   ///***Api hit for sms ***///
//   // String apiKey = 'v2_aD; //token generated
//   String apiKey = '';

//   ///******Api For Login *****///
//   Future login(TextEditingController emailController,
//       TextEditingController passwordController) async {
//     try {
//       final userController = Get.put(CurrentUserController());
//       isLoading.value = true;
//       consolelog(jsonEncode(
//         <String, dynamic>{
//           "username": emailController.text,
//           "password": passwordController.text,
//         },
//       ));
//       consolelog("login :: $baseUrl/api/v1/auth/login");
//       final res = await http
//           .post(
//             Uri.parse('$baseUrl/api/v1/auth/login'),
//             headers: <String, String>{
//               'Content-Type': 'application/json',
//             },
//             body: jsonEncode(
//               <String, dynamic>{
//                 "username": emailController.text,
//                 "password": passwordController.text,
//               },
//             ),
//           )
//           .timeout(const Duration(seconds: 20));
//       consolelog("login :: $baseUrl/api/v1/auth/login");
//       consolelog(res.statusCode);
//       consolelog(res.body);

//       if (res.statusCode == 200) {
//         // emailController.clear;
//         // passwordController.clear;
//         var user = jsonDecode(res.body);
//         log("login :: $baseUrl/api/v1/auth/login");
//         log(res.body);
//         // token = user['token'];
//         final apptoken = user['token'];
//         int tid = user['user']['id'];

//         fullName = user['user']['fullName'];
//         mobileNumber = user['user']['mobileNumber'];

//         email = user['user']['email'];
//         password = user['user']['password'];
//         companyName = user['user']['companyName'];
//         role = user['user']['role'];
//         citizenshipNum = user['user']['citizenshipNum'];
//         issuedDate = user['user']['issueDate'];
//         latitude = user['user']['latitude'];
//         expiryDate = user['user']['expiryDate'];
//         workStatus = user['user']['workStatus'];
//         serviceProvided = user['user']["serviceProvided"];
//         // id = user['user']['id'];
//         firstName = user['user']['firstName'];
//         lastName = user['user']['lastName'];
//         address = user['user']['address'];
//         expiryCheck = user['user']['expiryCheck'];
//         onlineStatus = user['user']['onlineStatus'];
//         userController.getCurrentUser();

//         var prefs = await SharedPreferences.getInstance();
//         logger("workStatus :: $workStatus", loggerType: LoggerType.success);
//         await prefs.setBool(SplashScreenState.keylogin, true);
//         await prefs.setBool("workStatus", workStatus);
//         await prefs.setString('token', apptoken);
//         await prefs.setInt("id", tid);
//         await prefs.setBool("rememberMe", true);
//         await prefs.setBool("isAuthenticated", true);
//         await prefs.setString('username', emailController.text);
//         await prefs.setString('password', passwordController.text);
//         await prefs.setBool('isServiceProviderActive', onlineStatus!);
//         selectedAutoLogin.value = prefs.getBool("rememberMe");

//         successToast(msg: "Login successfully");

//         if (apptoken != null && workStatus == true) {
//           //Get.offAll(() => const WelcomeScreen();
//           Get.offAll(() => const MainScreen());
//           isLoading.value = false;
//         } else {
//           Get.offAll(() => const MainScreen());
//           isLoading.value = false;
//         }
//         emailController.clear();
//         passwordController.clear();

//         // log('ID: $id');

//         // Get.offAll(() => const MainScreen());
//         //Get.offAll(() => MyScreen());
//       } else {
//         isLoading.value = false;
//         errorToast(msg: "Invalid Username or Password");
//       }
//     } on TimeoutException {
//       // Handle timeout error
//       errorToast(msg: 'Request Timeout');
//       isLoading.value = false;
//     } on http.ClientException {
//       isLoading.value = false;
//       errorToast(msg: 'Client Error');
//     } catch (err) {
//       errorToast(msg: 'Unexpected error');
//       isLoading.value = false;
//     }
//   }

// ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////

//   Future<void> uploadProfile(File? profilePicture,
//       {bool? isFromServiceScreen = false}) async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     String? apptoken = prefs.getString("token");
//     int? tid = prefs.getInt("id");
//     final userController = Get.put(CurrentUserController());

//     log(tid.toString());
//     // log(id.toString());
//     log('Get images init $apptoken');
//     // log('get images $token');
//     try {
//       var profilestream = http.ByteStream(profilePicture!.openRead());

//       // log('frontstream: $frontstream');
//       // // uploading file as a stream
//       // frontstream.cast();
//       // backstream.cast();
//       profilestream.cast();

//       // getting length of file
//       // var frontlength = citizenshipFront.lengthSync();
//       // var backlength = citizenshipBack.lengthSync();
//       var profilelength = profilePicture.lengthSync();
//       log('profilelength: $profilelength');

//       // log('frontlength: $frontlength');

//       // post file
//       var request = http.MultipartRequest(
//           'POST', Uri.parse('$baseUrl/api/allimg/upload/profile/$tid'));
// // http://*************:9000/api/allimg/upload/profile/2007
//       // adding header
//       request.headers.addAll({
//         'Content-Type': 'multipart/form-data',
//         // 'Content-Type': 'charset=UTF-8',
//         'Authorization': 'Bearer $apptoken'
//       });

//       // adding each images
//       // request.files.add(http.MultipartFile.fromBytes(
//       //     'citizenshipFront', citizenshipFront.readAsBytesSync(),
//       //     filename: "citizenshipFront"));
//       // request.files.add(http.MultipartFile.fromBytes(
//       //     'profilePicture', profilePicture.readAsBytesSync(),
//       //     filename: "profilePicture"));
//       request.files.add(http.MultipartFile.fromBytes(
//           'profilePicture', profilePicture.readAsBytesSync(),
//           filename: profilePicture.path.split("/").last));
//       // request.files.add(http.MultipartFile.fromBytes(
//       //     'profilePicture', profilePicture.readAsBytesSync(),
//       //     filename: "profilePicture"));

//       var response = await request.send();

//       log('stream: ${response.stream.toString()}');

//       log('status code: ${response.statusCode}');
//       Get.back();
//       if (response.statusCode == 200) {
//         var resdata = await response.stream.toBytes();
//         var result = String.fromCharCodes(resdata);
//         log("uploadProfile :: $baseUrl/api/allimg/upload/profile/$tid");
//         log(result);
//         var data = jsonDecode(result);
//         log('image uploaded');
//         log('emaillll:${data["email"]}');
//         userController.getCurrentUser();
//         Get.offAll(() => const MainScreen());
//         successToast(msg: 'Image Uploaded successfully!');
//       } else {
//         log('failed');
//         errorToast(msg: 'Image Upload failed!');
//       }
//     } catch (e) {
//       Get.back();
//       errorToast(msg: e.toString());
//       throw Exception(e.toString());
//     }
//   }

//   Future registerUser(String firstname, String lastname, String mobile,
//       String email, String password, String latitude, String longitude) async {
//     try {
//       isLoading.value = true;
//       var data = email != ""
//           ? jsonEncode(
//               <String, dynamic>{
//                 "email": email,
//                 "password": password,
//                 "mobileNumber": mobile,
//                 "fullname": "$firstname $lastname ",
//                 "latitude": latitude,
//                 "longitude": longitude,
//                 "firstName": firstname,
//                 "lastName": lastname,
//               },
//             )
//           : jsonEncode(
//               <String, dynamic>{
//                 "password": password,
//                 "mobileNumber": mobile,
//                 "fullname": "$firstname $lastname ",
//                 "latitude": latitude,
//                 "longitude": longitude,
//                 "firstName": firstname,
//                 "lastName": lastname,
//               },
//             );

//       // consolelog(data);
//       final response = await http.post(
//         Uri.parse('$baseUrl/api/v1/auth/register'),
//         headers: <String, String>{
//           'Content-Type': 'application/json',
//         },
//         body: data,
//       );

//       consolelog("registerUser :: $baseUrl/api/v1/auth/register");
//       consolelog(response.statusCode);
//       // consolelog(response.body);
//       Get.back();
//       isLoading(false);
//       if (response.statusCode == 201) {
//         successToast(msg: "User Registered Successfully");
//         Get.offAll(
//           () => const LoginScreen(),
//         );
//       } else {
//         Get.defaultDialog(
//             buttonColor: Colors.red,
//             titlePadding: const EdgeInsets.only(top: 20),
//             title: 'Error',
//             titleStyle: const TextStyle(color: Colors.black, fontSize: 20),
//             textConfirm: 'Ok',
//             onConfirm: () {
//               Get.back();
//             },
//             content: const Text(
//               'Something went wrong or phone number already exists',
//               textAlign: TextAlign.center,
//             ));
//       }
//     } catch (err) {
//       Get.back();
//       isLoading(false);
//       if (err.toString().contains('SocketException')) {
//         return errorToast(msg: 'no internet');
//       } else {
//         errorToast(msg: err.toString());
//       }
//     }
//   }

//   Future<void> forgotPassword({
//     String? newPassword,
//     String? confirmPassword,
//     String? mobileNumber,
//   }) async {
//     try {
//       isLoading.value = true;
//       final response = await http.post(
//         Uri.parse(
//           '$baseUrl/api/v1/auth/$mobileNumber/changePassword?newPassword=$newPassword&confirmPassword=$confirmPassword',
//         ),
//         headers: <String, String>{
//           'Content-Type': 'application/json',
//         },
//       ).timeout(const Duration(seconds: 20)); // Handle request timeout

//       log(response.body);
//       log(response.statusCode.toString());

//       CommonResponseModel? result = commonResponseModelFromJson(response.body);

//       if (response.statusCode == 200) {
//         successToast(msg: result.message ?? 'Password changed successfully');
//         Get.offAll(() => const LoginScreen());
//       } else {
//         log(result.message ?? "An error occurred");
//         errorToast(msg: result.message ?? 'Error: ${response.statusCode}');
//         isLoading.value = false;
//       }
//     } on SocketException catch (err) {
//       errorToast(msg: 'Network Error: ${err.message}');
//       isLoading.value = false;
//     } on http.ClientException catch (err) {
//       errorToast(msg: 'Client Error: ${err.message}');
//       isLoading.value = false;
//     } catch (err) {
//       errorToast(msg: 'Unexpected error: ${err.toString()}');
//       isLoading.value = false;
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   Future checkNumberAndEmailRegister({
//     String? email,
//     String? mobileNumber,
//   }) async {
//     try {
//       final response = await http.get(
//         Uri.parse(
//             '$baseUrl/api/users/emailAndMobile?email=$email&mobileNumber=$mobileNumber'),
//         headers: <String, String>{
//           'Content-Type': 'application/json',
//         },
//       );
//       log("checkNumberAndEmailRegister :: $baseUrl/api/users/emailAndMobile?email=$email&mobileNumber=$mobileNumber");
//       log('body: ${response.body}');
//       log(response.statusCode.toString());
//       CommonResponseModel? result = CommonResponseModel();
//       if (response.statusCode == 200) {
//         return true;
//       } else {
//         log(result.message ?? "");
//         errorToast(msg: result.message.toString());
//       }
//       return false;
//     } catch (err) {
//       isLoading(false);
//       log(err.toString());
//       if (err.toString().contains('SocketException')) {
//         errorToast(msg: 'no internet');
//         // print('no internet');
//       } else {
//         errorToast(msg: err.toString());
//       }
//       return false;
//     }
//   }

//   // service ,provided by sms provider
//   Future<void> sendOTP(
//       {String? phoneNumber, int? randomNumber, String? message}) async {
//     var response = await http.post(
//       Uri.parse("https://api.sparrowsms.com/v2/sms/"),
//       body: {
//         'token': apiKey,
//         'to': phoneNumber,
//         'from': "TheAlert",
//         'text': """
// From Smart Sewa,
// $message : $randomNumber
// """,
//       },
//     );
//     logger(response.body, loggerType: LoggerType.success);
//     if (response.statusCode == 200) {}
//   }
// }
// import 'dart:convert';
// import 'dart:io';
// import 'dart:developer';
// import 'dart:async';

// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:http/http.dart' as http;
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smartsewa/core/development/console.dart';
// import 'package:smartsewa/core/enum.dart';
// import 'package:smartsewa/core/states.dart';
// import 'package:smartsewa/network/base_client.dart';
// import 'package:smartsewa/network/models/common_response_model.dart';
// import 'package:smartsewa/views/user_screen/main_screen.dart';
// import 'package:smartsewa/views/widgets/Welcome%20Screen/splashscreen.dart';
// import 'package:smartsewa/views/widgets/custom_toasts.dart';
// import '../../../views/auth/login/login_screen.dart';
// import '../userdetails/current_user_controller.dart';

// class AuthController extends GetxController {
//   String? fullName;
//   String? mobileNumber;
//   String? address;
//   String? token;
//   String? email;
//   String? password;
//   String? companyName;
//   String? citizenshipNum;
//   String? issuedDate;
//   String? latitude;
//   String? expiryDate;
//   bool? expiryCheck;
//   var workStatus = false;
//   String? serviceProvided;
//   bool? onlineStatus;

//   bool? role;
//   String? firstName;
//   String? lastName;
//   String? apptoken;
//   var isLoading = false.obs;
//   String baseUrl = BaseClient().baseUrl;
//   var isLogged = false.obs;
//   var isGuest = false.obs;
//   final pickedJobFieldName = ''.obs;
//   final isGuestUser = false.obs;
//   final guestUserData = {
//     'id': '',
//     'name': 'Guest User',
//     'role': 'guest',
//     'permissions': ['view_basic', 'search']
//   }.obs;

//   TextEditingController otpController = TextEditingController();

//   Future<bool> handleGuestLogin() async {
//     try {
//       isLoading.value = true;

//       // Generate a unique guest ID
//       final guestId = 'guest_${DateTime.now().millisecondsSinceEpoch}';

//       // Get shared preferences instance
//       SharedPreferences prefs = await SharedPreferences.getInstance();

//       // Store guest user data
//       await prefs.setBool('isGuestUser', true);
//       await prefs.setString('guestUserId', guestId);
//       await prefs.setBool('isAuthenticated', true);
//       await prefs.setBool('rememberMe', false);

//       // Set guest user state
//       isGuestUser.value = true;
//       guestUserData.value = {
//         'id': guestId,
//         'name': 'Guest User',
//         'role': 'guest',
//         'permissions': ['view_basic', 'search']
//       };

//       // Set default guest user data
//       fullName = 'Guest User';
//       mobileNumber = null;
//       email = null;
//       role = false;
//       workStatus = false;
//       onlineStatus = false;
//       token = null;

//       // Update selected auto login value
//       selectedAutoLogin.value = false;

//       isLoading.value = false;
//       return true;
//     } catch (e) {
//       isLoading.value = false;
//       logger('Guest login error: $e', loggerType: LoggerType.error);
//       return false;
//     }
//   }

//   bool isUserGuest() {
//     return isGuestUser.value;
//   }

//   Future<void> clearGuestSession() async {
//     try {
//       SharedPreferences prefs = await SharedPreferences.getInstance();
//       await prefs.remove('isGuestUser');
//       await prefs.remove('guestUserId');
//       isGuestUser.value = false;
//       guestUserData.value = {};
//     } catch (e) {
//       logger('Error clearing guest session: $e', loggerType: LoggerType.error);
//     }
//   }

//   Future<void> logout() async {
//     try {
//       if (isUserGuest()) {
//         await clearGuestSession();
//       }
//       SharedPreferences prefs = await SharedPreferences.getInstance();
//       await prefs.clear();
//       Get.offAll(() => const LoginScreen());
//     } catch (e) {
//       logger('Logout error: $e', loggerType: LoggerType.error);
//     }
//   }

//   ///***Api hit for sms ***///
//   // String apiKey = 'v2_aD; //token generated
//   String apiKey = '';

//   ///******Api For Login *****///
//   Future login(TextEditingController emailController,
//       TextEditingController passwordController) async {
//     try {
//       final userController = Get.put(CurrentUserController());
//       isLoading.value = true;
//       consolelog(jsonEncode(
//         <String, dynamic>{
//           "username": emailController.text,
//           "password": passwordController.text,
//         },
//       ));
//       consolelog("login :: $baseUrl/api/v1/auth/login");
//       final res = await http
//           .post(
//             Uri.parse('$baseUrl/api/v1/auth/login'),
//             headers: <String, String>{
//               'Content-Type': 'application/json',
//             },
//             body: jsonEncode(
//               <String, dynamic>{
//                 "username": emailController.text,
//                 "password": passwordController.text,
//               },
//             ),
//           )
//           .timeout(const Duration(seconds: 20));
//       consolelog("login :: $baseUrl/api/v1/auth/login");
//       consolelog(res.statusCode);
//       consolelog(res.body);

//       if (res.statusCode == 200) {
//         var user = jsonDecode(res.body);
//         log("login :: $baseUrl/api/v1/auth/login");
//         log(res.body);
//         final apptoken = user['token'];
//         int tid = user['user']['id'];

//         fullName = user['user']['fullName'];
//         mobileNumber = user['user']['mobileNumber'];
//         email = user['user']['email'];
//         password = user['user']['password'];
//         companyName = user['user']['companyName'];
//         role = user['user']['role'];
//         citizenshipNum = user['user']['citizenshipNum'];
//         issuedDate = user['user']['issueDate'];
//         latitude = user['user']['latitude'];
//         expiryDate = user['user']['expiryDate'];
//         workStatus = user['user']['workStatus'];
//         serviceProvided = user['user']["serviceProvided"];
//         firstName = user['user']['firstName'];
//         lastName = user['user']['lastName'];
//         address = user['user']['address'];
//         expiryCheck = user['user']['expiryCheck'];
//         onlineStatus = user['user']['onlineStatus'];
//         userController.getCurrentUser();

//         var prefs = await SharedPreferences.getInstance();
//         logger("workStatus :: $workStatus", loggerType: LoggerType.success);

//         // Reset guest user state
//         await prefs.setBool('isGuestUser', false); // Reset guest flag
//         isGuestUser.value = false; // Update state in AuthController

//         await prefs.setBool(SplashScreenState.keylogin, true);
//         await prefs.setBool("workStatus", workStatus);
//         await prefs.setString('token', apptoken);
//         await prefs.setInt("id", tid);
//         await prefs.setBool("rememberMe", true);
//         await prefs.setBool("isAuthenticated", true);
//         await prefs.setString('username', emailController.text);
//         await prefs.setString('password', passwordController.text);
//         await prefs.setBool('isServiceProviderActive', onlineStatus!);
//         await prefs.setString('latitude', latitude!);
//         selectedAutoLogin.value = prefs.getBool("rememberMe");

//         successToast(msg: "Login successfully");

//         if (apptoken != null && workStatus == true) {
//           Get.offAll(() => const MainScreen());
//           isLoading.value = false;
//         } else {
//           Get.offAll(() => const MainScreen());
//           isLoading.value = false;
//         }
//         emailController.clear();
//         passwordController.clear();
//       } else {
//         isLoading.value = false;
//         errorToast(msg: "Invalid Username or Password");
//       }
//     } on TimeoutException {
//       errorToast(msg: 'Request Timeout');
//       isLoading.value = false;
//     } on http.ClientException {
//       isLoading.value = false;
//       errorToast(msg: 'Client Error');
//     } catch (err) {
//       errorToast(msg: 'Unexpected error');
//       isLoading.value = false;
//     }
//   }

// ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////

//   Future<void> uploadProfile(File? profilePicture,
//       {bool? isFromServiceScreen = false}) async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     String? apptoken = prefs.getString("token");
//     int? tid = prefs.getInt("id");
//     final userController = Get.put(CurrentUserController());

//     log(tid.toString());
//     // log(id.toString());
//     log('Get images init $apptoken');
//     // log('get images $token');
//     try {
//       var profilestream = http.ByteStream(profilePicture!.openRead());

//       // log('frontstream: $frontstream');
//       // // uploading file as a stream
//       // frontstream.cast();
//       // backstream.cast();
//       profilestream.cast();

//       // getting length of file
//       // var frontlength = citizenshipFront.lengthSync();
//       // var backlength = citizenshipBack.lengthSync();
//       var profilelength = profilePicture.lengthSync();
//       log('profilelength: $profilelength');

//       // log('frontlength: $frontlength');

//       // post file
//       var request = http.MultipartRequest(
//           'POST', Uri.parse('$baseUrl/api/allimg/upload/profile/$tid'));
// // http://*************:9000/api/allimg/upload/profile/2007
//       // adding header
//       request.headers.addAll({
//         'Content-Type': 'multipart/form-data',
//         // 'Content-Type': 'charset=UTF-8',
//         'Authorization': 'Bearer $apptoken'
//       });

//       // adding each images
//       // request.files.add(http.MultipartFile.fromBytes(
//       //     'citizenshipFront', citizenshipFront.readAsBytesSync(),
//       //     filename: "citizenshipFront"));
//       // request.files.add(http.MultipartFile.fromBytes(
//       //     'profilePicture', profilePicture.readAsBytesSync(),
//       //     filename: "profilePicture"));
//       request.files.add(http.MultipartFile.fromBytes(
//           'profilePicture', profilePicture.readAsBytesSync(),
//           filename: profilePicture.path.split("/").last));
//       // request.files.add(http.MultipartFile.fromBytes(
//       //     'profilePicture', profilePicture.readAsBytesSync(),
//       //     filename: "profilePicture"));

//       var response = await request.send();

//       log('stream: ${response.stream.toString()}');

//       log('status code: ${response.statusCode}');
//       Get.back();
//       if (response.statusCode == 200) {
//         var resdata = await response.stream.toBytes();
//         var result = String.fromCharCodes(resdata);
//         log("uploadProfile :: $baseUrl/api/allimg/upload/profile/$tid");
//         log(result);
//         var data = jsonDecode(result);
//         log('image uploaded');
//         log('emaillll:${data["email"]}');
//         userController.getCurrentUser();
//         Get.offAll(() => const MainScreen());
//         successToast(msg: 'Image Uploaded successfully!');
//       } else {
//         log('failed');
//         errorToast(msg: 'Image Upload failed!');
//       }
//     } catch (e) {
//       Get.back();
//       errorToast(msg: e.toString());
//       throw Exception(e.toString());
//     }
//   }

//   Future registerUser(String firstname, String lastname, String mobile,
//       String email, String password, String latitude, String longitude) async {
//     try {
//       isLoading.value = true;
//       var data = email != ""
//           ? jsonEncode(
//               <String, dynamic>{
//                 "email": email,
//                 "password": password,
//                 "mobileNumber": mobile,
//                 "fullname": "$firstname $lastname ",
//                 "latitude": latitude,
//                 "longitude": longitude,
//                 "firstName": firstname,
//                 "lastName": lastname,
//               },
//             )
//           : jsonEncode(
//               <String, dynamic>{
//                 "password": password,
//                 "mobileNumber": mobile,
//                 "fullname": "$firstname $lastname ",
//                 "latitude": latitude,
//                 "longitude": longitude,
//                 "firstName": firstname,
//                 "lastName": lastname,
//               },
//             );

//       // consolelog(data);
//       final response = await http.post(
//         Uri.parse('$baseUrl/api/v1/auth/register'),
//         headers: <String, String>{
//           'Content-Type': 'application/json',
//         },
//         body: data,
//       );

//       consolelog("registerUser :: $baseUrl/api/v1/auth/register");
//       consolelog(response.statusCode);
//       // consolelog(response.body);
//       Get.back();
//       isLoading(false);
//       if (response.statusCode == 201) {
//         successToast(msg: "User Registered Successfully");
//         Get.offAll(
//           () => const LoginScreen(),
//         );
//       } else {
//         Get.defaultDialog(
//             buttonColor: Colors.red,
//             titlePadding: const EdgeInsets.only(top: 20),
//             title: 'Error',
//             titleStyle: const TextStyle(color: Colors.black, fontSize: 20),
//             textConfirm: 'Ok',
//             onConfirm: () {
//               Get.back();
//             },
//             content: const Text(
//               'Something went wrong or phone number already exists',
//               textAlign: TextAlign.center,
//             ));
//       }
//     } catch (err) {
//       Get.back();
//       isLoading(false);
//       if (err.toString().contains('SocketException')) {
//         return errorToast(msg: 'no internet');
//       } else {
//         errorToast(msg: err.toString());
//       }
//     }
//   }

//   Future<void> forgotPassword({
//     String? newPassword,
//     String? confirmPassword,
//     String? mobileNumber,
//   }) async {
//     try {
//       isLoading.value = true;
//       final response = await http.post(
//         Uri.parse(
//           '$baseUrl/api/v1/auth/$mobileNumber/changePassword?newPassword=$newPassword&confirmPassword=$confirmPassword',
//         ),
//         headers: <String, String>{
//           'Content-Type': 'application/json',
//         },
//       ).timeout(const Duration(seconds: 20)); // Handle request timeout

//       log(response.body);
//       log(response.statusCode.toString());

//       CommonResponseModel? result = commonResponseModelFromJson(response.body);

//       if (response.statusCode == 200) {
//         successToast(msg: result.message ?? 'Password changed successfully');
//         Get.offAll(() => const LoginScreen());
//       } else {
//         log(result.message ?? "An error occurred");
//         errorToast(msg: result.message ?? 'Error: ${response.statusCode}');
//         isLoading.value = false;
//       }
//     } on SocketException catch (err) {
//       errorToast(msg: 'Network Error: ${err.message}');
//       isLoading.value = false;
//     } on http.ClientException catch (err) {
//       errorToast(msg: 'Client Error: ${err.message}');
//       isLoading.value = false;
//     } catch (err) {
//       errorToast(msg: 'Unexpected error: ${err.toString()}');
//       isLoading.value = false;
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   Future checkNumberAndEmailRegister({
//     String? email,
//     String? mobileNumber,
//   }) async {
//     try {
//       final response = await http.get(
//         Uri.parse(
//             '$baseUrl/api/users/emailAndMobile?email=$email&mobileNumber=$mobileNumber'),
//         headers: <String, String>{
//           'Content-Type': 'application/json',
//         },
//       );
//       log("checkNumberAndEmailRegister :: $baseUrl/api/users/emailAndMobile?email=$email&mobileNumber=$mobileNumber");
//       log('body: ${response.body}');
//       log(response.statusCode.toString());
//       CommonResponseModel? result = CommonResponseModel();
//       if (response.statusCode == 200) {
//         return true;
//       } else {
//         log(result.message ?? "");
//         errorToast(msg: result.message.toString());
//       }
//       return false;
//     } catch (err) {
//       isLoading(false);
//       log(err.toString());
//       if (err.toString().contains('SocketException')) {
//         errorToast(msg: 'no internet');
//         // print('no internet');
//       } else {
//         errorToast(msg: err.toString());
//       }
//       return false;
//     }
//   }

//   // service ,provided by sms provider
//   Future<void> sendOTP(
//       {String? phoneNumber, int? randomNumber, String? message}) async {
//     var response = await http.post(
//       Uri.parse("https://api.sparrowsms.com/v2/sms/"),
//       body: {
//         'token': apiKey,
//         'to': phoneNumber,
//         'from': "TheAlert",
//         'text': """
// From Smart Sewa,
// $message : $randomNumber
// """,
//       },
//     );
//     logger(response.body, loggerType: LoggerType.success);
//     if (response.statusCode == 200) {}
//   }
// }

// import 'dart:convert';
// import 'dart:io';
// import 'dart:developer';
// import 'dart:async';

// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:http/http.dart' as http;
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smartsewa/core/development/console.dart';
// import 'package:smartsewa/core/enum.dart';
// import 'package:smartsewa/core/states.dart';
// import 'package:smartsewa/network/base_client.dart';
// import 'package:smartsewa/network/models/common_response_model.dart';
// import 'package:smartsewa/views/user_screen/main_screen.dart';
// import 'package:smartsewa/views/widgets/Welcome%20Screen/splashscreen.dart';
// import 'package:smartsewa/views/widgets/custom_toasts.dart';
// import '../../../views/auth/login/login_screen.dart';
// import '../userdetails/current_user_controller.dart';

// class AuthController extends GetxController {
//   String? fullName;
//   String? mobileNumber;
//   String? address;
//   String? token;
//   String? email;
//   String? password;
//   String? companyName;
//   String? citizenshipNum;
//   String? issuedDate;
//   String? latitude;
//   String? expiryDate;
//   bool? expiryCheck;
//   var workStatus = false;
//   String? serviceProvided;
//   bool? onlineStatus;

//   bool? role;
//   String? firstName;
//   String? lastName;
//   String? apptoken;
//   var isLoading = false.obs;
//   String baseUrl = BaseClient().baseUrl;
//   var isLogged = false.obs;
//   var isGuest = false.obs;
//   final pickedJobFieldName = ''.obs;
//   final isGuestUser = false.obs;
//   final guestUserData = {
//     'id': '',
//     'name': 'Guest User',
//     'role': 'guest',
//     'permissions': ['view_basic', 'search']
//   }.obs;

//   TextEditingController otpController = TextEditingController();

//   // Error handling helper method
//   void _handleError(dynamic error, {String customMessage = ''}) {
//     isLoading.value = false;
//     String errorMessage = customMessage.isNotEmpty
//         ? customMessage
//         : 'An unexpected error occurred';

//     if (error is SocketException) {
//       errorMessage =
//           'Network connection error. Please check your internet connection.';
//     } else if (error is TimeoutException) {
//       errorMessage = 'Request timed out. Please try again.';
//     } else if (error is http.ClientException) {
//       errorMessage = 'Client connection error. Please try again.';
//     } else if (error is FormatException) {
//       errorMessage = 'Data format error. Please contact support.';
//     }

//     logger('Error: $error', loggerType: LoggerType.error);
//     errorToast(msg: errorMessage);
//   }

//   // Helper method to handle API responses
//   Future<Map<String, dynamic>?> _handleApiResponse(
//       http.Response response, String endpoint) async {
//     logger("$endpoint :: Status Code: ${response.statusCode}",
//         loggerType: LoggerType.info);

//     if (response.statusCode >= 200 && response.statusCode < 300) {
//       try {
//         if (response.body.isEmpty) {
//           return {};
//         }
//         return jsonDecode(response.body);
//       } catch (e) {
//         _handleError(e, customMessage: 'Invalid response format');
//         return null;
//       }
//     } else {
//       String errorMessage =
//           'Request failed with status: ${response.statusCode}';
//       try {
//         final errorBody = jsonDecode(response.body);
//         if (errorBody != null && errorBody['message'] != null) {
//           errorMessage = errorBody['message'];
//         }
//       } catch (_) {
//         // If parsing fails, use default error message
//       }

//       _handleError(null, customMessage: errorMessage);
//       return null;
//     }
//   }

//   Future<bool> handleGuestLogin() async {
//     try {
//       isLoading.value = true;

//       // Generate a unique guest ID
//       final guestId = 'guest_${DateTime.now().millisecondsSinceEpoch}';

//       // Get shared preferences instance
//       SharedPreferences prefs = await SharedPreferences.getInstance();

//       // Store guest user data
//       await prefs.setBool('isGuestUser', true);
//       await prefs.setString('guestUserId', guestId);
//       await prefs.setBool('isAuthenticated', true);
//       await prefs.setBool('rememberMe', false);

//       // Set guest user state
//       isGuestUser.value = true;
//       guestUserData.value = {
//         'id': guestId,
//         'name': 'Guest User',
//         'role': 'guest',
//         'permissions': ['view_basic', 'search']
//       };

//       // Set default guest user data
//       fullName = 'Guest User';
//       mobileNumber = null;
//       email = null;
//       role = false;
//       workStatus = false;
//       onlineStatus = false;
//       token = null;

//       // Update selected auto login value
//       selectedAutoLogin.value = false;

//       isLoading.value = false;
//       return true;
//     } catch (e) {
//       _handleError(e, customMessage: 'Guest login failed');
//       return false;
//     }
//   }

//   bool isUserGuest() {
//     return isGuestUser.value;
//   }

//   Future<void> clearGuestSession() async {
//     try {
//       SharedPreferences prefs = await SharedPreferences.getInstance();
//       await prefs.remove('isGuestUser');
//       await prefs.remove('guestUserId');
//       isGuestUser.value = false;
//       guestUserData.value = {};
//     } catch (e) {
//       _handleError(e, customMessage: 'Error clearing guest session');
//     }
//   }

//   Future<void> logout() async {
//     try {
//       if (isUserGuest()) {
//         await clearGuestSession();
//       }
//       SharedPreferences prefs = await SharedPreferences.getInstance();
//       await prefs.clear();
//       Get.offAll(() => const LoginScreen());
//     } catch (e) {
//       _handleError(e, customMessage: 'Logout failed');
//     }
//   }

//   String apiKey = '';

//   Future<bool> login(TextEditingController emailController,
//       TextEditingController passwordController) async {
//     isLoading.value = true;
//     final endpoint = "$baseUrl/api/v1/auth/login";

//     try {
//       final userController = Get.put(CurrentUserController());
//       final loginData = {
//         "username": emailController.text,
//         "password": passwordController.text,
//       };

//       consolelog("login :: $endpoint");
//       consolelog(jsonEncode(loginData));

//       final res = await http
//           .post(
//             Uri.parse(endpoint),
//             headers: <String, String>{
//               'Content-Type': 'application/json',
//             },
//             body: jsonEncode(loginData),
//           )
//           .timeout(const Duration(seconds: 20));

//       final responseData = await _handleApiResponse(res, endpoint);

//       if (responseData == null) {
//         return false;
//       }

//       var user = responseData;
//       final apptoken = user['token'];
//       int tid = user['user']['id'];

//       // Set user data
//       fullName = user['user']['fullName'];
//       mobileNumber = user['user']['mobileNumber'];
//       email = user['user']['email'];
//       password = user['user']['password'];
//       companyName = user['user']['companyName'];
//       role = user['user']['role'];
//       citizenshipNum = user['user']['citizenshipNum'];
//       issuedDate = user['user']['issueDate'];
//       latitude = user['user']['latitude'];
//       expiryDate = user['user']['expiryDate'];
//       workStatus = user['user']['workStatus'];
//       serviceProvided = user['user']["serviceProvided"];
//       firstName = user['user']['firstName'];
//       lastName = user['user']['lastName'];
//       address = user['user']['address'];
//       expiryCheck = user['user']['expiryCheck'];
//       onlineStatus = user['user']['onlineStatus'];
//       userController.getCurrentUser();

//       var prefs = await SharedPreferences.getInstance();
//       logger("workStatus :: $workStatus", loggerType: LoggerType.success);

//       // Reset guest user state
//       await prefs.setBool('isGuestUser', false); // Reset guest flag
//       isGuestUser.value = false; // Update state in AuthController

//       // Save user data to preferences
//       await prefs.setBool(SplashScreenState.keylogin, true);
//       await prefs.setBool("workStatus", workStatus);
//       await prefs.setString('token', apptoken);
//       await prefs.setInt("id", tid);
//       await prefs.setBool("rememberMe", true);
//       await prefs.setBool("isAuthenticated", true);
//       await prefs.setString('username', emailController.text);
//       await prefs.setString('password', passwordController.text);
//       await prefs.setBool('isServiceProviderActive', onlineStatus!);
//       await prefs.setString('latitude', latitude!);
//       selectedAutoLogin.value = prefs.getBool("rememberMe");

//       successToast(msg: "Login successful");

//       if (apptoken != null && workStatus == true) {
//         Get.offAll(() => const MainScreen());
//       } else {
//         Get.offAll(() => const MainScreen());
//       }

//       emailController.clear();
//       passwordController.clear();
//       isLoading.value = false;
//       return true;
//     } on TimeoutException {
//       _handleError(null, customMessage: 'Request timed out. Please try again.');
//       return false;
//     } on SocketException {
//       _handleError(null,
//           customMessage:
//               'Network connection error. Please check your internet connection.');
//       return false;
//     } catch (err) {
//       _handleError(err);
//       return false;
//     }
//   }

//   Future<bool> uploadProfile(File? profilePicture,
//       {bool? isFromServiceScreen = false}) async {
//     if (profilePicture == null) {
//       errorToast(msg: 'No profile picture selected');
//       return false;
//     }

//     try {
//       isLoading.value = true;
//       SharedPreferences prefs = await SharedPreferences.getInstance();
//       String? apptoken = prefs.getString("token");
//       int? tid = prefs.getInt("id");

//       if (apptoken == null || tid == null) {
//         errorToast(msg: 'Authentication error. Please login again.');
//         return false;
//       }

//       final userController = Get.put(CurrentUserController());
//       final endpoint = '$baseUrl/api/allimg/upload/profile/$tid';

//       log('Uploading profile: $endpoint');
//       var profilestream = http.ByteStream(profilePicture.openRead());
//       profilestream.cast();

//       var profilelength = profilePicture.lengthSync();
//       log('Profile file size: $profilelength bytes');

//       var request = http.MultipartRequest('POST', Uri.parse(endpoint));

//       // Adding header
//       request.headers.addAll({
//         'Content-Type': 'multipart/form-data',
//         'Authorization': 'Bearer $apptoken'
//       });

//       // Adding profile picture
//       request.files.add(http.MultipartFile.fromBytes(
//           'profilePicture', profilePicture.readAsBytesSync(),
//           filename: profilePicture.path.split("/").last));

//       // Send request with timeout
//       final streamedResponse = await request.send().timeout(
//         const Duration(seconds: 30),
//         onTimeout: () {
//           throw TimeoutException('Request timed out');
//         },
//       );

//       log('Response status code: ${streamedResponse.statusCode}');

//       if (streamedResponse.statusCode >= 200 &&
//           streamedResponse.statusCode < 300) {
//         var resdata = await streamedResponse.stream.toBytes();
//         var result = String.fromCharCodes(resdata);
//         log("uploadProfile response: $result");

//         try {
//           var data = jsonDecode(result);
//           log('Image uploaded successfully');
//           userController.getCurrentUser();
//           Get.offAll(() => const MainScreen());
//           successToast(msg: 'Profile image uploaded successfully!');
//           isLoading.value = false;
//           return true;
//         } catch (e) {
//           _handleError(e, customMessage: 'Error processing server response');
//           return false;
//         }
//       } else {
//         _handleError(null,
//             customMessage:
//                 'Image upload failed with status: ${streamedResponse.statusCode}');
//         return false;
//       }
//     } on TimeoutException {
//       _handleError(null,
//           customMessage:
//               'Upload timed out. Please try again with a smaller image.');
//       return false;
//     } catch (e) {
//       _handleError(e, customMessage: 'Profile upload failed');
//       return false;
//     } finally {
//       isLoading.value = false;
//       Get.back();
//     }
//   }

//   Future<bool> registerUser(String firstname, String lastname, String mobile,
//       String email, String password, String latitude, String longitude) async {
//     try {
//       isLoading.value = true;
//       final endpoint = '$baseUrl/api/v1/auth/register';

//       // Prepare request data
//       Map<String, dynamic> requestData = {
//         "password": password,
//         "mobileNumber": mobile,
//         "fullname": "$firstname $lastname",
//         "latitude": latitude,
//         "longitude": longitude,
//         "firstName": firstname,
//         "lastName": lastname,
//       };

//       // Add email if provided
//       if (email.isNotEmpty) {
//         requestData["email"] = email;
//       }

//       final response = await http
//           .post(
//             Uri.parse(endpoint),
//             headers: <String, String>{
//               'Content-Type': 'application/json',
//             },
//             body: jsonEncode(requestData),
//           )
//           .timeout(const Duration(seconds: 20));

//       consolelog("registerUser :: $endpoint");
//       consolelog(response.statusCode);

//       if (response.statusCode == 201) {
//         successToast(msg: "User registered successfully");
//         Get.offAll(() => const LoginScreen());
//         isLoading.value = false;
//         return true;
//       } else {
//         String errorMessage = 'Registration failed';

//         // Try to extract error message from response
//         try {
//           final errorBody = jsonDecode(response.body);
//           if (errorBody != null && errorBody['message'] != null) {
//             errorMessage = errorBody['message'];
//           }
//         } catch (_) {
//           // If parsing fails, check common status codes
//           if (response.statusCode == 409) {
//             errorMessage = 'User with this phone number already exists';
//           } else if (response.statusCode == 400) {
//             errorMessage = 'Invalid registration data';
//           } else {
//             errorMessage =
//                 'Registration failed with status: ${response.statusCode}';
//           }
//         }

//         Get.defaultDialog(
//             buttonColor: Colors.red,
//             titlePadding: const EdgeInsets.only(top: 20),
//             title: 'Registration Error',
//             titleStyle: const TextStyle(color: Colors.black, fontSize: 20),
//             textConfirm: 'Ok',
//             onConfirm: () {
//               Get.back();
//             },
//             content: Text(
//               errorMessage,
//               textAlign: TextAlign.center,
//             ));

//         isLoading.value = false;
//         return false;
//       }
//     } on TimeoutException {
//       _handleError(null,
//           customMessage: 'Registration request timed out. Please try again.');
//       return false;
//     } on SocketException {
//       _handleError(null,
//           customMessage:
//               'Network connection error. Please check your internet connection.');
//       return false;
//     } catch (err) {
//       _handleError(err, customMessage: 'Registration failed');
//       return false;
//     } finally {
//       isLoading.value = false;
//       Get.back();
//     }
//   }

//   Future<bool> forgotPassword({
//     String? newPassword,
//     String? confirmPassword,
//     String? mobileNumber,
//   }) async {
//     if (newPassword == null ||
//         newPassword.isEmpty ||
//         confirmPassword == null ||
//         confirmPassword.isEmpty ||
//         mobileNumber == null ||
//         mobileNumber.isEmpty) {
//       errorToast(msg: 'All fields are required');
//       return false;
//     }

//     if (newPassword != confirmPassword) {
//       errorToast(msg: 'Passwords do not match');
//       return false;
//     }

//     try {
//       isLoading.value = true;
//       final endpoint =
//           '$baseUrl/api/v1/auth/$mobileNumber/changePassword?newPassword=$newPassword&confirmPassword=$confirmPassword';

//       final response = await http.post(
//         Uri.parse(endpoint),
//         headers: <String, String>{
//           'Content-Type': 'application/json',
//         },
//       ).timeout(const Duration(seconds: 20));

//       log("forgotPassword :: $endpoint");
//       log("Status code: ${response.statusCode}");

//       if (response.body.isNotEmpty) {
//         log("Response: ${response.body}");
//       }

//       CommonResponseModel? result;
//       try {
//         result = commonResponseModelFromJson(response.body);
//       } catch (e) {
//         log("Error parsing response: $e");
//       }

//       if (response.statusCode == 200) {
//         successToast(msg: result?.message ?? 'Password changed successfully');
//         Get.offAll(() => const LoginScreen());
//         isLoading.value = false;
//         return true;
//       } else {
//         String errorMessage = result?.message ?? 'Password change failed';
//         if (response.statusCode == 404) {
//           errorMessage = 'User with this mobile number not found';
//         } else if (response.statusCode == 400) {
//           errorMessage = 'Invalid password data';
//         }

//         errorToast(msg: errorMessage);
//         isLoading.value = false;
//         return false;
//       }
//     } on SocketException catch (err) {
//       _handleError(err, customMessage: 'Network connection error');
//       return false;
//     } on http.ClientException catch (err) {
//       _handleError(err, customMessage: 'Client error');
//       return false;
//     } on TimeoutException {
//       _handleError(null, customMessage: 'Request timed out');
//       return false;
//     } catch (err) {
//       _handleError(err, customMessage: 'Password change failed');
//       return false;
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   Future<bool> checkNumberAndEmailRegister({
//     String? email,
//     String? mobileNumber,
//   }) async {
//     if ((email == null || email.isEmpty) &&
//         (mobileNumber == null || mobileNumber.isEmpty)) {
//       errorToast(msg: 'Email or mobile number is required');
//       return false;
//     }

//     try {
//       isLoading.value = true;
//       final endpoint =
//           '$baseUrl/api/users/emailAndMobile?email=$email&mobileNumber=$mobileNumber';

//       final response = await http.get(
//         Uri.parse(endpoint),
//         headers: <String, String>{
//           'Content-Type': 'application/json',
//         },
//       ).timeout(const Duration(seconds: 15));

//       log("checkNumberAndEmailRegister :: $endpoint");
//       log("Status code: ${response.statusCode}");

//       if (response.body.isNotEmpty) {
//         log("Response: ${response.body}");
//       }

//       if (response.statusCode == 200) {
//         isLoading.value = false;
//         return true;
//       } else {
//         String errorMessage = 'Validation failed';

//         try {
//           final errorBody = jsonDecode(response.body);
//           if (errorBody != null && errorBody['message'] != null) {
//             errorMessage = errorBody['message'];
//           }
//         } catch (_) {
//           if (response.statusCode == 409) {
//             errorMessage = 'Email or mobile number already in use';
//           } else if (response.statusCode == 400) {
//             errorMessage = 'Invalid email or mobile number format';
//           }
//         }

//         errorToast(msg: errorMessage);
//         isLoading.value = false;
//         return false;
//       }
//     } on TimeoutException {
//       _handleError(null, customMessage: 'Request timed out');
//       return false;
//     } on SocketException {
//       _handleError(null, customMessage: 'Network connection error');
//       return false;
//     } catch (err) {
//       _handleError(err, customMessage: 'Validation check failed');
//       return false;
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   Future<bool> sendOTP(
//       {String? phoneNumber, int? randomNumber, String? message}) async {
//     if (phoneNumber == null ||
//         phoneNumber.isEmpty ||
//         randomNumber == null ||
//         message == null ||
//         message.isEmpty) {
//       errorToast(msg: 'Missing required parameters for OTP');
//       return false;
//     }

//     if (apiKey.isEmpty) {
//       errorToast(msg: 'SMS API key not configured');
//       return false;
//     }

//     try {
//       isLoading.value = true;
//       final endpoint = "https://api.sparrowsms.com/v2/sms/";

//       final response = await http.post(
//         Uri.parse(endpoint),
//         body: {
//           'token': apiKey,
//           'to': phoneNumber,
//           'from': "TheAlert",
//           'text': """
// From Smart Sewa,
// $message : $randomNumber
// """,
//         },
//       ).timeout(const Duration(seconds: 20));

//       logger("sendOTP :: $endpoint", loggerType: LoggerType.info);
//       logger("Status code: ${response.statusCode}",
//           loggerType: LoggerType.info);
//       logger("Response: ${response.body}", loggerType: LoggerType.success);

//       if (response.statusCode == 200) {
//         try {
//           final responseData = jsonDecode(response.body);
//           final bool success = responseData['success'] ?? false;

//           if (success) {
//             successToast(msg: 'OTP sent successfully');
//             isLoading.value = false;
//             return true;
//           } else {
//             final String errorMessage =
//                 responseData['message'] ?? 'OTP sending failed';
//             errorToast(msg: errorMessage);
//             isLoading.value = false;
//             return false;
//           }
//         } catch (e) {
//           // Even if JSON parsing fails, HTTP 200 usually means success for SMS APIs
//           successToast(msg: 'OTP sent successfully');
//           isLoading.value = false;
//           return true;
//         }
//       } else {
//         String errorMessage = 'Failed to send OTP';

//         try {
//           final responseData = jsonDecode(response.body);
//           errorMessage = responseData['message'] ??
//               'OTP sending failed with status: ${response.statusCode}';
//         } catch (_) {
//           // Use default message if parsing fails
//         }

//         errorToast(msg: errorMessage);
//         isLoading.value = false;
//         return false;
//       }
//     } on TimeoutException {
//       _handleError(null, customMessage: 'OTP request timed out');
//       return false;
//     } on SocketException {
//       _handleError(null, customMessage: 'Network connection error');
//       return false;
//     } catch (err) {
//       _handleError(err, customMessage: 'Failed to send OTP');
//       return false;
//     } finally {
//       isLoading.value = false;
//     }
//   }
// }

import 'dart:convert';
import 'dart:io';
import 'dart:developer';
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/core/enum.dart';
import 'package:smartsewa/core/states.dart';
import 'package:smartsewa/network/base_client.dart';
import 'package:smartsewa/network/models/common_response_model.dart';
import 'package:smartsewa/views/user_screen/main_screen.dart';
import 'package:smartsewa/views/widgets/Welcome%20Screen/splashscreen.dart';
import 'package:smartsewa/views/widgets/custom_toasts.dart';
import '../../../views/auth/login/login_screen.dart';
import '../userdetails/current_user_controller.dart';

class AuthController extends GetxController {
  String? fullName;
  String? mobileNumber;
  String? address;
  String? token;
  String? email;
  String? password;
  String? companyName;
  String? citizenshipNum;
  String? issuedDate;
  String? latitude;
  String? expiryDate;
  bool? expiryCheck;
  var workStatus = false;
  String? serviceProvided;
  bool? onlineStatus;

  bool? role;
  String? firstName;
  String? lastName;
  String? apptoken;
  var isLoading = false.obs;
  String baseUrl = BaseClient().baseUrl;
  var isLogged = false.obs;
  var isGuest = false.obs;
  final pickedJobFieldName = ''.obs;
  final isGuestUser = false.obs;
  final guestUserData = {
    'id': '',
    'name': 'Guest User',
    'role': 'guest',
    'permissions': ['view_basic', 'search']
  }.obs;

  TextEditingController otpController = TextEditingController();
  String apiKey = '';

  // Enhanced error handling helper method with better logging and user feedback
  void _handleError(dynamic error,
      {String customMessage = '', bool showToast = true}) {
    isLoading.value = false;
    String errorMessage = customMessage.isNotEmpty
        ? customMessage
        : 'An unexpected error occurred';

    if (error is SocketException) {
      errorMessage =
          'Network connection error. Please check your internet connection.';
    } else if (error is TimeoutException) {
      errorMessage = 'Request timed out. Please try again.';
    } else if (error is http.ClientException) {
      errorMessage = 'Client connection error. Please try again.';
    } else if (error is FormatException) {
      errorMessage = 'Data format error. Please contact support.';
    }

    logger('Error: $error', loggerType: LoggerType.error);
    if (showToast) {
      errorToast(msg: errorMessage);
    }
    return;
  }

  // Improved API response handler with better error parsing and response validation
  Future<Map<String, dynamic>?> _handleApiResponse(
      http.Response response, String endpoint,
      {bool showToast = true}) async {
    logger("$endpoint :: Status Code: ${response.statusCode}",
        loggerType: LoggerType.info);

    try {
      // Log response body for debugging
      if (response.body.isNotEmpty) {
        logger("Response body: ${response.body}", loggerType: LoggerType.info);
      } else {
        logger("Response body is empty", loggerType: LoggerType.warning);
      }

      // Handle successful responses
      if (response.statusCode >= 200 && response.statusCode < 300) {
        if (response.body.isEmpty) {
          return {};
        }

        try {
          final Map<String, dynamic> parsedJson = jsonDecode(response.body);
          return parsedJson;
        } on FormatException catch (e) {
          _handleError(e,
              customMessage: 'Invalid response format from server',
              showToast: showToast);
          return null;
        }
      } else {
        // Enhanced error handling for different status codes
        String errorMessage =
            'Request failed with status: ${response.statusCode}';

        try {
          if (response.body.isNotEmpty) {
            final errorBody = jsonDecode(response.body);
            if (errorBody != null) {
              if (errorBody['message'] != null) {
                errorMessage = errorBody['message'];
              } else if (errorBody['error'] != null) {
                errorMessage = errorBody['error'];
              }
            }
          }
        } catch (e) {
          logger("Error parsing error response: $e",
              loggerType: LoggerType.error);
        }

        // Handle common HTTP status codes
        switch (response.statusCode) {
          case 400:
            errorMessage = ' $errorMessage';
            break;
          case 401:
            errorMessage = 'Authentication failed: $errorMessage';
            break;
          case 403:
            errorMessage = 'Permission denied: $errorMessage';
            break;
          case 404:
            errorMessage = 'Resource not found: $errorMessage';
            break;
          case 409:
            errorMessage = 'Conflict: $errorMessage';
            break;
          case 500:
            errorMessage = 'Server error: $errorMessage';
            break;
        }

        _handleError(null, customMessage: errorMessage, showToast: showToast);
        return null;
      }
    } catch (e) {
      _handleError(e,
          customMessage: 'Error processing response', showToast: showToast);
      return null;
    }
  }

  // Consistent method to parse and handle streaming responses
  Future<Map<String, dynamic>?> _handleStreamedResponse(
      http.StreamedResponse response, String endpoint) async {
    try {
      logger("$endpoint :: Status Code: ${response.statusCode}",
          loggerType: LoggerType.info);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final bytes = await response.stream.toBytes();
        final String responseString = utf8.decode(bytes);

        logger("Streamed response: $responseString",
            loggerType: LoggerType.info);

        if (responseString.isEmpty) {
          return {};
        }

        try {
          final Map<String, dynamic> parsedJson = jsonDecode(responseString);
          return parsedJson;
        } on FormatException catch (e) {
          _handleError(e, customMessage: 'Invalid streamed response format');
          return null;
        }
      } else {
        String errorMessage =
            'Request failed with status: ${response.statusCode}';

        try {
          final bytes = await response.stream.toBytes();
          final String errorString = utf8.decode(bytes);

          if (errorString.isNotEmpty) {
            final errorBody = jsonDecode(errorString);
            if (errorBody != null && errorBody['message'] != null) {
              errorMessage = errorBody['message'];
            }
          }
        } catch (e) {
          logger("Error parsing streamed error response: $e",
              loggerType: LoggerType.error);
        }

        _handleError(null, customMessage: errorMessage);
        return null;
      }
    } catch (e) {
      _handleError(e, customMessage: 'Error processing streamed response');
      return null;
    }
  }

  Future<bool> handleGuestLogin() async {
    try {
      isLoading.value = true;

      // Generate a unique guest ID
      final guestId = 'guest_${DateTime.now().millisecondsSinceEpoch}';

      // Get shared preferences instance
      SharedPreferences prefs = await SharedPreferences.getInstance();

      // Store guest user data
      await prefs.setBool('isGuestUser', true);
      await prefs.setString('guestUserId', guestId);
      await prefs.setBool('isAuthenticated', true);
      await prefs.setBool('rememberMe', false);

      // Set guest user state
      isGuestUser.value = true;
      guestUserData.value = {
        'id': guestId,
        'name': 'Guest User',
        'role': 'guest',
        'permissions': ['view_basic', 'search']
      };

      // Set default guest user data
      fullName = 'Guest User';
      mobileNumber = null;
      email = null;
      role = false;
      workStatus = false;
      onlineStatus = false;
      token = null;

      // Update selected auto login value
      selectedAutoLogin.value = false;

      isLoading.value = false;
      successToast(msg: "Logged in as guest");
      return true;
    } catch (e) {
      _handleError(e, customMessage: 'Guest login failed');
      return false;
    }
  }

  bool isUserGuest() {
    return isGuestUser.value;
  }

  Future<void> clearGuestSession() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.remove('isGuestUser');
      await prefs.remove('guestUserId');
      isGuestUser.value = false;
      guestUserData.value = {};
    } catch (e) {
      _handleError(e, customMessage: 'Error clearing guest session');
    }
  }

  Future<void> logout() async {
    try {
      if (isUserGuest()) {
        await clearGuestSession();
      }
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      successToast(msg: "Logged out successfully");
      Get.offAll(() => const LoginScreen());
    } catch (e) {
      _handleError(e, customMessage: 'Logout failed');
    }
  }

  Future<bool> login(TextEditingController emailController,
      TextEditingController passwordController) async {
    isLoading.value = true;
    final endpoint = "$baseUrl/api/v1/auth/login";

    try {
      final userController = Get.put(CurrentUserController());
      final loginData = {
        "username": emailController.text,
        "password": passwordController.text,
      };

      consolelog("login :: $endpoint");
      consolelog(jsonEncode(loginData));

      final res = await http
          .post(
            Uri.parse(endpoint),
            headers: <String, String>{
              'Content-Type': 'application/json',
            },
            body: jsonEncode(loginData),
          )
          .timeout(const Duration(seconds: 20));

      final responseData = await _handleApiResponse(res, endpoint);

      if (responseData == null) {
        isLoading.value = false;
        return false;
      }

      // Check if the response has the expected structure
      if (!responseData.containsKey('token') ||
          !responseData.containsKey('user')) {
        _handleError(null,
            customMessage: 'Invalid response format from server');
        isLoading.value = false;
        return false;
      }

      var user = responseData;
      final apptoken = user['token'];

      // Safely extract user ID with null checks
      int tid = -1;
      if (user['user'] != null && user['user']['id'] != null) {
        tid = user['user']['id'];
      } else {
        _handleError(null, customMessage: 'User ID not found in response');
        isLoading.value = false;
        return false;
      }

      // Set user data with null safety
      fullName = user['user']['fullName'];
      mobileNumber = user['user']['mobileNumber'];
      email = user['user']['email'];
      password = user['user']['password'];
      companyName = user['user']['companyName'];
      role = user['user']['role'];
      citizenshipNum = user['user']['citizenshipNum'];
      issuedDate = user['user']['issueDate'];
      latitude = user['user']['latitude'] ?? '';
      expiryDate = user['user']['expiryDate'];
      workStatus = user['user']['workStatus'] ?? false;
      serviceProvided = user['user']["serviceProvided"];
      firstName = user['user']['firstName'];
      lastName = user['user']['lastName'];
      address = user['user']['address'];
      expiryCheck = user['user']['expiryCheck'];
      onlineStatus = user['user']['onlineStatus'] ?? false;

      userController.getCurrentUser();

      var prefs = await SharedPreferences.getInstance();
      logger("workStatus :: $workStatus", loggerType: LoggerType.success);

      // Reset guest user state
      await prefs.setBool('isGuestUser', false);
      isGuestUser.value = false;

      // Save user data to preferences with null safety
      await prefs.setBool(SplashScreenState.keylogin, true);
      await prefs.setBool("workStatus", workStatus);
      await prefs.setString('token', apptoken);
      await prefs.setInt("id", tid);
      await prefs.setBool("rememberMe", true);
      await prefs.setBool("isAuthenticated", true);
      await prefs.setString('username', emailController.text);
      await prefs.setString('password', passwordController.text);
      await prefs.setBool('isServiceProviderActive', onlineStatus ?? false);
      await prefs.setString('latitude', latitude ?? '');
      selectedAutoLogin.value = prefs.getBool("rememberMe");

      successToast(msg: "Login successful");

      if (apptoken != null && workStatus == true) {
        Get.offAll(() => const MainScreen());
      } else {
        Get.offAll(() => const MainScreen());
      }

      emailController.clear();
      passwordController.clear();
      isLoading.value = false;
      return true;
    } on TimeoutException {
      _handleError(null, customMessage: 'Request timed out. Please try again.');
      isLoading.value = false;
      return false;
    } on SocketException {
      _handleError(null,
          customMessage:
              'Network connection error. Please check your internet connection.');
      isLoading.value = false;
      return false;
    } catch (err) {
      _handleError(err);
      isLoading.value = false;
      return false;
    }
  }

  Future<bool> uploadProfile(File? profilePicture,
      {bool? isFromServiceScreen = false}) async {
    if (profilePicture == null) {
      errorToast(msg: 'No profile picture selected');
      return false;
    }

    try {
      isLoading.value = true;
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? apptoken = prefs.getString("token");
      int? tid = prefs.getInt("id");

      if (apptoken == null || tid == null) {
        errorToast(msg: 'Authentication error. Please login again.');
        isLoading.value = false;
        return false;
      }

      final userController = Get.put(CurrentUserController());
      final endpoint = '$baseUrl/api/allimg/upload/profile/$tid';

      log('Uploading profile: $endpoint');

      // Validate file size
      var profilelength = profilePicture.lengthSync();
      log('Profile file size: $profilelength bytes');

      if (profilelength > 5 * 1024 * 1024) {
        // 5MB limit
        _handleError(null,
            customMessage:
                'Image too large. Please select an image smaller than 5MB.');
        isLoading.value = false;
        return false;
      }

      var request = http.MultipartRequest('POST', Uri.parse(endpoint));

      // Adding header
      request.headers.addAll({
        'Content-Type': 'multipart/form-data',
        'Authorization': 'Bearer $apptoken'
      });

      // Adding profile picture with proper error handling
      try {
        request.files.add(http.MultipartFile.fromBytes(
            'profilePicture', profilePicture.readAsBytesSync(),
            filename: profilePicture.path.split("/").last));
      } catch (e) {
        _handleError(e, customMessage: 'Error processing image file');
        isLoading.value = false;
        return false;
      }

      // Send request with timeout
      final streamedResponse = await request.send().timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw TimeoutException('Request timed out');
        },
      );

      // Process response using the new helper method
      final responseData =
          await _handleStreamedResponse(streamedResponse, endpoint);

      if (responseData == null) {
        isLoading.value = false;
        return false;
      }

      log('Image uploaded successfully');
      userController.getCurrentUser();
      Get.offAll(() => const MainScreen());
      successToast(msg: 'Profile image uploaded successfully!');
      isLoading.value = false;
      return true;
    } on TimeoutException {
      _handleError(null,
          customMessage:
              'Upload timed out. Please try again with a smaller image.');
      isLoading.value = false;
      return false;
    } catch (e) {
      _handleError(e, customMessage: 'Profile upload failed');
      isLoading.value = false;
      return false;
    } finally {
      isLoading.value = false;
      Get.back();
    }
  }

  Future<bool> registerUser(String firstname, String lastname, String mobile,
      String email, String password, String latitude, String longitude) async {
    try {
      isLoading.value = true;
      final endpoint = '$baseUrl/api/v1/auth/register';

      // Validate input parameters
      if (firstname.isEmpty ||
          lastname.isEmpty ||
          mobile.isEmpty ||
          password.isEmpty) {
        _handleError(null, customMessage: 'Required fields cannot be empty');
        isLoading.value = false;
        return false;
      }

      // Prepare request data
      Map<String, dynamic> requestData = {
        "password": password,
        "mobileNumber": mobile,
        "fullname": "$firstname $lastname",
        "latitude": latitude,
        "longitude": longitude,
        "firstName": firstname,
        "lastName": lastname,
      };

      // Add email if provided
      if (email.isNotEmpty) {
        requestData["email"] = email;
      }

      final response = await http
          .post(
            Uri.parse(endpoint),
            headers: <String, String>{
              'Content-Type': 'application/json',
            },
            body: jsonEncode(requestData),
          )
          .timeout(const Duration(seconds: 20));

      consolelog("registerUser :: $endpoint");
      consolelog(response.statusCode);

      // Use the improved response handler
      final responseData = await _handleApiResponse(response, endpoint);

      if (responseData != null && response.statusCode == 201) {
        successToast(
            msg: responseData['message'] ?? "User registered successfully");
        Get.offAll(() => const LoginScreen());
        isLoading.value = false;
        return true;
      } else {
        // If _handleApiResponse already displayed an error toast, we don't need to do anything else
        isLoading.value = false;
        return false;
      }
    } on TimeoutException {
      _handleError(null,
          customMessage: 'Registration request timed out. Please try again.');
      isLoading.value = false;
      return false;
    } on SocketException {
      _handleError(null,
          customMessage:
              'Network connection error. Please check your internet connection.');
      isLoading.value = false;
      return false;
    } catch (err) {
      _handleError(err, customMessage: 'Registration failed');
      isLoading.value = false;
      return false;
    }
  }

  Future<bool> forgotPassword({
    String? newPassword,
    String? confirmPassword,
    String? mobileNumber,
  }) async {
    if (newPassword == null ||
        newPassword.isEmpty ||
        confirmPassword == null ||
        confirmPassword.isEmpty ||
        mobileNumber == null ||
        mobileNumber.isEmpty) {
      errorToast(msg: 'All fields are required');
      return false;
    }

    if (newPassword != confirmPassword) {
      errorToast(msg: 'Passwords do not match');
      return false;
    }

    try {
      isLoading.value = true;
      final endpoint =
          '$baseUrl/api/v1/auth/$mobileNumber/changePassword?newPassword=$newPassword&confirmPassword=$confirmPassword';

      final response = await http.post(
        Uri.parse(endpoint),
        headers: <String, String>{
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 20));

      log("forgotPassword :: $endpoint");
      log("Status code: ${response.statusCode}");

      // Use the improved response handler
      final responseData = await _handleApiResponse(response, endpoint);

      if (responseData != null && response.statusCode == 200) {
        String successMessage = "Password changed successfully";

        // Try to extract message from response model
        try {
          final result = commonResponseModelFromJson(response.body);
          if (result.message != null && result.message!.isNotEmpty) {
            successMessage = result.message!;
          }
        } catch (e) {
          // If parsing as CommonResponseModel fails, try to get message directly
          if (responseData.containsKey('message')) {
            successMessage = responseData['message'];
          }
        }

        successToast(msg: successMessage);
        Get.offAll(() => const LoginScreen());
        isLoading.value = false;
        return true;
      } else {
        // Error handling is already done in _handleApiResponse
        isLoading.value = false;
        return false;
      }
    } on SocketException catch (err) {
      _handleError(err, customMessage: 'Network connection error');
      isLoading.value = false;
      return false;
    } on http.ClientException catch (err) {
      _handleError(err, customMessage: 'Client error');
      isLoading.value = false;
      return false;
    } on TimeoutException {
      _handleError(null, customMessage: 'Request timed out');
      isLoading.value = false;
      return false;
    } catch (err) {
      _handleError(err, customMessage: 'Password change failed');
      isLoading.value = false;
      return false;
    }
  }

  Future<bool> checkNumberAndEmailRegister({
    String? email,
    String? mobileNumber,
  }) async {
    if ((email == null || email.isEmpty) &&
        (mobileNumber == null || mobileNumber.isEmpty)) {
      errorToast(msg: 'Email or mobile number is required');
      return false;
    }

    try {
      isLoading.value = true;
      final endpoint =
          '$baseUrl/api/users/emailAndMobile?email=$email&mobileNumber=$mobileNumber';

      final response = await http.get(
        Uri.parse(endpoint),
        headers: <String, String>{
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 15));

      log("checkNumberAndEmailRegister :: $endpoint");
      log("Status code: ${response.statusCode}");

      // Use the improved response handler
      final responseData = await _handleApiResponse(response, endpoint);

      if (responseData != null && response.statusCode == 200) {
        isLoading.value = false;
        return true;
      } else {
        // Error handling is already done in _handleApiResponse
        isLoading.value = false;
        return false;
      }
    } on TimeoutException {
      _handleError(null, customMessage: 'Request timed out');
      isLoading.value = false;
      return false;
    } on SocketException {
      _handleError(null, customMessage: 'Network connection error');
      isLoading.value = false;
      return false;
    } catch (err) {
      _handleError(err, customMessage: 'Validation check failed');
      isLoading.value = false;
      return false;
    }
  }

  Future<bool> sendOTP(
      {String? phoneNumber, int? randomNumber, String? message}) async {
    if (phoneNumber == null ||
        phoneNumber.isEmpty ||
        randomNumber == null ||
        message == null ||
        message.isEmpty) {
      errorToast(msg: 'Missing required parameters for OTP');
      return false;
    }

    if (apiKey.isEmpty) {
      errorToast(msg: 'SMS API key not configured');
      return false;
    }

    try {
      isLoading.value = true;
      final endpoint = "https://api.sparrowsms.com/v2/sms/";

      final response = await http.post(
        Uri.parse(endpoint),
        body: {
          'token': apiKey,
          'to': phoneNumber,
          'from': "TheAlert",
          'text': """
From Smart Sewa,
$message : $randomNumber
""",
        },
      ).timeout(const Duration(seconds: 20));

      logger("sendOTP :: $endpoint", loggerType: LoggerType.info);
      logger("Status code: ${response.statusCode}",
          loggerType: LoggerType.info);
      logger("Response: ${response.body}", loggerType: LoggerType.success);

      // Process response
      try {
        if (response.statusCode == 200) {
          final Map<String, dynamic> responseData = jsonDecode(response.body);
          final bool success = responseData['success'] ?? false;

          if (success) {
            successToast(msg: 'OTP sent successfully');
            isLoading.value = false;
            return true;
          } else {
            final String errorMessage =
                responseData['message'] ?? 'OTP sending failed';
            errorToast(msg: errorMessage);
            isLoading.value = false;
            return false;
          }
        } else {
          String errorMessage = 'Failed to send OTP';

          try {
            final responseData = jsonDecode(response.body);
            errorMessage = responseData['message'] ??
                'OTP sending failed with status: ${response.statusCode}';
          } catch (e) {
            // Use default message if parsing fails
            logger("Error parsing OTP response: $e",
                loggerType: LoggerType.error);
          }

          errorToast(msg: errorMessage);
          isLoading.value = false;
          return false;
        }
      } catch (e) {
        // Special case for SMS APIs where empty response with 200 might indicate success
        if (response.statusCode == 200) {
          successToast(msg: 'OTP sent successfully');
          isLoading.value = false;
          return true;
        } else {
          throw e; // Re-throw to be caught by the outer catch block
        }
      }
    } on TimeoutException {
      _handleError(null, customMessage: 'OTP request timed out');
      isLoading.value = false;
      return false;
    } on SocketException {
      _handleError(null, customMessage: 'Network connection error');
      isLoading.value = false;
      return false;
    } catch (err) {
      _handleError(err, customMessage: 'Failed to send OTP');
      isLoading.value = false;
      return false;
    }
  }
}
