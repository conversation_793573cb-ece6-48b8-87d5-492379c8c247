group 'com.f1soft.esewa.esewa_flutter_sdk'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.5.31'
    repositories {
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.0.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
        flatDir {
            dirs 'libs'
            dirs project(":esewa_flutter_sdk").files('libs')
        }
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
//apply plugin: 'kotlin-android-extensions'

android {
    namespace "com.esewa.sdk"
    compileSdkVersion 34

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    defaultConfig {
        minSdkVersion 16
    }

}

dependencies {
    implementation (name: 'esewasdk-release', ext: 'aar')
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.appcompat:appcompat:1.3.1'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'com.squareup.okhttp3:okhttp:4.9.1'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.9.1'
}
