import 'package:flutter/material.dart';
import 'package:flutter_offline/flutter_offline.dart';
class ConnectionWidge extends StatelessWidget {
  final Widget widget;
  void Function()? onPressed;
  ConnectionWidge({super.key, required this.widget, required this.onPressed});


  @override
  Widget build(BuildContext context) {
    return OfflineBuilder(
      connectivityBuilder:
          (BuildContext context, ConnectivityResult connection, Widget child) {
        final bool connected = connection != ConnectivityResult.none;
        // print(connected);
        return connected ? widget : Scaffold(
          appBar: AppBar(
            backgroundColor: Colors.red,
            title: const Text('Connection Lost'),
          ),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                const Text(
                  'Oops! It seems you lost your internet connection.',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.white),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: onPressed,
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        );
      },
      child: Container(),
    );
  }
}
