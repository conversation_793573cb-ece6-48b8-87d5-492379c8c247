import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:khalti_checkout_flutter/khalti_checkout_flutter.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/views/payment_screen.dart';
import 'package:smartsewa/views/utils.dart';
import 'package:smartsewa/views/widgets/buttons/app_buttons.dart';
import 'package:smartsewa/views/widgets/custom_toasts.dart';

class KhaltiSDKDemo extends StatefulWidget {
  String mobileNumber;
  String finalAmount;
  String paymentDuration;
  KhaltiSDKDemo(
      {super.key,
      required this.onPaymentInitiated,
      required this.mobileNumber,
      required this.finalAmount,
      required this.paymentDuration});

  final Function(Khalti) onPaymentInitiated;

  @override
  State<KhaltiSDKDemo> createState() => _KhaltiSDKDemoState();
}

class _KhaltiSDKDemoState extends State<KhaltiSDKDemo> {
  late Future<Khalti?> khalti;
  bool isLoading = true;
  bool paymentProcessed =
      false; // Add this flag to track if payment is processed
  String? pidx;
  PaymentResult? paymentResult;

  @override
  void initState() {
    super.initState();
    khalti = _fetchPidx();
  }

  Future<Khalti?> _fetchPidx() async {
    try {
      String result = await initializePayment();
      await Future.delayed(const Duration(seconds: 1));
      consolelog(result);
      setState(() {
        pidx = result;
        isLoading = false;
        consolelog(pidx);
        consolelog(result);
        if (result.contains("Error")) {
          errorToast(msg: result);
        }
      });
      return _initializeKhaltiConfig(pidx.toString());
    } catch (e) {
      errorToast(msg: "Error");
      print('Error: $e');
      return null;
    }
  }

  Future<bool> verifyKhaltiPayment() async {
    var headers = {'Content-Type': 'application/json'};
    var request = http.Request(
        'POST',
        Uri.parse(
            '$baseUrl/api/v1/user/payment/khalti/verify/${widget.mobileNumber}/$pidx'));
    request.body = '''''';
    request.headers.addAll(headers);

    try {
      http.StreamedResponse response = await request.send();
      if (response.statusCode == 200) {
        String responseBody = await response.stream.bytesToString();
        print(responseBody);

        // Parse response to check actual payment status
        Map<String, dynamic> jsonResponse = json.decode(responseBody);

        if (jsonResponse['status'] == 'success' ||
            jsonResponse['verified'] == true) {
          successToast(msg: "Payment Successful");
          return true;
        } else {
          errorToast(msg: "Payment verification failed");
          return false;
        }
      } else {
        errorToast(
            msg: "Payment Verification Failed\n Please Contact Smartsewa");
        print('Error: ${response.statusCode} - ${response.reasonPhrase}');
        return false;
      }
    } catch (e) {
      errorToast(msg: "Payment Verification Failed\n Please Contact Smartsewa");
      print('Unexpected error: $e');
      return false;
    }
  }

  Future<Khalti> _initializeKhaltiConfig(String pidx) {
    final payConfig = KhaltiPayConfig(
      publicKey: '071b5343ce57455d9a407aa945e0cd8f',
      pidx: pidx,
      environment: Environment.prod,
    );

    return Khalti.init(
        enableDebugging: true,
        payConfig: payConfig,
        onPaymentResult: (paymentResult, khalti) async {
          log(paymentResult.toString());
          setState(() {
            this.paymentResult = paymentResult;
            paymentProcessed = true;
          });

          khalti.close(context);

          // Check if payment was successful
          if (paymentResult.payload != null) {
            // Payment successful - verify and navigate
            bool verified = await verifyKhaltiPayment();

            if (verified && mounted) {
              // Navigate to PaymentScreen only on success
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => PaymentScreen()),
              );
            } else {
              // Verification failed, go back
              Navigator.of(context).pop();
            }
          } else {
            // Payment failed
            errorToast(msg: "Payment failed. Please try again.");
            if (mounted) {
              Navigator.of(context).pop(); // Go back to previous screen
            }
          }
        },
        onMessage: (khalti,
            {description, statusCode, event, needsPaymentConfirmation}) async {
          log('Description: $description, Status Code: $statusCode, Event: $event, NeedsPaymentConfirmation: $needsPaymentConfirmation');

          // Handle different events
          if (event == 'PAYMENT_CANCELLED') {
            setState(() {
              paymentProcessed = true;
            });
            khalti.close(context);
            errorToast(msg: "Payment cancelled by user");
            if (mounted) {
              Navigator.of(context).pop(); // Go back to previous screen
            }
          } else if (event == 'PAYMENT_FAILED') {
            setState(() {
              paymentProcessed = true;
            });
            khalti.close(context);

            errorToast(msg: "Payment failed. Please try again.");

            if (mounted) {
              Navigator.of(context).pop();
              // Go back to previous screen
            }
          }
        },
        onReturn: () {
          // This is called when user returns from payment interface
          log('User returned from payment interface');

          // Add a small delay to check if other callbacks were triggered
          Future.delayed(const Duration(milliseconds: 1000), () {
            if (mounted && !paymentProcessed) {
              setState(() {
                paymentProcessed = true;
              });

              // Close the current dialog or screen
              Navigator.of(context).pop();

              // Delay to let pop complete before navigating
              Future.delayed(const Duration(milliseconds: 200), () {
                if (mounted) {
                  // Show toast
                  errorToast(msg: "Payment was cancelled or interrupted");
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                  // Navigate to PaymentScreen
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(builder: (context) => PaymentScreen()),
                  );
                }
              });
            }
          });
        });
  }

  Future<String> initializePayment() async {
    var headers = {'Content-Type': 'application/json'};

    var request = http.Request(
      'POST',
      Uri.parse(
          '$baseUrl/api/v1/user/payment/khalti/initalize/${widget.mobileNumber}/${widget.paymentDuration}'),
    );

    request.body = json.encode({
      "amount": widget.finalAmount,
      "purchase_order_id": "PayKhalti",
      "purchase_order_name": "ServiceProvider",
    });
    consolelog(widget.finalAmount);
    consolelog({
      Uri.parse(
          '$baseUrl/api/v1/user/payment/khalti/initalize/${widget.mobileNumber}/${widget.paymentDuration}')
    });
    consolelog(request.body);
    request.headers.addAll(headers);

    var connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      return "No internet connection. Please check your network settings.";
    }

    try {
      http.StreamedResponse response = await request.send();
      consolelog("Response Code:${response.statusCode}");
      if (response.statusCode == 200) {
        String responseString = await response.stream.bytesToString();
        Map<String, dynamic> jsonResponse = json.decode(responseString);
        return jsonResponse['pidx'];
      } else {
        return "${response.reasonPhrase}";
      }
    } catch (e) {
      return "An Error occurred";
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Processing Payment'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Center(
        child: FutureBuilder<Khalti?>(
          future: khalti,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator.adaptive(),
                  SizedBox(height: 16),
                  Text('Initializing payment...'),
                ],
              );
            }

            if (snapshot.hasError) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('Error: ${snapshot.error}'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Go Back'),
                  ),
                ],
              );
            }

            if (!snapshot.hasData || snapshot.data == null) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.payment, size: 64, color: Colors.orange),
                  const SizedBox(height: 16),
                  const Text('Failed to initialize payment'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Go Back'),
                  ),
                ],
              );
            }

            final khaltiSnapshot = snapshot.data!;

            // Call the payment function when Khalti is ready
            WidgetsBinding.instance.addPostFrameCallback((_) {
              widget.onPaymentInitiated(khaltiSnapshot);
            });

            // Show processing screen instead of empty container
            return const Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator.adaptive(),
                SizedBox(height: 16),
                Text('Opening Khalti Payment...'),
                SizedBox(height: 8),
                Text('Please complete your payment'),
              ],
            );
          },
        ),
      ),
    );
  }
}
