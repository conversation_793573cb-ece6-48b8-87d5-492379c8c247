Future<void> deleteUserAccount(
    String username, String password, BuildContext context) async {
  // Store the context for later use
  final scaffoldContext = context;

  try {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Deleting account...'),
            ],
          ),
        );
      },
    );

    // Retrieve token from SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('token') ?? '';

    // Set headers
    var headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token'
    };

    // Create and send request
    var request = http.Request(
        'DELETE',
        Uri.parse(
            '$baseUrl/api/users/delete?username=$username&password=$password'));

    request.headers.addAll(headers);

    http.StreamedResponse response = await request.send();
    String responseBody = await response.stream.bytesToString();

    // Print for debugging
    print("Response status: ${response.statusCode}");
    print("Response body: $responseBody");

    // Dismiss loading dialog
    Navigator.of(context, rootNavigator: true).pop();

    try {
      var jsonResponse = json.decode(responseBody);
      
      switch (response.statusCode) {
        case 200:
          // Success case
          showSuccessDialog(scaffoldContext, jsonResponse['message']);
          break;
        case 401:
          // Invalid credentials, show attempts remaining
          showErrorDialog(scaffoldContext, jsonResponse['message']);
          break;
        case 403:
          // Maximum attempts reached
          showErrorDialog(scaffoldContext, jsonResponse['message']);
          break;
        case 404:
          // User not found
          showErrorDialog(scaffoldContext, jsonResponse['message']);
          break;
        case 500:
        default:
          // Internal server error or other unexpected errors
          showErrorDialog(scaffoldContext, 
              jsonResponse['message'] ?? 'An unexpected error occurred');
          break;
      }
    } catch (parseError) {
      print("JSON parsing error: $parseError");
      showErrorDialog(scaffoldContext, 'Error processing server response');
    }
  } catch (e) {
    print("Exception occurred: $e");
    // Make sure to dismiss loading dialog if still showing
    Navigator.of(context, rootNavigator: true).pop();
    showErrorDialog(
        scaffoldContext, 'Connection error. Please try again later.');
  }
}

// Helper function to show success dialog with proper navigation
void showSuccessDialog(BuildContext context, String message) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext dialogContext) {
      return AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        title: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: MediaQuery.of(context).size.width * 0.06,
            ),
            SizedBox(width: MediaQuery.of(context).size.width * 0.02),
            Text(
              'Success',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: MediaQuery.of(context).size.width * 0.045,
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: TextStyle(
            fontSize: MediaQuery.of(context).size.width * 0.04 > 16
                ? 16
                : MediaQuery.of(context).size.width * 0.04,
          ),
          textAlign: TextAlign.center,
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                ),
                onPressed: () {
                  // First close the dialog
                  Navigator.pop(dialogContext);
                  
                  // Then navigate to login page and clear all previous routes
                  Navigator.pushNamedAndRemoveUntil(
                    context, '/login', (route) => false);
                },
                child: Text('OK'),
              ),
            ),
          ),
        ],
      );
    },
  );
}L