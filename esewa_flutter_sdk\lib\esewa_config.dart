final String clientId = "JB0BBQ4aD0UqIThFJwAKBgAXEUkEGQUBBAwdOgABHD4DChwUAB0R";
final String secretId = "BhwIWQQADhIYSxILExMcAgFXFhcOBwAKBgAXEQ== ";

class EsewaConfig {
  final String clientId;
  final String secretId;
  final Environment environment;

  EsewaConfig({
    required this.clientId,
    required this.secretId,
    required this.environment,
  });
}

extension ConfigExt on EsewaConfig {
  Map<String, dynamic> toMap() => {
        "client_id": this.clientId,
        "client_secret": this.secretId,
        "environment": this.environment.parse(),
      };
}

enum Environment { test, live, prod }

extension EnvExt on Environment {
  String parse() => this == Environment.live ? 'live' : 'test';
}
