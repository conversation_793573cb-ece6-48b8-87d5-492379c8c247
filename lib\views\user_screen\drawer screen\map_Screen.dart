// import 'dart:async';
// import 'dart:convert';
// import 'dart:developer';
// import 'package:geolocator/geolocator.dart' as geo;

// import 'package:custom_info_window/custom_info_window.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart'; // Import for clipboard functionality
// import 'package:flutter_rating_bar/flutter_rating_bar.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:get/get.dart';
// import 'package:http/http.dart' as http;
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:intl/intl.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smartsewa/core/development/console.dart';
// import 'package:smartsewa/network/models/service_map_model.dart';
// import 'package:smartsewa/network/services/authServices/auth_controller.dart';
// import 'package:smartsewa/network/services/categories&services/service_map_controller.dart';
// import 'package:smartsewa/network/services/orderService/filter_controller.dart';
// import 'package:smartsewa/views/auth/registration/user_registration.dart';
// import 'package:smartsewa/views/user_screen/approval/open_map_screen.dart';
// import 'package:smartsewa/views/utils.dart';
// import 'package:smartsewa/views/widgets/custom_toasts.dart';
// import 'package:url_launcher/url_launcher.dart';
// import '../../../network/base_client.dart';
// import '../../../network/services/orderService/request_service.dart';
// import '../../widgets/my_appbar.dart';

// class MapScreen extends StatefulWidget {
//   final String work;
//   final String name;

//   MapScreen({required this.work, required this.name});

//   @override
//   State<MapScreen> createState() => _MapScreenState();
// }

// class _MapScreenState extends State<MapScreen> {
//   final CustomInfoWindowController _customInfoWindowController =
//       CustomInfoWindowController();
//   String baseUrl = BaseClient().baseUrl;
//   var isLoading = false.obs;
//   final myMapController = Get.put(MyMapController());
//   final controller = Get.put(OrderController());
//   final authController = Get.put(AuthController());
//   final filterController = Get.put(FilterController());
//   double averageRating = 0.0;
//   TextEditingController reasonController = TextEditingController();
//   String? token;
//   List<Comment> comments = [];
//   bool loadingMore = false;
//   int currentPage = 0;
//   int totalCommentsCount = 0;
//   late Future<Widget> reviewListFuture;
//   bool _showServiceProvidersList = false;
//   List<ServiceMapModel> _sortedServiceProviders = [];

//   bool isSearching = false;
//   String searchQuery = '';
//   TextEditingController searchController = TextEditingController();
//   FocusNode searchFocusNode = FocusNode();

//   final Completer<GoogleMapController> _controller = Completer();
//   int? tid;
//   final List<Marker> _marker = <Marker>[];
//   final Set<Circle> _circles = {};

//   LatLng currentPosition = const LatLng(27.709290, 85.348101);

//   final CameraPosition _kGoogle = const CameraPosition(
//     target: LatLng(27.707795, 85.343362),
//     zoom: 14.4746,
//   );

//   @override
//   void initState() {
//     super.initState();
//     getToken();
//     _getUserLocation();
//     Get.delete<MyMapController>();

//     super.initState();
//   }

//   @override
//   void dispose() {
//     searchController.dispose();
//     searchFocusNode.dispose();
//     super.dispose();
//   }

//   void _sortProvidersByDistance() {
//     final userLocation = currentPosition;

//     _sortedServiceProviders = getWorker().where((provider) {
//       // Filter out providers with invalid coordinates
//       if (provider.latitude.isEmpty || provider.longitude.isEmpty) return false;

//       final lat = double.tryParse(provider.latitude);
//       final lng = double.tryParse(provider.longitude);
//       return lat != null && lng != null;
//     }).map((provider) {
//       final providerLocation = LatLng(
//         double.parse(provider.latitude),
//         double.parse(provider.longitude),
//       );
//       // Calculate distance in meters
//       provider.distance = Geolocator.distanceBetween(
//         userLocation.latitude,
//         userLocation.longitude,
//         providerLocation.latitude,
//         providerLocation.longitude,
//       );
//       return provider;
//     }).toList()
//       ..sort((a, b) => a.distance!.compareTo(b.distance!));
//   }

//   Future<Position> getUserCurrentLocation() async {
//     await Geolocator.requestPermission()
//         .then((value) {})
//         .onError((error, stackTrace) async {
//       await Geolocator.requestPermission();
//       print("ERROR$error");
//     });

//     return await Geolocator.getCurrentPosition();
//   }

//   // List<ServiceMapModel> getWorker() {
//   //   // Debug logs to understand the data
//   //   consolelog("Available services:");
//   //   for (var user in myMapController.users) {
//   //     consolelog("User ${user.fullName}: Service = ${user.serviceProvided}");
//   //   }
//   //   consolelog("Selected work: ${widget.work}");

//   //   var filteredUsers = myMapController.users.where((user) {
//   //     if (user.serviceProvided == null) {
//   //       return false;
//   //     }

//   //     // Clean up strings for comparison
//   //     String cleanUserService = _cleanServiceString(user.serviceProvided!);
//   //     String cleanSelectedWork = _cleanServiceString(widget.work);

//   //     consolelog("Comparing: '$cleanUserService' with '$cleanSelectedWork'");

//   //     // Check if the service contains the selected work or vice versa
//   //     return cleanUserService.contains(cleanSelectedWork) ||
//   //         cleanSelectedWork.contains(cleanUserService) ||
//   //         cleanUserService == "all"; // Include users who provide all services
//   //   }).toList();

//   //   consolelog("Number of filtered users: ${filteredUsers.length}");

//   //   if (searchQuery.isNotEmpty) {
//   //     filteredUsers = filteredUsers
//   //         .where((user) =>
//   //             user.fullName.toLowerCase().contains(searchQuery.toLowerCase()))
//   //         .toList();
//   //     consolelog(
//   //         "Number of users after search filter: ${filteredUsers.length}");
//   //   }

//   //   return filteredUsers;
//   // }

//   List<ServiceMapModel> getWorker() {
//     // Debug logs to understand the data
//     consolelog("Available services:");
//     for (var user in myMapController.users) {
//       consolelog("User ${user.fullName}: Service = ${user.serviceProvided}");
//     }
//     consolelog("Selected work: ${widget.work}");

//     var filteredUsers = myMapController.users.where((user) {
//       if (user.serviceProvided == null) {
//         return false;
//       }

//       // Clean up strings for comparison
//       String cleanUserService = _cleanServiceString(user.serviceProvided!);
//       String cleanSelectedWork = _cleanServiceString(widget.work);

//       consolelog("Comparing: '$cleanUserService' with '$cleanSelectedWork'");

//       // Check if the service contains the selected work or vice versa
//       return cleanUserService.contains(cleanSelectedWork) ||
//           cleanSelectedWork.contains(cleanUserService) ||
//           cleanUserService == "all"; // Include users who provide all services
//     }).toList();

//     consolelog("Number of filtered users: ${filteredUsers.length}");

//     if (searchQuery.isNotEmpty) {
//       filteredUsers = filteredUsers
//           .where((user) =>
//               user.fullName.toLowerCase().contains(searchQuery.toLowerCase()))
//           .toList();
//       consolelog(
//           "Number of users after search filter: ${filteredUsers.length}");
//     }

//     return filteredUsers;
//   }

//   String _cleanServiceString(String input) {
//     // Convert to lowercase and trim whitespace
//     String cleaned = input.trim().toLowerCase();

//     // List of common words to remove
//     final List<String> commonWords = ['service', 'services', 'and', '&', '-'];

//     // Remove common words
//     for (String word in commonWords) {
//       cleaned = cleaned.replaceAll(word, '').trim();
//     }

//     return cleaned;
//   }

//   void _getUserLocation() async {
//     var position = await GeolocatorPlatform.instance.getCurrentPosition();
//     setState(() {
//       currentPosition = LatLng(position.latitude, position.longitude);

//       consolelog(currentPosition);
//     });
//   }

//   Future<void> getUserId(int id) async {
//     setState(() {});
//   }

//   // void getToken() async {
//   //   SharedPreferences prefs = await SharedPreferences.getInstance();
//   //   String? apptoken = prefs.getString("token");
//   //   int? id = prefs.getInt("id");
//   //   setState(() {
//   //     token = apptoken;
//   //     tid = id;
//   //   });
//   // }

//   void getToken() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     String? apptoken = prefs.getString("token");
//     int? id = prefs.getInt("id");
//     setState(() {
//       token = apptoken;
//       tid = id;
//     });

//     // Update AuthController's token
//     authController.token = apptoken;
//     authController.update();
//   }

//   void _showRatingDialog(ServiceMapModel user) {
//     double userRating = 0;
//     String userComment = '';

//     showDialog(
//       context: context,
//       barrierDismissible: true, // Allows dismissing by tapping outside
//       builder: (BuildContext context) {
//         return LayoutBuilder(
//           builder: (context, constraints) {
//             // Responsive design based on screen width
//             bool isLargeScreen = constraints.maxWidth > 600;

//             return Dialog(
//               shape: RoundedRectangleBorder(
//                 borderRadius: BorderRadius.circular(25),
//               ),
//               insetPadding: EdgeInsets.symmetric(
//                   horizontal: isLargeScreen ? 40 : 20,
//                   vertical: isLargeScreen ? 60 : 40),
//               child: SingleChildScrollView(
//                 // Make the dialog content scrollable
//                 child: Container(
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.circular(25),
//                     color: Colors.white,
//                   ),
//                   child: Column(
//                     mainAxisSize: MainAxisSize.min,
//                     children: [
//                       // Title section (keep original color)
//                       Container(
//                         padding: EdgeInsets.all(isLargeScreen ? 30 : 20),
//                         decoration: const BoxDecoration(
//                           color: Color.fromARGB(
//                               240, 0, 131, 143), // Original teal color
//                           borderRadius: BorderRadius.only(
//                             topLeft: Radius.circular(25),
//                             topRight: Radius.circular(25),
//                           ),
//                         ),
//                         child: Center(
//                           child: Text(
//                             'Rate ${user.fullName}',
//                             style: TextStyle(
//                               fontSize: isLargeScreen ? 24 : 20,
//                               fontWeight: FontWeight.bold,
//                               color: Colors.white,
//                             ),
//                           ),
//                         ),
//                       ),

//                       // Content Section
//                       Padding(
//                         padding: EdgeInsets.all(isLargeScreen ? 20 : 9),
//                         child: Column(
//                           mainAxisSize: MainAxisSize.min,
//                           children: [
//                             // Question Text
//                             const Text(
//                               'How was your experience?',
//                               style: TextStyle(
//                                 fontSize: 16,
//                                 fontWeight: FontWeight.w500,
//                                 color: Colors.black87,
//                               ),
//                             ),
//                             const SizedBox(height: 10),

//                             // Star Rating Bar
//                             RatingBar.builder(
//                               initialRating: 0,
//                               minRating: 1,
//                               direction: Axis.horizontal,
//                               allowHalfRating: true,
//                               itemCount: 5,
//                               itemSize:
//                                   isLargeScreen ? 50 : 40, // Responsive size
//                               unratedColor: Colors.grey[300],
//                               glowColor: Colors.amber,
//                               itemBuilder: (context, _) => const Icon(
//                                 Icons.star_rounded,
//                                 color: Colors.amber,
//                               ),
//                               onRatingUpdate: (rating) {
//                                 userRating = rating;
//                               },
//                             ),
//                             const SizedBox(height: 10),
//                             // Comment Input
//                             TextField(
//                               maxLines: 2,
//                               decoration: InputDecoration(
//                                 contentPadding:
//                                     EdgeInsets.all(isLargeScreen ? 20 : 15),
//                                 border: OutlineInputBorder(
//                                   borderRadius: BorderRadius.circular(15),
//                                   borderSide: BorderSide.none,
//                                 ),
//                                 hintText: 'Share your feedback...',
//                                 filled: true,
//                                 fillColor: Colors.grey[100],
//                                 hintStyle: const TextStyle(color: Colors.grey),
//                               ),
//                               onChanged: (value) {
//                                 userComment = value;
//                               },
//                             ),
//                           ],
//                         ),
//                       ),

//                       // Action Buttons
//                       Padding(
//                         padding: EdgeInsets.symmetric(
//                             horizontal: isLargeScreen ? 30 : 20,
//                             vertical: isLargeScreen ? 20 : 15),
//                         child: Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             // Cancel Button (keep original color)
//                             Expanded(
//                               child: ElevatedButton.icon(
//                                 onPressed: () {
//                                   Navigator.of(context).pop();
//                                 },
//                                 label: const Text('Cancel'),
//                                 style: ElevatedButton.styleFrom(
//                                   backgroundColor: Colors
//                                       .redAccent, // Original color for cancel
//                                   shape: RoundedRectangleBorder(
//                                     borderRadius: BorderRadius.circular(10),
//                                   ),
//                                   padding: EdgeInsets.symmetric(
//                                       vertical: isLargeScreen ? 20 : 15),
//                                   textStyle: TextStyle(
//                                     fontSize: isLargeScreen ? 18 : 16,
//                                     fontWeight: FontWeight.w500,
//                                   ),
//                                 ),
//                               ),
//                             ),
//                             const SizedBox(width: 15),

//                             // Submit Button (keep original color)
//                             Expanded(
//                               child: ElevatedButton.icon(
//                                 onPressed: () {
//                                   consolelog("Rated User: ${user.id}");
//                                   if (userRating == 0 || userComment == "") {
//                                     errorToast(
//                                         msg: "Empty Rating or Comment Field");
//                                   } else {
//                                     submitRating(
//                                         tid!, user.id, userRating, userComment);
//                                     Navigator.of(context).pop();
//                                   }
//                                 },
//                                 label: const Text('Submit'),
//                                 style: ElevatedButton.styleFrom(
//                                   backgroundColor: const Color.fromARGB(
//                                       240, 0, 131, 143), // Original teal
//                                   shape: RoundedRectangleBorder(
//                                     borderRadius: BorderRadius.circular(10),
//                                   ),
//                                   padding: EdgeInsets.symmetric(
//                                       vertical: isLargeScreen ? 20 : 15),
//                                   textStyle: TextStyle(
//                                     fontSize: isLargeScreen ? 18 : 16,
//                                     fontWeight: FontWeight.w500,
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             );
//           },
//         );
//       },
//     );
//   }

//   Future<void> submitRating(
//       int ratingUserId, int userId, double score, String comment) async {
//     // Replace with actual service ID as needed

//     try {
//       final response = await http.post(
//         Uri.parse('$baseUrl/api/v1/rating/serviceID'), // Use the correct IP
//         headers: {
//           'Content-Type': 'application/json',
//           'Authorization': 'Bearer $token', // Add your authorization token
//         },
//         body: jsonEncode({
//           'ratingUserid': ratingUserId,
//           'userId': userId,
//           'score': score,
//           'comment': comment,
//         }),
//       );

//       // Check for errors in the response
//       if (response.statusCode == 200) {
//         successToast(msg: "User Rated Successfully");
//         // Handle successful response
//         print('Rating submitted successfully');
//       } else {
//         errorToast(msg: response.body);
//         // Handle non-200 responses
//         print('Failed to submit rating: ${response.statusCode}');
//         print('Response body: ${response.body}');
//       }
//     } catch (e) {
//       errorToast(msg: "Internal Server Error");
//       // Handle network or other errors
//       print('Error occurred while submitting rating: $e');
//     }
//   }

//   Future<void> reportServiceProvider(
//       int reportingUserId, int reportedUserId, String reportText) async {
//     var headers = {
//       'Content-Type': 'application/json',
//       'Authorization': 'Bearer $token',
//     };

//     var body = json.encode({
//       "report": reportText,
//     });

//     var url =
//         Uri.parse('$baseUrl/api/v1/reports/$reportingUserId/$reportedUserId');

//     try {
//       var response = await http.post(url, headers: headers, body: body);

//       if (response.statusCode == 200) {
//         print('Report successfully Submitted: ${response.body}');
//         successToast(msg: "Report Successfully Submitted");
//       } else {
//         print('Failed to Submit Report: ${response.reasonPhrase}');
//         errorToast(msg: "Failed to Submit Report");
//       }
//     } catch (e) {
//       print('Error occurred: $e');
//       errorToast(msg: "An Error Occurred \nFailed to Submit Report");
//     }
//   }

//   PreferredSizeWidget _buildAppBar() {
//     final Color appBarColor =
//         const Color.fromARGB(240, 0, 131, 143); // App bar color
//     final Color iconColor = Colors.white; // Icon color

//     if (isSearching) {
//       return AppBar(
//         systemOverlayStyle: const SystemUiOverlayStyle(
//             statusBarColor: Color.fromARGB(240, 0, 96, 100)),
//         backgroundColor: appBarColor,
//         iconTheme: IconThemeData(color: iconColor),
//         leading: IconButton(
//           icon: Icon(Icons.arrow_back, color: iconColor), // White back arrow
//           onPressed: () {
//             setState(() {
//               isSearching = false;
//               searchQuery = '';
//               searchController.clear();
//             });
//           },
//         ),
//         title: Container(
//           decoration: BoxDecoration(
//             color: Colors.white,
//             borderRadius: BorderRadius.circular(25),
//           ),
//           child: TextField(
//             focusNode: searchFocusNode,
//             controller: searchController,
//             decoration: InputDecoration(
//               hintText: 'Search service provider...',
//               hintStyle: TextStyle(color: Colors.grey[500], fontSize: 16),
//               prefixIcon: const Icon(
//                 Icons.search,
//                 color: Color.fromARGB(240, 0, 131, 143),
//               ),
//               border: InputBorder.none,
//               contentPadding:
//                   const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
//             ),
//             style: const TextStyle(color: Colors.black87, fontSize: 16),
//             onChanged: (value) {
//               setState(() {
//                 searchQuery = value;
//               });
//             },
//           ),
//         ),
//         actions: [
//           if (searchQuery.isNotEmpty)
//             IconButton(
//               icon: Icon(Icons.clear, color: iconColor), // White clear icon
//               onPressed: () {
//                 searchController.clear();
//                 setState(() {
//                   searchQuery = '';
//                 });
//               },
//             ),
//         ],
//         elevation: 4,
//         shadowColor: Colors.black.withOpacity(0.2),
//       );
//     } else {
//       return AppBar(
//         systemOverlayStyle: const SystemUiOverlayStyle(
//             statusBarColor: Color.fromARGB(240, 0, 96, 100)),
//         backgroundColor: appBarColor,
//         iconTheme: IconThemeData(color: iconColor),
//         automaticallyImplyLeading: true,
//         leading: IconButton(
//           icon: Icon(Icons.arrow_back, color: iconColor),
//           onPressed: () {
//             Navigator.pop(context);
//           },
//         ),
//         centerTitle: true,
//         title: Text(
//           widget.name,
//           style: const TextStyle(
//             fontSize: 20,
//             fontWeight: FontWeight.w500,
//             color: Colors.white,
//           ),
//         ),
//         actions: [
//           IconButton(
//             icon: Icon(
//               Icons.search,
//               color: iconColor,
//               size: 28, // Larger for visibility
//             ),
//             tooltip: 'Search',
//             onPressed: () {
//               setState(() {
//                 isSearching = true;
//                 // Request focus after a short delay
//                 Future.delayed(const Duration(milliseconds: 100), () {
//                   searchFocusNode.requestFocus();
//                 });
//               });
//             },
//             splashRadius: 24,
//           ),
//         ],
//         elevation: 4,
//         shadowColor: Colors.black.withOpacity(0.2),
//       );
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     Size size = MediaQuery.of(context).size;

//     consolelog("Name and Work:");
//     consolelog(widget.name);
//     consolelog(widget.work);
//     return Scaffold(
//       appBar: _buildAppBar(),
//       body: Stack(
//         children: [
//           Obx(() {
//             final markers = getWorker().map((user) {
//               bool isConditionTrue = false;
//               if (tid == user.id) {
//                 isConditionTrue = true;
//               } else {
//                 isConditionTrue = false;
//               }

//               return Marker(
//                   markerId: MarkerId(user.id.toString()),
//                   position: LatLng(
//                     double.parse(user.latitude),
//                     double.parse(user.longitude),
//                   ),
//                   icon: isConditionTrue
//                       ? BitmapDescriptor.defaultMarkerWithHue(
//                           BitmapDescriptor.hueBlue)
//                       : BitmapDescriptor.defaultMarkerWithHue(
//                           BitmapDescriptor.hueRed),

//                   //icon: BitmapDescriptor.defaultMarkerWithHue(
//                   //  BitmapDescriptor.hueBlue),
//                   onTap: () async {
//                     if (authController.isUserGuest()) {
//                       showDialog(
//                         context: context,
//                         builder: (BuildContext context) {
//                           final screenWidth = MediaQuery.of(context).size.width;
//                           final isSmallScreen = screenWidth < 360;

//                           return Dialog(
//                             backgroundColor: Colors.transparent,
//                             insetPadding: EdgeInsets.all(screenWidth * 0.05),
//                             child: Container(
//                               decoration: BoxDecoration(
//                                 color: const Color.fromARGB(240, 0, 131, 143),
//                                 borderRadius: BorderRadius.circular(25),
//                                 boxShadow: [
//                                   BoxShadow(
//                                     color: Colors.black.withOpacity(0.3),
//                                     blurRadius: 15,
//                                     spreadRadius: 2,
//                                   )
//                                 ],
//                               ),
//                               child: Column(
//                                 mainAxisSize: MainAxisSize.min,
//                                 children: [
//                                   // Header Section
//                                   Container(
//                                     padding: EdgeInsets.all(screenWidth * 0.04),
//                                     decoration: BoxDecoration(
//                                       color: Colors.white,
//                                       borderRadius: const BorderRadius.only(
//                                         topLeft: Radius.circular(25),
//                                         topRight: Radius.circular(25),
//                                       ),
//                                       boxShadow: [
//                                         BoxShadow(
//                                           color: Colors.black.withOpacity(0.1),
//                                           blurRadius: 8,
//                                         )
//                                       ],
//                                     ),
//                                     child: Row(
//                                       mainAxisAlignment:
//                                           MainAxisAlignment.center,
//                                       children: [
//                                         Icon(
//                                           Icons.verified_user_rounded,
//                                           color: const Color.fromARGB(
//                                               240, 0, 131, 143),
//                                           size: screenWidth * 0.08,
//                                         ),
//                                         SizedBox(width: screenWidth * 0.03),
//                                         Flexible(
//                                           child: Text(
//                                             'Access Required',
//                                             style: TextStyle(
//                                               color: const Color.fromARGB(
//                                                   240, 0, 131, 143),
//                                               fontSize: isSmallScreen ? 18 : 22,
//                                               fontWeight: FontWeight.w700,
//                                               letterSpacing: 0.8,
//                                             ),
//                                           ),
//                                         ),
//                                       ],
//                                     ),
//                                   ),

//                                   // Content Section
//                                   Padding(
//                                     padding: EdgeInsets.all(screenWidth * 0.05),
//                                     child: Column(
//                                       children: [
//                                         SizedBox(height: screenWidth * 0.03),
//                                         Text(
//                                           'Please   SignUp to get the More Service ',
//                                           textAlign: TextAlign.center,
//                                           style: TextStyle(
//                                             color: Colors.white,
//                                             fontSize: isSmallScreen ? 14 : 16,
//                                             height: 1.4,
//                                             fontWeight: FontWeight.w500,
//                                           ),
//                                         ),
//                                         SizedBox(height: screenWidth * 0.06),
//                                         // Buttons Row
//                                         LayoutBuilder(
//                                           builder: (context, constraints) {
//                                             return Row(
//                                               children: [
//                                                 Expanded(
//                                                   child: ElevatedButton(
//                                                     onPressed: () =>
//                                                         Navigator.pop(context),
//                                                     style: ElevatedButton
//                                                         .styleFrom(
//                                                       backgroundColor:
//                                                           Colors.transparent,
//                                                       elevation: 0,
//                                                       shape:
//                                                           RoundedRectangleBorder(
//                                                         borderRadius:
//                                                             BorderRadius
//                                                                 .circular(15),
//                                                         side: const BorderSide(
//                                                           color: Colors.white,
//                                                           width: 1.5,
//                                                         ),
//                                                       ),
//                                                       padding:
//                                                           EdgeInsets.symmetric(
//                                                         vertical:
//                                                             screenWidth * 0.03,
//                                                       ),
//                                                     ),
//                                                     child: Text(
//                                                       'Later',
//                                                       style: TextStyle(
//                                                         color: Colors.white,
//                                                         fontSize: isSmallScreen
//                                                             ? 14
//                                                             : 16,
//                                                         fontWeight:
//                                                             FontWeight.w600,
//                                                       ),
//                                                     ),
//                                                   ),
//                                                 ),
//                                                 SizedBox(
//                                                     width: screenWidth * 0.04),
//                                                 Expanded(
//                                                   child: ElevatedButton(
//                                                     onPressed: () {
//                                                       Navigator.pop(context);
//                                                       Get.to(() =>
//                                                           UserRegistration());
//                                                     },
//                                                     style: ElevatedButton
//                                                         .styleFrom(
//                                                       backgroundColor:
//                                                           Colors.white,
//                                                       elevation: 4,
//                                                       shape:
//                                                           RoundedRectangleBorder(
//                                                         borderRadius:
//                                                             BorderRadius
//                                                                 .circular(15),
//                                                       ),
//                                                       padding:
//                                                           EdgeInsets.symmetric(
//                                                         vertical:
//                                                             screenWidth * 0.03,
//                                                       ),
//                                                     ),
//                                                     child: Text(
//                                                       'Sign Up Now',
//                                                       style: TextStyle(
//                                                         color: const Color
//                                                             .fromARGB(
//                                                             240, 0, 131, 143),
//                                                         fontSize: isSmallScreen
//                                                             ? 14
//                                                             : 16,
//                                                         fontWeight:
//                                                             FontWeight.w700,
//                                                       ),
//                                                     ),
//                                                   ),
//                                                 ),
//                                               ],
//                                             );
//                                           },
//                                         ),
//                                       ],
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           );
//                         },
//                       );
//                     } else {
//                       double? averageRating = user.averageRating;
//                       int? nos = user.noOfRatingReceived;
//                       consolelog("Selected User: ${user.id}");
//                       comments = await getComments(user.id, 0);
//                       totalCommentsCount = nos!;
//                       currentPage = 0;

//                       showModalBottomSheet(
//                         context: context,
//                         isScrollControlled: true,
//                         builder: (context) => DraggableScrollableSheet(
//                           expand: false,
//                           initialChildSize: 0.4,
//                           minChildSize: 0.25,
//                           maxChildSize: 0.9,
//                           builder: (_, scrollController) {
//                             return Container(
//                               padding: const EdgeInsets.all(16),
//                               decoration: const BoxDecoration(
//                                 color: Colors.white,
//                                 borderRadius: BorderRadius.only(
//                                   topRight: Radius.circular(28),
//                                   topLeft: Radius.circular(28),
//                                 ),
//                               ),
//                               child: Column(
//                                 children: [
//                                   // Header Section
//                                   Row(
//                                     mainAxisAlignment:
//                                         MainAxisAlignment.spaceBetween,
//                                     children: [
//                                       // User Info Section
//                                       Row(
//                                         children: [
//                                           CircleAvatar(
//                                             radius: 30,
//                                             backgroundImage:
//                                                 user.picture != null
//                                                     ? NetworkImage(
//                                                         "$baseUrl/api/allimg/image/${user.picture}",
//                                                         headers: {
//                                                           'Authorization':
//                                                               "Bearer $token"
//                                                         },
//                                                       )
//                                                     : null,
//                                             backgroundColor: Colors.grey[300],
//                                             child: user.picture == null
//                                                 ? const Icon(Icons.person,
//                                                     size: 30,
//                                                     color: Colors.white)
//                                                 : null,
//                                           ),
//                                           const SizedBox(width: 6),
//                                           Column(
//                                             crossAxisAlignment:
//                                                 CrossAxisAlignment.start,
//                                             children: [
//                                               Text(
//                                                 user.fullName.length > 20
//                                                     ? "${user.fullName.substring(0, 18)}.."
//                                                     : user.fullName,

//                                                 style: TextStyle(
//                                                   fontSize: MediaQuery.of(
//                                                               context)
//                                                           .size
//                                                           .width *
//                                                       0.04, // Adjust the multiplier as needed
//                                                   fontWeight: FontWeight.bold,
//                                                   color: Colors.black87,
//                                                 ),
//                                                 overflow: TextOverflow
//                                                     .ellipsis, // Adds ellipsis if the text overflows
//                                                 maxLines:
//                                                     1, // Ensures text stays in a single line
//                                               ),
//                                               const SizedBox(height: 4),
//                                               Row(
//                                                 children: [
//                                                   Icon(
//                                                     Icons.phone,
//                                                     color: Colors.blueAccent,
//                                                     size: MediaQuery.of(context)
//                                                             .size
//                                                             .width *
//                                                         0.08,
//                                                   ),
//                                                   const SizedBox(width: 6),
//                                                   Text(
//                                                     '${user.mobileNumber}',
//                                                     style: TextStyle(
//                                                       fontSize: MediaQuery.of(
//                                                                   context)
//                                                               .size
//                                                               .width *
//                                                           0.04, // Adjust the multiplier as needed,
//                                                       color: Colors.black54,
//                                                     ),
//                                                   ),
//                                                 ],
//                                               ),
//                                               const SizedBox(height: 8),
//                                               Row(
//                                                 children: [
//                                                   RatingBarIndicator(
//                                                     rating: averageRating!,
//                                                     itemSize:
//                                                         MediaQuery.of(context)
//                                                                 .size
//                                                                 .width *
//                                                             0.04,
//                                                     itemBuilder: (context,
//                                                             index) =>
//                                                         const Icon(
//                                                             Icons.star_rounded,
//                                                             color:
//                                                                 Colors.amber),
//                                                   ),
//                                                   const SizedBox(width: 8),
//                                                   Text(
//                                                     "${averageRating.toStringAsFixed(1)} (${nos.toString()})",
//                                                     style: TextStyle(
//                                                       fontSize:
//                                                           MediaQuery.of(context)
//                                                                   .size
//                                                                   .width *
//                                                               0.04,
//                                                       fontWeight:
//                                                           FontWeight.w500,
//                                                       color: Colors.grey[700],
//                                                     ),
//                                                   ),
//                                                 ],
//                                               ),
//                                             ],
//                                           ),
//                                         ],
//                                       ),
//                                       // Action Buttons

//                                       Column(
//                                         children: [
//                                           // Call Button (at the top)
//                                           Container(
//                                             margin: const EdgeInsets.only(
//                                                 left:
//                                                     30), // Adjust the value as needed
//                                             decoration: BoxDecoration(
//                                               color:
//                                                   Colors.green.withOpacity(0.1),
//                                               borderRadius:
//                                                   BorderRadius.circular(50),
//                                             ),
//                                             child: IconButton(
//                                               icon: const Icon(Icons.call,
//                                                   color: Colors.green),
//                                               onPressed: () async {
//                                                 final Uri url = Uri(
//                                                     scheme: 'tel',
//                                                     path: user.mobileNumber);
//                                                 if (await canLaunchUrl(url)) {
//                                                   await launchUrl(url);
//                                                 }
//                                               },
//                                               padding: EdgeInsets
//                                                   .zero, // Remove default padding
//                                               constraints:
//                                                   BoxConstraints.tightFor(
//                                                 width: size.width *
//                                                     0.09, // Set the width to match the rating button
//                                                 height: size.height *
//                                                     0.03, // Set the height to match the rating button
//                                               ),
//                                             ),
//                                           ),

//                                           const SizedBox(
//                                               height:
//                                                   10), // Space between Call button and Rating/Report row

//                                           // Row containing Rating and Report buttons
//                                           Row(
//                                             mainAxisAlignment: MainAxisAlignment
//                                                 .start, // Align buttons to the start
//                                             children: [
//                                               // Rating Button
//                                               InkWell(
//                                                 onTap: () {
//                                                   if (tid == user.id) {
//                                                     errorToast(
//                                                         msg:
//                                                             "You can't rate yourself");
//                                                   } else {
//                                                     _showRatingDialog(user);
//                                                   }
//                                                 },
//                                                 borderRadius:
//                                                     BorderRadius.circular(40),
//                                                 child: Container(
//                                                   padding:
//                                                       const EdgeInsets.all(12),
//                                                   decoration: BoxDecoration(
//                                                     color: Colors.blueAccent
//                                                         .withOpacity(0.2),
//                                                     borderRadius:
//                                                         BorderRadius.circular(
//                                                             30),
//                                                   ),
//                                                   child: Icon(
//                                                     Icons.star,
//                                                     color: Colors.blueAccent,
//                                                     size: size.width * 0.05,
//                                                   ),
//                                                 ),
//                                               ),
//                                               SizedBox(
//                                                 width: size.width * 0.04,
//                                               ), // Space between Rating and Report buttons

//                                               // Report Button
//                                               InkWell(
//                                                 onTap: () {
//                                                   if (tid == user.id) {
//                                                     errorToast(
//                                                         msg:
//                                                             "You can't report yourself");
//                                                   } else {
//                                                     reasonController.text = "";
//                                                     showDialog(
//                                                       context: context,
//                                                       builder: (BuildContext
//                                                           context) {
//                                                         return AlertDialog(
//                                                           shape:
//                                                               RoundedRectangleBorder(
//                                                             borderRadius:
//                                                                 BorderRadius
//                                                                     .circular(
//                                                                         32), // Rounded corners for the dialog box
//                                                           ),
//                                                           backgroundColor:
//                                                               Colors.white,
//                                                           titlePadding:
//                                                               EdgeInsets.zero,
//                                                           title: Container(
//                                                             padding:
//                                                                 const EdgeInsets
//                                                                     .all(24),
//                                                             decoration:
//                                                                 const BoxDecoration(
//                                                               color: Colors.red,
//                                                               borderRadius:
//                                                                   BorderRadius.vertical(
//                                                                       top: Radius
//                                                                           .circular(
//                                                                               16)),
//                                                             ),
//                                                             child: Center(
//                                                               // Center the text
//                                                               child: Text(
//                                                                 'Report  ${user.fullName}',
//                                                                 style:
//                                                                     TextStyle(
//                                                                   fontSize: MediaQuery.of(
//                                                                               context)
//                                                                           .size
//                                                                           .width *
//                                                                       0.045,
//                                                                   fontWeight:
//                                                                       FontWeight
//                                                                           .bold,
//                                                                   color: Colors
//                                                                       .white,
//                                                                 ),
//                                                               ),
//                                                             ),
//                                                           ),
//                                                           contentPadding: EdgeInsets
//                                                               .all(MediaQuery.of(
//                                                                           context)
//                                                                       .size
//                                                                       .width *
//                                                                   0.04), // Responsive padding
//                                                           content: Column(
//                                                             mainAxisSize:
//                                                                 MainAxisSize
//                                                                     .min,
//                                                             children: [
//                                                               Text(
//                                                                 'Please provide a reason for reporting this service provider.',
//                                                                 style:
//                                                                     TextStyle(
//                                                                   fontSize: MediaQuery.of(
//                                                                               context)
//                                                                           .size
//                                                                           .width *
//                                                                       0.04, // Responsive font size
//                                                                 ),
//                                                               ),
//                                                               SizedBox(
//                                                                 height: MediaQuery.of(
//                                                                             context)
//                                                                         .size
//                                                                         .height *
//                                                                     0.02, // Responsive spacing
//                                                               ),
//                                                               TextField(
//                                                                 controller:
//                                                                     reasonController,
//                                                                 maxLines: 2,
//                                                                 decoration:
//                                                                     InputDecoration(
//                                                                   hintText:
//                                                                       'Enter your reason here',
//                                                                   hintStyle: TextStyle(
//                                                                       color: Colors
//                                                                           .grey
//                                                                           .shade600),
//                                                                   border:
//                                                                       OutlineInputBorder(
//                                                                     borderRadius:
//                                                                         BorderRadius.circular(
//                                                                             10),
//                                                                     borderSide: BorderSide(
//                                                                         color: Colors
//                                                                             .grey
//                                                                             .shade400),
//                                                                   ),
//                                                                   focusedBorder:
//                                                                       OutlineInputBorder(
//                                                                     borderRadius:
//                                                                         BorderRadius.circular(
//                                                                             10),
//                                                                     borderSide:
//                                                                         const BorderSide(
//                                                                             color:
//                                                                                 Colors.red),
//                                                                   ),
//                                                                   contentPadding:
//                                                                       EdgeInsets
//                                                                           .symmetric(
//                                                                     vertical: MediaQuery.of(context)
//                                                                             .size
//                                                                             .height *
//                                                                         0.015, // Responsive padding
//                                                                     horizontal: MediaQuery.of(context)
//                                                                             .size
//                                                                             .width *
//                                                                         0.04, // Responsive horizontal padding
//                                                                   ),
//                                                                 ),
//                                                               ),
//                                                             ],
//                                                           ),
//                                                           actions: <Widget>[
//                                                             Padding(
//                                                               padding:
//                                                                   const EdgeInsets
//                                                                       .symmetric(
//                                                                       horizontal:
//                                                                           8.0),
//                                                               child: Row(
//                                                                 mainAxisAlignment:
//                                                                     MainAxisAlignment
//                                                                         .spaceBetween,
//                                                                 children: [
//                                                                   Expanded(
//                                                                     child:
//                                                                         TextButton(
//                                                                       style: TextButton
//                                                                           .styleFrom(
//                                                                         backgroundColor: Colors
//                                                                             .grey
//                                                                             .shade300,
//                                                                         padding: const EdgeInsets
//                                                                             .symmetric(
//                                                                             vertical:
//                                                                                 14),
//                                                                         shape:
//                                                                             RoundedRectangleBorder(
//                                                                           borderRadius:
//                                                                               BorderRadius.circular(10),
//                                                                         ),
//                                                                       ),
//                                                                       child:
//                                                                           const Text(
//                                                                         'Cancel',
//                                                                         style: TextStyle(
//                                                                             color:
//                                                                                 Colors.black,
//                                                                             fontSize: 16),
//                                                                       ),
//                                                                       onPressed:
//                                                                           () {
//                                                                         Navigator.of(context)
//                                                                             .pop();
//                                                                       },
//                                                                     ),
//                                                                   ),
//                                                                   const SizedBox(
//                                                                       width:
//                                                                           10),
//                                                                   Expanded(
//                                                                     child:
//                                                                         ElevatedButton(
//                                                                       style: ElevatedButton
//                                                                           .styleFrom(
//                                                                         backgroundColor:
//                                                                             Colors.red,
//                                                                         padding: const EdgeInsets
//                                                                             .symmetric(
//                                                                             vertical:
//                                                                                 14),
//                                                                         shape:
//                                                                             RoundedRectangleBorder(
//                                                                           borderRadius:
//                                                                               BorderRadius.circular(10),
//                                                                         ),
//                                                                         elevation:
//                                                                             2,
//                                                                       ),
//                                                                       child:
//                                                                           Text(
//                                                                         'Submit',
//                                                                         style:
//                                                                             TextStyle(
//                                                                           color:
//                                                                               Colors.white,
//                                                                           fontSize:
//                                                                               MediaQuery.of(context).size.width * 0.04,
//                                                                         ),
//                                                                       ),
//                                                                       onPressed:
//                                                                           () {
//                                                                         if (reasonController
//                                                                             .text
//                                                                             .isEmpty) {
//                                                                           errorToast(
//                                                                               msg: "Fill the text box");
//                                                                         } else {
//                                                                           reportServiceProvider(
//                                                                               tid!,
//                                                                               user.id,
//                                                                               reasonController.text.toString());
//                                                                           Navigator.of(context)
//                                                                               .pop();
//                                                                         }
//                                                                         // Handle the report submission logic
//                                                                       },
//                                                                     ),
//                                                                   ),
//                                                                 ],
//                                                               ),
//                                                             ),
//                                                           ],
//                                                         );
//                                                       },
//                                                     );
//                                                   }
//                                                 },
//                                                 borderRadius:
//                                                     BorderRadius.circular(50),
//                                                 child: Container(
//                                                   padding:
//                                                       const EdgeInsets.all(12),
//                                                   decoration: BoxDecoration(
//                                                     color: Colors.red
//                                                         .withOpacity(0.15),
//                                                     borderRadius:
//                                                         BorderRadius.circular(
//                                                             50),
//                                                     boxShadow: const [
//                                                       BoxShadow(
//                                                         color: Colors.black12,
//                                                         blurRadius: 6,
//                                                         offset: Offset(0,
//                                                             4), // Shadow under the button
//                                                       ),
//                                                     ],
//                                                   ),
//                                                   child: Icon(
//                                                     Icons.report,
//                                                     color: Colors.red,
//                                                     size: MediaQuery.of(context)
//                                                             .size
//                                                             .width *
//                                                         0.06,
//                                                   ),
//                                                 ),
//                                               ),
//                                             ],
//                                           ),
//                                         ],
//                                       ),
//                                     ],
//                                   ),
//                                   const Divider(
//                                       color: Colors.black26, height: 20),

//                                   // Reviews Title
//                                   Align(
//                                     alignment: Alignment.centerLeft,
//                                     child: Text(
//                                       'Reviews',
//                                       style: TextStyle(
//                                         fontSize:
//                                             MediaQuery.of(context).size.width *
//                                                 0.045,
//                                         fontWeight: FontWeight.bold,
//                                         color: Colors.black87,
//                                       ),
//                                     ),
//                                   ),
//                                   const SizedBox(height: 10),

//                                   Expanded(
//                                     child: StatefulBuilder(
//                                       builder: (context, setModalState) {
//                                         return ListView.builder(
//                                           controller: scrollController,
//                                           itemCount: comments.length +
//                                               (totalCommentsCount >
//                                                       comments.length
//                                                   ? 1
//                                                   : 0), // +1 for the "See More" button
//                                           itemBuilder: (context, index) {
//                                             if (index < comments.length) {
//                                               // Display the comment item
//                                               final comment = comments[index];
//                                               return _buildReviewItem(
//                                                 comment.ratingFullName,
//                                                 comment.score,
//                                                 comment.comment,
//                                                 comment.date,
//                                               );
//                                             } else {
//                                               // Display the "See More" button
//                                               return Padding(
//                                                 padding:
//                                                     const EdgeInsets.symmetric(
//                                                         vertical: 16.0),
//                                                 child: ElevatedButton(
//                                                   onPressed: () async {
//                                                     setModalState(() {
//                                                       loadingMore =
//                                                           true; // Show loading indicator
//                                                     });
//                                                     currentPage++;

//                                                     // Fetch new comments
//                                                     List<Comment> newComments =
//                                                         await getComments(
//                                                             user.id,
//                                                             currentPage);
//                                                     setModalState(() {
//                                                       comments.addAll(
//                                                           newComments); // Update comments list
//                                                       loadingMore =
//                                                           false; // Hide loading indicator
//                                                     });
//                                                   },
//                                                   child: loadingMore
//                                                       ? const CircularProgressIndicator()
//                                                       : Text(
//                                                           'See More (${totalCommentsCount - comments.length})'),
//                                                 ),
//                                               );
//                                             }
//                                           },
//                                         );
//                                       },
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                             );
//                           },
//                         ),
//                       );
//                     }
//                   },
//                   infoWindow: InfoWindow(
//                     title: user.fullName,
//                     snippet: (authController.token == null ||
//                             authController.token!.isEmpty)
//                         ? 'Sign in to view details'
//                         : '⭐ ${user.averageRating} (${user.noOfRatingReceived})',
//                   ));
//             }).toSet();
//             return GoogleMap(
//               initialCameraPosition: CameraPosition(
//                 target: currentPosition,
//                 zoom: 14.4746,
//               ),
//               mapType: MapType.normal,
//               myLocationEnabled: true,
//               compassEnabled: true,
//               onMapCreated: (GoogleMapController controller) {
//                 _customInfoWindowController.googleMapController = controller;
//                 _controller.complete(controller);
//               },
//               markers: markers.union(_marker.toSet()),
//             );
//           }),
//           Positioned(
//             bottom: 100,
//             right: 5,
//             child: FloatingActionButton(
//               onPressed: () async {
//                 getUserCurrentLocation().then((value) async {
//                   // _marker.add(
//                   _circles.add(
//                     Circle(
//                       circleId: const CircleId("currentLocationCircle"),
//                       center: LatLng(value.latitude, value.longitude),
//                       radius: 20, // Radius in meters
//                       fillColor: Colors.red
//                           .withOpacity(1), // Red color with 90% opacity
//                       strokeColor: Colors
//                           .redAccent, // Optional: make the border a different shade
//                       strokeWidth: 3,
//                     ),
//                   );

//                   CameraPosition cameraPosition = CameraPosition(
//                     target: LatLng(value.latitude, value.longitude),
//                     zoom: 14,
//                   );

//                   final GoogleMapController controller =
//                       await _controller.future;
//                   controller.animateCamera(
//                       CameraUpdate.newCameraPosition(cameraPosition));
//                   setState(() {});
//                 });
//               },
//               child: const Icon(Icons.my_location),
//             ),
//           ),

//           // Add this near your other Positioned widgets in the Stack
//           Positioned(
//             bottom: 160,
//             right: 5,
//             child: FloatingActionButton(
//               onPressed: () {
//                 _sortProvidersByDistance();
//                 setState(() {
//                   _showServiceProvidersList = !_showServiceProvidersList;
//                 });
//               },
//               child: Icon(
//                 _showServiceProvidersList ? Icons.close : Icons.list,
//                 color: Colors.white,
//               ),
//               backgroundColor: Color.fromARGB(240, 0, 131, 143),
//             ),
//           ),

//           Positioned(
//             bottom: 100,
//             right: 5,
//             child: FloatingActionButton(
//               onPressed: () async {
//                 getUserCurrentLocation().then((value) async {
//                   _circles.add(
//                     Circle(
//                       circleId: const CircleId("currentLocationCircle"),
//                       center: LatLng(value.latitude, value.longitude),
//                       radius: 20,
//                       fillColor: Colors.red.withOpacity(1),
//                       strokeColor: Colors.redAccent,
//                       strokeWidth: 3,
//                     ),
//                   );

//                   CameraPosition cameraPosition = CameraPosition(
//                     target: LatLng(value.latitude, value.longitude),
//                     zoom: 14,
//                   );

//                   final GoogleMapController controller =
//                       await _controller.future;
//                   controller.animateCamera(
//                       CameraUpdate.newCameraPosition(cameraPosition));
//                   setState(() {});
//                 });
//               },
//               child: const Icon(Icons.my_location),
//             ),
//           ),

//           if (_showServiceProvidersList)
//             Positioned(
//               bottom: 100,
//               left: 0,
//               right: 0,
//               child: Container(
//                 height: MediaQuery.of(context).size.height * 0.5,
//                 margin: EdgeInsets.all(10),
//                 decoration: BoxDecoration(
//                   color: Colors.white,
//                   borderRadius: BorderRadius.circular(15),
//                   boxShadow: [
//                     BoxShadow(
//                       color: Colors.black26,
//                       blurRadius: 10,
//                       spreadRadius: 2,
//                     ),
//                   ],
//                 ),
//                 child: Column(
//                   children: [
//                     Padding(
//                       padding: const EdgeInsets.all(12.0),
//                       child: Row(
//                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                         children: [
//                           Text(
//                             'Service Providers Nearby',
//                             style: TextStyle(
//                               fontSize: 18,
//                               fontWeight: FontWeight.bold,
//                               color: Color.fromARGB(240, 0, 131, 143),
//                             ),
//                           ),
//                           IconButton(
//                             icon: Icon(Icons.close),
//                             onPressed: () {
//                               setState(() {
//                                 _showServiceProvidersList = false;
//                               });
//                             },
//                           ),
//                         ],
//                       ),
//                     ),
//                     Expanded(
//                       child: _sortedServiceProviders.isEmpty
//                           ? Center(child: Text('No service providers found'))
//                           : ListView.builder(
//                               itemCount: _sortedServiceProviders.length,
//                               itemBuilder: (context, index) {
//                                 final provider = _sortedServiceProviders[index];
//                                 String distanceText;
//                                 if (provider.distance! < 1000) {
//                                   distanceText =
//                                       '${provider.distance!.toStringAsFixed(0)} meters';
//                                 } else {
//                                   distanceText =
//                                       '${(provider.distance! / 1000).toStringAsFixed(1)} km';
//                                 }

//                                 return ListTile(
//                                   leading: CircleAvatar(
//                                     backgroundImage: provider.picture != null
//                                         ? NetworkImage(
//                                             "$baseUrl/api/allimg/image/${provider.picture}",
//                                             headers: {
//                                               'Authorization': "Bearer $token"
//                                             },
//                                           )
//                                         : null,
//                                     child: provider.picture == null
//                                         ? Icon(Icons.person)
//                                         : null,
//                                   ),
//                                   title: Text(provider.fullName),
//                                   subtitle: Column(
//                                     crossAxisAlignment:
//                                         CrossAxisAlignment.start,
//                                     children: [
//                                       RatingBarIndicator(
//                                         rating: provider.averageRating ?? 0,
//                                         itemSize: 15,
//                                         itemBuilder: (context, _) => Icon(
//                                           Icons.star,
//                                           color: Colors.amber,
//                                         ),
//                                       ),
//                                       Text(distanceText),
//                                     ],
//                                   ),
//                                   trailing: IconButton(
//                                     icon:
//                                         Icon(Icons.phone, color: Colors.green),
//                                     onPressed: () {
//                                       final Uri url = Uri(
//                                         scheme: 'tel',
//                                         path: provider.mobileNumber,
//                                       );
//                                       launchUrl(url);
//                                     },
//                                   ),
//                                   onTap: () {
//                                     _controller.future.then((controller) {
//                                       controller.animateCamera(
//                                         CameraUpdate.newLatLng(
//                                           LatLng(
//                                             double.parse(provider.latitude),
//                                             double.parse(provider.longitude),
//                                           ),
//                                         ),
//                                       );
//                                     });
//                                   },
//                                 );
//                               },
//                             ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//         ],
//       ),
//     );
//   }
// }

// Widget _buildReviewList(List<Comment> comments) {
//   // If there are no comments, display a message
//   if (comments.isEmpty) {
//     return const Center(child: Text('No reviews available.'));
//   }

//   // Return empty container if called directly
//   return const SizedBox.shrink();
// }

// // Function to fetch comments
// Future<List<Comment>> getComments(int id, int page) async {
//   List<Comment> comments = await fetchComments(id, page, 10);
//   return comments; // Return the list of comments
// }

// // Function to display individual reviews with a modern card style
// Widget _buildReviewItem(
//     String reviewerName, double rating, String comment, String dat) {
//   DateFormat inputFormat = DateFormat('dd-MM-yy');
//   DateTime date = inputFormat.parse(dat);
//   String formattedDate = DateFormat('dd MMMM yyyy').format(date);

//   return Card(
//     elevation: 3,
//     margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
//     child: ListTile(
//       leading: CircleAvatar(
//         backgroundColor: Colors.teal[200],
//         child: const Icon(Icons.person, color: Colors.white),
//       ),
//       title: Text(
//         reviewerName,
//         style: const TextStyle(fontWeight: FontWeight.bold),
//       ),
//       subtitle: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Row(
//             children: [
//               RatingBarIndicator(
//                 rating: rating,
//                 itemSize: 18,
//                 itemBuilder: (context, index) =>
//                     const Icon(Icons.star, color: Colors.amber),
//               ),
//               const Spacer(),
//               Text(
//                 formattedDate,
//                 style: TextStyle(color: Colors.grey[500], fontSize: 12),
//               ),
//             ],
//           ),
//           const SizedBox(height: 4),
//           Text(
//             comment,
//             style: TextStyle(color: Colors.grey[600]),
//           ),
//         ],
//       ),
//     ),
//   );
// }

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:geolocator/geolocator.dart' as geo;

import 'package:custom_info_window/custom_info_window.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Import for clipboard functionality
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/network/models/service_map_model.dart';
import 'package:smartsewa/network/services/authServices/auth_controller.dart';
import 'package:smartsewa/network/services/categories&services/service_map_controller.dart';
import 'package:smartsewa/network/services/orderService/filter_controller.dart';
import 'package:smartsewa/views/auth/registration/user_registration.dart';
import 'package:smartsewa/views/user_screen/approval/open_map_screen.dart';
import 'package:smartsewa/views/utils.dart';
import 'package:smartsewa/views/widgets/custom_toasts.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../network/base_client.dart';
import '../../../network/services/orderService/request_service.dart';
import '../../widgets/my_appbar.dart';

class MapScreen extends StatefulWidget {
  final String work;
  final String name;

  MapScreen({required this.work, required this.name});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  final CustomInfoWindowController _customInfoWindowController =
      CustomInfoWindowController();
  String baseUrl = BaseClient().baseUrl;
  var isLoading = false.obs;
  final myMapController = Get.put(MyMapController());
  final controller = Get.put(OrderController());
  final authController = Get.put(AuthController());
  final filterController = Get.put(FilterController());
  double averageRating = 0.0;
  TextEditingController reasonController = TextEditingController();
  String? token;
  List<Comment> comments = [];
  bool loadingMore = false;
  int currentPage = 0;
  int totalCommentsCount = 0;
  late Future<Widget> reviewListFuture;
  bool _showServiceProvidersList = false;
  List<ServiceMapModel> _sortedServiceProviders = [];
  List<ServiceMapModel> _filteredServiceProviders = []; // <-- Add this line
  TextEditingController _searchController = TextEditingController();
  bool isSearching = false;
  String searchQuery = '';
  TextEditingController searchController = TextEditingController();
  FocusNode searchFocusNode = FocusNode();

  final Completer<GoogleMapController> _controller = Completer();
  int? tid;
  final List<Marker> _marker = <Marker>[];
  final Set<Circle> _circles = {};

  LatLng currentPosition = const LatLng(27.709290, 85.348101);

  final CameraPosition _kGoogle = const CameraPosition(
    target: LatLng(27.707795, 85.343362),
    zoom: 14.4746,
  );

  @override
  void initState() {
    super.initState();
    getToken();
    _getUserLocation();
    Get.delete<MyMapController>();

    super.initState();
  }

  @override
  void dispose() {
    searchController.dispose();
    searchFocusNode.dispose();
    super.dispose();
  }

  // void _sortProvidersByDistance() {
  //   final userLocation = currentPosition;

  //   _sortedServiceProviders = getWorker().where((provider) {
  //     // Filter out providers with invalid coordinates
  //     if (provider.latitude.isEmpty || provider.longitude.isEmpty) return false;

  //     final lat = double.tryParse(provider.latitude);
  //     final lng = double.tryParse(provider.longitude);
  //     return lat != null && lng != null;
  //   }).map((provider) {
  //     final providerLocation = LatLng(
  //       double.parse(provider.latitude),
  //       double.parse(provider.longitude),
  //     );
  //     // Calculate distance in meters
  //     provider.distance = Geolocator.distanceBetween(
  //       userLocation.latitude,
  //       userLocation.longitude,
  //       providerLocation.latitude,
  //       providerLocation.longitude,
  //     );
  //     return provider;
  //   }).toList()
  //     ..sort((a, b) => a.distance!.compareTo(b.distance!));
  // }

  void _sortProvidersByDistance(List<ServiceMapModel> providers) {
    _sortedServiceProviders = providers.where((provider) {
      if (provider.latitude.isEmpty || provider.longitude.isEmpty) return false;

      final lat = double.tryParse(provider.latitude);
      final lng = double.tryParse(provider.longitude);
      return lat != null && lng != null;
    }).map((provider) {
      final providerLocation = LatLng(
        double.parse(provider.latitude),
        double.parse(provider.longitude),
      );
      provider.distance = Geolocator.distanceBetween(
        currentPosition.latitude,
        currentPosition.longitude,
        providerLocation.latitude,
        providerLocation.longitude,
      );
      return provider;
    }).toList()
      ..sort((a, b) => (a.distance ?? double.infinity)
          .compareTo(b.distance ?? double.infinity));
  }

  Future<Position> getUserCurrentLocation() async {
    await Geolocator.requestPermission()
        .then((value) {})
        .onError((error, stackTrace) async {
      await Geolocator.requestPermission();
      print("ERROR$error");
    });

    return await Geolocator.getCurrentPosition();
  }

// In the _MapScreenState class, modify the _navigateToProviderOnMap method:
  void _navigateToProviderOnMap(ServiceMapModel provider) async {
    final GoogleMapController controller = await _controller.future;
    consolelog(provider.fullName);

    // Create a marker for the selected provider with a custom info window
    final selectedMarker = Marker(
      markerId: MarkerId('selected_${provider.id}'),
      position: LatLng(
        double.parse(provider.latitude),
        double.parse(provider.longitude),
      ),
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange),
      infoWindow: InfoWindow(
        title: provider.fullName,
        snippet:
            '⭐ ${provider.averageRating?.toStringAsFixed(1) ?? '0.0'} (${provider.noOfRatingReceived ?? '0'})',
      ),
    );

    setState(() {
      // Clear any existing selected markers and add the new one
      _marker.removeWhere((m) => m.markerId.value.startsWith('selected_'));
      _marker.add(selectedMarker);

      _showServiceProvidersList = false;
      isSearching = false;
    });

    // Animate camera to the selected provider
    controller.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: LatLng(
            double.parse(provider.latitude),
            double.parse(provider.longitude),
          ),
          zoom: 16,
        ),
      ),
    );
  }

// In the GoogleMap widget, modify the markers parameter to include both sets
  // void _navigateToProviderOnMap(ServiceMapModel provider) async {
  //   final GoogleMapController controller = await _controller.future;

  //   controller.animateCamera(
  //     CameraUpdate.newCameraPosition(
  //       CameraPosition(
  //         target: LatLng(
  //           double.parse(provider.latitude),
  //           double.parse(provider.longitude),
  //         ),
  //         zoom: 16,
  //       ),
  //     ),
  //   );

  //   setState(() {
  //     _showServiceProvidersList = false;
  //     isSearching = false;
  //   });
  // }

  Set<Marker> _buildMarkers() {
    final markers = getWorker().map((user) {
      bool isCurrentUser = tid == user.id;

      return Marker(
        markerId: MarkerId(user.id.toString()),
        position: LatLng(
          double.parse(user.latitude),
          double.parse(user.longitude),
        ),
        icon: isCurrentUser
            ? BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue)
            : BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        onTap: () {
          consolelog("hello");
          // Your existing onTap logic
        },
      );
    }).toSet();

    return markers;
  }

  // List<ServiceMapModel> getWorker() {
  //   // Debug logs to understand the data
  //   consolelog("Available services:");
  //   for (var user in myMapController.users) {
  //     consolelog("User ${user.fullName}: Service = ${user.serviceProvided}");
  //   }
  //   consolelog("Selected work: ${widget.work}");

  //   var filteredUsers = myMapController.users.where((user) {
  //     if (user.serviceProvided == null) {
  //       return false;
  //     }

  //     // Clean up strings for comparison
  //     String cleanUserService = _cleanServiceString(user.serviceProvided!);
  //     String cleanSelectedWork = _cleanServiceString(widget.work);

  //     consolelog("Comparing: '$cleanUserService' with '$cleanSelectedWork'");

  //     // Check if the service contains the selected work or vice versa
  //     return cleanUserService.contains(cleanSelectedWork) ||
  //         cleanSelectedWork.contains(cleanUserService) ||
  //         cleanUserService == "all"; // Include users who provide all services
  //   }).toList();

  //   consolelog("Number of filtered users: ${filteredUsers.length}");

  //   if (searchQuery.isNotEmpty) {
  //     filteredUsers = filteredUsers
  //         .where((user) =>
  //             user.fullName.toLowerCase().contains(searchQuery.toLowerCase()))
  //         .toList();
  //     consolelog(
  //         "Number of users after search filter: ${filteredUsers.length}");
  //   }

  //   return filteredUsers;
  // }

  List<ServiceMapModel> getWorker() {
    // Debug logs to understand the data
    consolelog("Available services:");
    for (var user in myMapController.users) {
      consolelog("User ${user.fullName}: Service = ${user.serviceProvided}");
    }
    consolelog("Selected work: ${widget.work}");

    var filteredUsers = myMapController.users.where((user) {
      if (user.serviceProvided == null) {
        return false;
      }

      // Clean up strings for comparison
      String cleanUserService = _cleanServiceString(user.serviceProvided!);
      String cleanSelectedWork = _cleanServiceString(widget.work);

      consolelog("Comparing: '$cleanUserService' with '$cleanSelectedWork'");

      // Check if the service contains the selected work or vice versa
      return cleanUserService.contains(cleanSelectedWork) ||
          cleanSelectedWork.contains(cleanUserService) ||
          cleanUserService == "all"; // Include users who provide all services
    }).toList();

    consolelog("Number of filtered users: ${filteredUsers.length}");

    if (searchQuery.isNotEmpty) {
      filteredUsers = filteredUsers
          .where((user) =>
              user.fullName.toLowerCase().contains(searchQuery.toLowerCase()))
          .toList();
      consolelog(
          "Number of users after search filter: ${filteredUsers.length}");
    }

    return filteredUsers;
  }

  String _cleanServiceString(String input) {
    // Convert to lowercase and trim whitespace
    String cleaned = input.trim().toLowerCase();

    // List of common words to remove
    final List<String> commonWords = ['service', 'services', 'and', '&', '-'];

    // Remove common words
    for (String word in commonWords) {
      cleaned = cleaned.replaceAll(word, '').trim();
    }

    return cleaned;
  }

  // void _getUserLocation() async {
  //   var position = await GeolocatorPlatform.instance.getCurrentPosition();
  //   setState(() {
  //     currentPosition = LatLng(position.latitude, position.longitude);

  //     // Add blue marker for current user location
  //     _marker.add(
  //       Marker(
  //         markerId: MarkerId('current_location'),
  //         position: currentPosition,
  //         // icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
  //       ),
  //     );

  //     consolelog(currentPosition);
  //   });
  // }

  void _getUserLocation() async {
    var position = await GeolocatorPlatform.instance.getCurrentPosition();
    setState(() {
      currentPosition = LatLng(position.latitude, position.longitude);

      consolelog(currentPosition);
    });
  }

  Future<void> logCallClick(int userId, String phoneNumber) async {
    try {
      var headers = {'Authorization': 'Bearer $token'};
      var request = http.Request(
          'POST',
          Uri.parse(
              '$baseUrl/api/call-logs/click?userId=$userId&phoneNumber=$phoneNumber'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      } else {
        print(response.reasonPhrase);
        throw Exception('Failed to log call click');
      }
    } catch (e) {
      print('Error logging call: $e');
    }
  }

  Future<void> getUserId(int id) async {
    setState(() {});
  }

  // void getToken() async {
  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   String? apptoken = prefs.getString("token");
  //   int? id = prefs.getInt("id");
  //   setState(() {
  //     token = apptoken;
  //     tid = id;
  //   });
  // }

  void getToken() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? apptoken = prefs.getString("token");
    int? id = prefs.getInt("id");
    setState(() {
      token = apptoken;
      tid = id;
    });

    // Update AuthController's token
    authController.token = apptoken;
    authController.update();
  }

  void _showRatingDialog(ServiceMapModel user) {
    double userRating = 0;
    String userComment = '';

    showDialog(
      context: context,
      barrierDismissible: true, // Allows dismissing by tapping outside
      builder: (BuildContext context) {
        return LayoutBuilder(
          builder: (context, constraints) {
            // Responsive design based on screen width
            bool isLargeScreen = constraints.maxWidth > 600;

            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              insetPadding: EdgeInsets.symmetric(
                  horizontal: isLargeScreen ? 40 : 20,
                  vertical: isLargeScreen ? 60 : 40),
              child: SingleChildScrollView(
                // Make the dialog content scrollable
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(25),
                    color: Colors.white,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Title section (keep original color)
                      Container(
                        padding: EdgeInsets.all(isLargeScreen ? 30 : 20),
                        decoration: const BoxDecoration(
                          color: Color.fromARGB(
                              240, 0, 131, 143), // Original teal color
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(25),
                            topRight: Radius.circular(25),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            'Rate ${user.fullName}',
                            style: TextStyle(
                              fontSize: isLargeScreen ? 24 : 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),

                      // Content Section
                      Padding(
                        padding: EdgeInsets.all(isLargeScreen ? 20 : 9),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Question Text
                            const Text(
                              'How was your experience?',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 10),

                            // Star Rating Bar
                            RatingBar.builder(
                              initialRating: 0,
                              minRating: 1,
                              direction: Axis.horizontal,
                              allowHalfRating: true,
                              itemCount: 5,
                              itemSize:
                                  isLargeScreen ? 50 : 40, // Responsive size
                              unratedColor: Colors.grey[300],
                              glowColor: Colors.amber,
                              itemBuilder: (context, _) => const Icon(
                                Icons.star_rounded,
                                color: Colors.amber,
                              ),
                              onRatingUpdate: (rating) {
                                userRating = rating;
                              },
                            ),
                            const SizedBox(height: 10),
                            // Comment Input
                            TextField(
                              maxLines: 2,
                              decoration: InputDecoration(
                                contentPadding:
                                    EdgeInsets.all(isLargeScreen ? 20 : 15),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                  borderSide: BorderSide.none,
                                ),
                                hintText: 'Share your feedback...',
                                filled: true,
                                fillColor: Colors.grey[100],
                                hintStyle: const TextStyle(color: Colors.grey),
                              ),
                              onChanged: (value) {
                                userComment = value;
                              },
                            ),
                          ],
                        ),
                      ),

                      // Action Buttons
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: isLargeScreen ? 30 : 20,
                            vertical: isLargeScreen ? 20 : 15),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Cancel Button (keep original color)
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                                label: const Text('Cancel'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors
                                      .redAccent, // Original color for cancel
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  padding: EdgeInsets.symmetric(
                                      vertical: isLargeScreen ? 20 : 15),
                                  textStyle: TextStyle(
                                    fontSize: isLargeScreen ? 18 : 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 15),

                            // Submit Button (keep original color)
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: () {
                                  consolelog("Rated User: ${user.id}");
                                  if (userRating == 0 || userComment == "") {
                                    errorToast(
                                        msg: "Empty Rating or Comment Field");
                                  } else {
                                    submitRating(
                                        tid!, user.id, userRating, userComment);
                                    Navigator.of(context).pop();
                                  }
                                },
                                label: const Text('Submit'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color.fromARGB(
                                      240, 0, 131, 143), // Original teal
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  padding: EdgeInsets.symmetric(
                                      vertical: isLargeScreen ? 20 : 15),
                                  textStyle: TextStyle(
                                    fontSize: isLargeScreen ? 18 : 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Future<void> submitRating(
      int ratingUserId, int userId, double score, String comment) async {
    // Replace with actual service ID as needed

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/v1/rating/serviceID'), // Use the correct IP
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token', // Add your authorization token
        },
        body: jsonEncode({
          'ratingUserid': ratingUserId,
          'userId': userId,
          'score': score,
          'comment': comment,
        }),
      );

      // Check for errors in the response
      if (response.statusCode == 200) {
        successToast(msg: "User Rated Successfully");
        // Handle successful response
        print('Rating submitted successfully');
      } else {
        errorToast(msg: response.body);
        // Handle non-200 responses
        print('Failed to submit rating: ${response.statusCode}');
        print('Response body: ${response.body}');
      }
    } catch (e) {
      errorToast(msg: "Internal Server Error");
      // Handle network or other errors
      print('Error occurred while submitting rating: $e');
    }
  }

  Future<void> reportServiceProvider(
      int reportingUserId, int reportedUserId, String reportText) async {
    var headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };

    var body = json.encode({
      "report": reportText,
    });

    var url =
        Uri.parse('$baseUrl/api/v1/reports/$reportingUserId/$reportedUserId');

    try {
      var response = await http.post(url, headers: headers, body: body);

      if (response.statusCode == 200) {
        print('Report successfully Submitted: ${response.body}');
        successToast(msg: "Report Successfully Submitted");
      } else {
        print('Failed to Submit Report: ${response.reasonPhrase}');
        errorToast(msg: "Failed to Submit Report");
      }
    } catch (e) {
      print('Error occurred: $e');
      errorToast(msg: "An Error Occurred \nFailed to Submit Report");
    }
  }

  // PreferredSizeWidget _buildAppBar() {
  //   final Color appBarColor =
  //       const Color.fromARGB(240, 0, 131, 143); // App bar color
  //   final Color iconColor = Colors.white; // Icon color

  //   if (isSearching) {
  //     return AppBar(
  //       systemOverlayStyle: const SystemUiOverlayStyle(
  //           statusBarColor: Color.fromARGB(240, 0, 96, 100)),
  //       backgroundColor: appBarColor,
  //       iconTheme: IconThemeData(color: iconColor),
  //       leading: IconButton(
  //         icon: Icon(Icons.arrow_back, color: iconColor), // White back arrow
  //         onPressed: () {
  //           setState(() {
  //             isSearching = false;
  //             searchQuery = '';
  //             searchController.clear();
  //           });
  //         },
  //       ),
  //       title: Container(
  //         decoration: BoxDecoration(
  //           color: Colors.white,
  //           borderRadius: BorderRadius.circular(25),
  //         ),
  //         child: TextField(
  //           focusNode: searchFocusNode,
  //           controller: searchController,
  //           decoration: InputDecoration(
  //             hintText: 'Search service provider...',
  //             hintStyle: TextStyle(color: Colors.grey[500], fontSize: 16),
  //             prefixIcon: const Icon(
  //               Icons.search,
  //               color: Color.fromARGB(240, 0, 131, 143),
  //             ),
  //             border: InputBorder.none,
  //             contentPadding:
  //                 const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
  //           ),
  //           style: const TextStyle(color: Colors.black87, fontSize: 16),
  //           onChanged: (value) {
  //             setState(() {
  //               searchQuery = value;
  //             });
  //           },
  //         ),
  //       ),
  //       actions: [
  //         if (searchQuery.isNotEmpty)
  //           IconButton(
  //             icon: Icon(Icons.clear, color: iconColor), // White clear icon
  //             onPressed: () {
  //               searchController.clear();
  //               setState(() {
  //                 searchQuery = '';
  //               });
  //             },
  //           ),
  //       ],
  //       elevation: 4,
  //       shadowColor: Colors.black.withOpacity(0.2),
  //     );
  //   } else {
  //     return AppBar(
  //       systemOverlayStyle: const SystemUiOverlayStyle(
  //           statusBarColor: Color.fromARGB(240, 0, 96, 100)),
  //       backgroundColor: appBarColor,
  //       iconTheme: IconThemeData(color: iconColor),
  //       automaticallyImplyLeading: true,
  //       leading: IconButton(
  //         icon: Icon(Icons.arrow_back, color: iconColor),
  //         onPressed: () {
  //           Navigator.pop(context);
  //         },
  //       ),
  //       centerTitle: true,
  //       title: Text(
  //         widget.name,
  //         style: const TextStyle(
  //           fontSize: 20,
  //           fontWeight: FontWeight.w500,
  //           color: Colors.white,
  //         ),
  //       ),
  //       actions: [
  //         IconButton(
  //           icon: Icon(
  //             Icons.search,
  //             color: iconColor,
  //             size: 28, // Larger for visibility
  //           ),
  //           tooltip: 'Search',
  //           onPressed: () {
  //             setState(() {
  //               isSearching = true;
  //               // Request focus after a short delay
  //               Future.delayed(const Duration(milliseconds: 100), () {
  //                 searchFocusNode.requestFocus();
  //               });
  //             });
  //           },
  //           splashRadius: 24,
  //         ),
  //       ],
  //       elevation: 4,
  //       shadowColor: Colors.black.withOpacity(0.2),
  //     );
  //   }
  // }

  PreferredSizeWidget _buildAppBar() {
    final Color appBarColor = const Color.fromARGB(240, 0, 131, 143);
    final Color iconColor = Colors.white;

    final Size size = MediaQuery.of(context).size;
    final double baseWidth = 375.0; // iPhone 11 width
    final double textScale = size.width / baseWidth;

    // Responsive sizing with media queries
    final bool isSmallScreen = size.width < 360;
    final bool isMediumScreen = size.width >= 360 && size.width < 768;
    final bool isLargeScreen = size.width >= 768;

    // Dynamic sizing based on screen size
    final double iconSize = isSmallScreen
        ? 20 * textScale
        : isMediumScreen
            ? 24 * textScale
            : 28 * textScale;

    final double fontSize = isSmallScreen
        ? 16 * textScale
        : isMediumScreen
            ? 18 * textScale
            : 20 * textScale;

    final double hintFontSize = isSmallScreen
        ? 14 * textScale
        : isMediumScreen
            ? 16 * textScale
            : 18 * textScale;

    final double titleFontSize = isSmallScreen
        ? 18 * textScale
        : isMediumScreen
            ? 20 * textScale
            : 22 * textScale;

    // Responsive padding
    final EdgeInsets searchPadding = EdgeInsets.symmetric(
      horizontal: isSmallScreen
          ? 8.0
          : isMediumScreen
              ? 12.0
              : 16.0,
      vertical: isSmallScreen ? 4.0 : 8.0,
    );

    if (isSearching) {
      return AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Color.fromARGB(240, 0, 96, 100),
        ),
        backgroundColor: appBarColor,
        iconTheme: IconThemeData(color: iconColor),
        leading: Container(
          margin: EdgeInsets.all(isSmallScreen ? 4.0 : 8.0),
          child: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: iconColor,
              size: iconSize,
            ),
            onPressed: () {
              setState(() {
                isSearching = false;
                searchQuery = '';
                _searchController.clear();
                _showServiceProvidersList = false;
              });
            },
            splashRadius: iconSize,
            padding: EdgeInsets.all(isSmallScreen ? 4.0 : 8.0),
          ),
        ),
        title: Container(
          padding: searchPadding,
          child: TextField(
            focusNode: searchFocusNode,
            controller: _searchController,
            cursorColor: Colors.white,
            cursorWidth: isSmallScreen
                ? 1.5
                : isMediumScreen
                    ? 2.0
                    : 2.5, // Responsive cursor width
            cursorHeight: fontSize * 1.2,
            decoration: InputDecoration(
              hintText: 'Search ${widget.name} providers...',
              hintStyle: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: hintFontSize,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                vertical: isSmallScreen ? 8.0 : 12.0,
                horizontal: isSmallScreen ? 4.0 : 8.0,
              ),
              // Add clear icon when text is present
              suffixIcon: searchQuery.isNotEmpty
                  ? Container(
                      margin: EdgeInsets.only(right: isSmallScreen ? 4.0 : 8.0),
                      child: IconButton(
                        icon: Icon(
                          Icons.clear,
                          color: Colors.white,
                          size: iconSize *
                              0.9, // Slightly smaller than other icons
                        ),
                        onPressed: () {
                          setState(() {
                            searchQuery = '';
                            _searchController.clear();
                            _filterServiceProviders();
                          });
                          // Keep focus on search field after clearing
                          searchFocusNode.requestFocus();
                        },
                        splashRadius: iconSize * 0.7,
                        padding: EdgeInsets.all(isSmallScreen ? 2.0 : 4.0),
                        constraints: BoxConstraints(
                          minWidth: iconSize * 1.2,
                          minHeight: iconSize * 1.2,
                        ),
                      ),
                    )
                  : null,
            ),
            style: TextStyle(
              color: Colors.white,
              fontSize: fontSize,
              fontWeight: FontWeight.w400,
            ),
            onChanged: (value) {
              setState(() {
                searchQuery = value;
                _filterServiceProviders();
              });
            },
          ),
        ),
        elevation: 4,
        toolbarHeight: isSmallScreen
            ? 56.0
            : isMediumScreen
                ? 64.0
                : 72.0,
      );
    } else {
      return AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Color.fromARGB(240, 0, 96, 100),
        ),
        backgroundColor: appBarColor,
        iconTheme: IconThemeData(color: iconColor),
        automaticallyImplyLeading: true,
        leading: Container(
          margin: EdgeInsets.all(isSmallScreen ? 4.0 : 8.0),
          child: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: iconColor,
              size: iconSize,
            ),
            onPressed: () => Navigator.pop(context),
            splashRadius: iconSize,
            padding: EdgeInsets.all(isSmallScreen ? 4.0 : 8.0),
          ),
        ),
        centerTitle: true,
        title: Container(
          padding: EdgeInsets.symmetric(
            horizontal: isSmallScreen ? 8.0 : 16.0,
          ),
          child: Text(
            widget.name,
            style: TextStyle(
              fontSize: titleFontSize,
              fontWeight: FontWeight.w500,
              color: Colors.white,
              letterSpacing: 0.5,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
        actions: [
          Container(
            margin: EdgeInsets.only(
              right: isSmallScreen ? 4.0 : 8.0,
              top: isSmallScreen ? 4.0 : 8.0,
              bottom: isSmallScreen ? 4.0 : 8.0,
            ),
            child: IconButton(
              icon: Icon(
                Icons.search,
                color: iconColor,
                size: iconSize + (isSmallScreen ? 2 : 4),
              ),
              onPressed: () {
                setState(() {
                  isSearching = true;
                  _showServiceProvidersList = true;

                  _filterServiceProviders();
                  Future.delayed(const Duration(milliseconds: 100), () {
                    searchFocusNode.requestFocus();
                  });
                });
              },
              splashRadius: iconSize,
              padding: EdgeInsets.all(isSmallScreen ? 4.0 : 8.0),
              constraints: BoxConstraints(
                minWidth: iconSize * 1.5,
                minHeight: iconSize * 1.5,
              ),
            ),
          ),
        ],
        elevation: 4,
        toolbarHeight: isSmallScreen
            ? 56.0
            : isMediumScreen
                ? 64.0
                : 72.0,
      );
    }
  }

  void _filterServiceProviders() {
    final workers = getWorker();

    if (searchQuery.isEmpty) {
      _filteredServiceProviders = workers;
    } else {
      _filteredServiceProviders = workers.where((provider) {
        return provider.fullName
                .toLowerCase()
                .contains(searchQuery.toLowerCase()) ||
            (provider.serviceProvided
                    ?.toLowerCase()
                    .contains(searchQuery.toLowerCase()) ??
                false);
      }).toList();
    }

    // Sort by distance if we have current position
    if (currentPosition != null) {
      _sortProvidersByDistance(_filteredServiceProviders);
    }
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    consolelog("Name and Work:");
    consolelog(widget.name);
    consolelog(widget.work);
    return Scaffold(
      appBar: _buildAppBar(),
      body: Stack(
        children: [
          Obx(() {
            final markers = getWorker().map((user) {
              bool isConditionTrue = false;
              if (tid == user.id) {
                isConditionTrue = true;
              } else {
                isConditionTrue = false;
              }

              return Marker(
                  markerId: MarkerId(user.id.toString()),
                  position: LatLng(
                    double.parse(user.latitude),
                    double.parse(user.longitude),
                  ),
                  icon: isConditionTrue
                      ? BitmapDescriptor.defaultMarkerWithHue(
                          BitmapDescriptor.hueBlue)
                      : BitmapDescriptor.defaultMarkerWithHue(
                          BitmapDescriptor.hueRed),

                  //icon: BitmapDescriptor.defaultMarkerWithHue(
                  //  BitmapDescriptor.hueBlue),
                  onTap: () async {
                    if (authController.isUserGuest()) {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          final screenWidth = MediaQuery.of(context).size.width;
                          final isSmallScreen = screenWidth < 360;

                          return Dialog(
                            backgroundColor: Colors.transparent,
                            insetPadding: EdgeInsets.all(screenWidth * 0.05),
                            child: Container(
                              decoration: BoxDecoration(
                                color: const Color.fromARGB(240, 0, 131, 143),
                                borderRadius: BorderRadius.circular(25),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.3),
                                    blurRadius: 15,
                                    spreadRadius: 2,
                                  )
                                ],
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // Header Section
                                  Container(
                                    padding: EdgeInsets.all(screenWidth * 0.04),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(25),
                                        topRight: Radius.circular(25),
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.1),
                                          blurRadius: 8,
                                        )
                                      ],
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.verified_user_rounded,
                                          color: const Color.fromARGB(
                                              240, 0, 131, 143),
                                          size: screenWidth * 0.08,
                                        ),
                                        SizedBox(width: screenWidth * 0.03),
                                        Flexible(
                                          child: Text(
                                            'Access Required',
                                            style: TextStyle(
                                              color: const Color.fromARGB(
                                                  240, 0, 131, 143),
                                              fontSize: isSmallScreen ? 18 : 22,
                                              fontWeight: FontWeight.w700,
                                              letterSpacing: 0.8,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  // Content Section
                                  Padding(
                                    padding: EdgeInsets.all(screenWidth * 0.05),
                                    child: Column(
                                      children: [
                                        SizedBox(height: screenWidth * 0.03),
                                        Text(
                                          'Please   SignUp to get the More Service ',
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: isSmallScreen ? 14 : 16,
                                            height: 1.4,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        SizedBox(height: screenWidth * 0.06),
                                        // Buttons Row
                                        LayoutBuilder(
                                          builder: (context, constraints) {
                                            return Row(
                                              children: [
                                                Expanded(
                                                  child: ElevatedButton(
                                                    onPressed: () =>
                                                        Navigator.pop(context),
                                                    style: ElevatedButton
                                                        .styleFrom(
                                                      backgroundColor:
                                                          Colors.transparent,
                                                      elevation: 0,
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(15),
                                                        side: const BorderSide(
                                                          color: Colors.white,
                                                          width: 1.5,
                                                        ),
                                                      ),
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                        vertical:
                                                            screenWidth * 0.03,
                                                      ),
                                                    ),
                                                    child: Text(
                                                      'Later',
                                                      style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: isSmallScreen
                                                            ? 14
                                                            : 16,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(
                                                    width: screenWidth * 0.04),
                                                Expanded(
                                                  child: ElevatedButton(
                                                    onPressed: () {
                                                      Navigator.pop(context);
                                                      Get.to(() =>
                                                          UserRegistration());
                                                    },
                                                    style: ElevatedButton
                                                        .styleFrom(
                                                      backgroundColor:
                                                          Colors.white,
                                                      elevation: 4,
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(15),
                                                      ),
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                        vertical:
                                                            screenWidth * 0.03,
                                                      ),
                                                    ),
                                                    child: Text(
                                                      'Sign Up Now',
                                                      style: TextStyle(
                                                        color: const Color
                                                            .fromARGB(
                                                            240, 0, 131, 143),
                                                        fontSize: isSmallScreen
                                                            ? 14
                                                            : 16,
                                                        fontWeight:
                                                            FontWeight.w700,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      );
                    } else {
                      double? averageRating = user.averageRating;
                      int? nos = user.noOfRatingReceived;
                      consolelog("Selected User: ${user.id}");
                      comments = await getComments(user.id, 0);
                      totalCommentsCount = nos!;
                      currentPage = 0;

                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        builder: (context) => DraggableScrollableSheet(
                          expand: false,
                          initialChildSize: 0.4,
                          minChildSize: 0.25,
                          maxChildSize: 0.9,
                          builder: (_, scrollController) {
                            return Container(
                              padding: const EdgeInsets.all(16),
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(28),
                                  topLeft: Radius.circular(28),
                                ),
                              ),
                              child: Column(
                                children: [
                                  // Header Section
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      // User Info Section
                                      Row(
                                        children: [
                                          CircleAvatar(
                                            radius: 30,
                                            backgroundImage:
                                                user.picture != null
                                                    ? NetworkImage(
                                                        "$baseUrl/api/allimg/image/${user.picture}",
                                                        headers: {
                                                          'Authorization':
                                                              "Bearer $token"
                                                        },
                                                      )
                                                    : null,
                                            backgroundColor: Colors.grey[300],
                                            child: user.picture == null
                                                ? const Icon(Icons.person,
                                                    size: 30,
                                                    color: Colors.white)
                                                : null,
                                          ),
                                          const SizedBox(width: 6),
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                user.fullName.length > 20
                                                    ? "${user.fullName.substring(0, 18)}.."
                                                    : user.fullName,

                                                style: TextStyle(
                                                  fontSize: MediaQuery.of(
                                                              context)
                                                          .size
                                                          .width *
                                                      0.04, // Adjust the multiplier as needed
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.black87,
                                                ),
                                                overflow: TextOverflow
                                                    .ellipsis, // Adds ellipsis if the text overflows
                                                maxLines:
                                                    1, // Ensures text stays in a single line
                                              ),
                                              const SizedBox(height: 4),
                                              Row(
                                                children: [
                                                  Icon(
                                                    Icons.phone,
                                                    color: Colors.blueAccent,
                                                    size: MediaQuery.of(context)
                                                            .size
                                                            .width *
                                                        0.08,
                                                  ),
                                                  const SizedBox(width: 6),
                                                  Text(
                                                    '${user.mobileNumber}',
                                                    style: TextStyle(
                                                      fontSize: MediaQuery.of(
                                                                  context)
                                                              .size
                                                              .width *
                                                          0.04, // Adjust the multiplier as needed,
                                                      color: Colors.black54,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(height: 8),
                                              Row(
                                                children: [
                                                  RatingBarIndicator(
                                                    rating: averageRating!,
                                                    itemSize:
                                                        MediaQuery.of(context)
                                                                .size
                                                                .width *
                                                            0.04,
                                                    itemBuilder: (context,
                                                            index) =>
                                                        const Icon(
                                                            Icons.star_rounded,
                                                            color:
                                                                Colors.amber),
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Text(
                                                    "${averageRating.toStringAsFixed(1)} (${nos.toString()})",
                                                    style: TextStyle(
                                                      fontSize:
                                                          MediaQuery.of(context)
                                                                  .size
                                                                  .width *
                                                              0.04,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color: Colors.grey[700],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                      // Action Buttons

                                      Column(
                                        children: [
                                          // Call Button (at the top)
                                          // Container(
                                          //   margin: const EdgeInsets.only(
                                          //       left:
                                          //           30), // Adjust the value as needed
                                          //   decoration: BoxDecoration(
                                          //     color:
                                          //         Colors.green.withOpacity(0.1),
                                          //     borderRadius:
                                          //         BorderRadius.circular(50),
                                          //   ),
                                          //   child: IconButton(
                                          //     icon: const Icon(Icons.call,
                                          //         color: Colors.green),
                                          //     onPressed: () async {
                                          //       final Uri url = Uri(
                                          //           scheme: 'tel',
                                          //           path: user.mobileNumber);
                                          //       if (await canLaunchUrl(url)) {
                                          //         await launchUrl(url);
                                          //       }
                                          //     },
                                          //     padding: EdgeInsets
                                          //         .zero, // Remove default padding
                                          //     constraints:
                                          //         BoxConstraints.tightFor(
                                          //       width: size.width *
                                          //           0.09, // Set the width to match the rating button
                                          //       height: size.height *
                                          //           0.03, // Set the height to match the rating button
                                          //     ),
                                          //   ),
                                          // ),
                                          Container(
                                            margin:
                                                const EdgeInsets.only(left: 30),
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.green.withOpacity(0.1),
                                              borderRadius:
                                                  BorderRadius.circular(50),
                                            ),
                                            child: IconButton(
                                              icon: const Icon(Icons.call,
                                                  color: Colors.green),
                                              onPressed: () async {
                                                try {
                                                  // First log the call click
                                                  await logCallClick(tid!,
                                                      user.mobileNumber ?? '');

                                                  // Then initiate the call
                                                  final Uri url = Uri(
                                                      scheme: 'tel',
                                                      path: user.mobileNumber);
                                                  if (await canLaunchUrl(url)) {
                                                    await launchUrl(url);
                                                  }
                                                } catch (e) {
                                                  // The call will still proceed even if logging fails
                                                  final Uri url = Uri(
                                                      scheme: 'tel',
                                                      path: user.mobileNumber);
                                                  if (await canLaunchUrl(url)) {
                                                    await launchUrl(url);
                                                  }
                                                }
                                              },
                                              padding: EdgeInsets.zero,
                                              constraints:
                                                  BoxConstraints.tightFor(
                                                width: size.width * 0.09,
                                                height: size.height * 0.03,
                                              ),
                                            ),
                                          ),

                                          const SizedBox(
                                              height:
                                                  10), // Space between Call button and Rating/Report row

                                          // Row containing Rating and Report buttons
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment
                                                .start, // Align buttons to the start
                                            children: [
                                              // Rating Button
                                              InkWell(
                                                onTap: () {
                                                  if (tid == user.id) {
                                                    errorToast(
                                                        msg:
                                                            "You can't rate yourself");
                                                  } else {
                                                    _showRatingDialog(user);
                                                  }
                                                },
                                                borderRadius:
                                                    BorderRadius.circular(40),
                                                child: Container(
                                                  padding:
                                                      const EdgeInsets.all(12),
                                                  decoration: BoxDecoration(
                                                    color: Colors.blueAccent
                                                        .withOpacity(0.2),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            30),
                                                  ),
                                                  child: Icon(
                                                    Icons.star,
                                                    color: Colors.blueAccent,
                                                    size: size.width * 0.05,
                                                  ),
                                                ),
                                              ),
                                              SizedBox(
                                                width: size.width * 0.04,
                                              ), // Space between Rating and Report buttons

                                              // Report Button
                                              InkWell(
                                                onTap: () {
                                                  if (tid == user.id) {
                                                    errorToast(
                                                        msg:
                                                            "You can't report yourself");
                                                  } else {
                                                    reasonController.text = "";
                                                    showDialog(
                                                      context: context,
                                                      builder: (BuildContext
                                                          context) {
                                                        return AlertDialog(
                                                          shape:
                                                              RoundedRectangleBorder(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        32), // Rounded corners for the dialog box
                                                          ),
                                                          backgroundColor:
                                                              Colors.white,
                                                          titlePadding:
                                                              EdgeInsets.zero,
                                                          title: Container(
                                                            padding:
                                                                const EdgeInsets
                                                                    .all(24),
                                                            decoration:
                                                                const BoxDecoration(
                                                              color: Colors.red,
                                                              borderRadius:
                                                                  BorderRadius.vertical(
                                                                      top: Radius
                                                                          .circular(
                                                                              16)),
                                                            ),
                                                            child: Center(
                                                              // Center the text
                                                              child: Text(
                                                                'Report  ${user.fullName}',
                                                                style:
                                                                    TextStyle(
                                                                  fontSize: MediaQuery.of(
                                                                              context)
                                                                          .size
                                                                          .width *
                                                                      0.045,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                  color: Colors
                                                                      .white,
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                          contentPadding: EdgeInsets
                                                              .all(MediaQuery.of(
                                                                          context)
                                                                      .size
                                                                      .width *
                                                                  0.04), // Responsive padding
                                                          content: Column(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            children: [
                                                              Text(
                                                                'Please provide a reason for reporting this service provider.',
                                                                style:
                                                                    TextStyle(
                                                                  fontSize: MediaQuery.of(
                                                                              context)
                                                                          .size
                                                                          .width *
                                                                      0.04, // Responsive font size
                                                                ),
                                                              ),
                                                              SizedBox(
                                                                height: MediaQuery.of(
                                                                            context)
                                                                        .size
                                                                        .height *
                                                                    0.02, // Responsive spacing
                                                              ),
                                                              TextField(
                                                                controller:
                                                                    reasonController,
                                                                maxLines: 2,
                                                                decoration:
                                                                    InputDecoration(
                                                                  hintText:
                                                                      'Enter your reason here',
                                                                  hintStyle: TextStyle(
                                                                      color: Colors
                                                                          .grey
                                                                          .shade600),
                                                                  border:
                                                                      OutlineInputBorder(
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            10),
                                                                    borderSide: BorderSide(
                                                                        color: Colors
                                                                            .grey
                                                                            .shade400),
                                                                  ),
                                                                  focusedBorder:
                                                                      OutlineInputBorder(
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            10),
                                                                    borderSide:
                                                                        const BorderSide(
                                                                            color:
                                                                                Colors.red),
                                                                  ),
                                                                  contentPadding:
                                                                      EdgeInsets
                                                                          .symmetric(
                                                                    vertical: MediaQuery.of(context)
                                                                            .size
                                                                            .height *
                                                                        0.015, // Responsive padding
                                                                    horizontal: MediaQuery.of(context)
                                                                            .size
                                                                            .width *
                                                                        0.04, // Responsive horizontal padding
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                          actions: <Widget>[
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                      horizontal:
                                                                          8.0),
                                                              child: Row(
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .spaceBetween,
                                                                children: [
                                                                  Expanded(
                                                                    child:
                                                                        TextButton(
                                                                      style: TextButton
                                                                          .styleFrom(
                                                                        backgroundColor: Colors
                                                                            .grey
                                                                            .shade300,
                                                                        padding: const EdgeInsets
                                                                            .symmetric(
                                                                            vertical:
                                                                                14),
                                                                        shape:
                                                                            RoundedRectangleBorder(
                                                                          borderRadius:
                                                                              BorderRadius.circular(10),
                                                                        ),
                                                                      ),
                                                                      child:
                                                                          const Text(
                                                                        'Cancel',
                                                                        style: TextStyle(
                                                                            color:
                                                                                Colors.black,
                                                                            fontSize: 16),
                                                                      ),
                                                                      onPressed:
                                                                          () {
                                                                        Navigator.of(context)
                                                                            .pop();
                                                                      },
                                                                    ),
                                                                  ),
                                                                  const SizedBox(
                                                                      width:
                                                                          10),
                                                                  Expanded(
                                                                    child:
                                                                        ElevatedButton(
                                                                      style: ElevatedButton
                                                                          .styleFrom(
                                                                        backgroundColor:
                                                                            Colors.red,
                                                                        padding: const EdgeInsets
                                                                            .symmetric(
                                                                            vertical:
                                                                                14),
                                                                        shape:
                                                                            RoundedRectangleBorder(
                                                                          borderRadius:
                                                                              BorderRadius.circular(10),
                                                                        ),
                                                                        elevation:
                                                                            2,
                                                                      ),
                                                                      child:
                                                                          Text(
                                                                        'Submit',
                                                                        style:
                                                                            TextStyle(
                                                                          color:
                                                                              Colors.white,
                                                                          fontSize:
                                                                              MediaQuery.of(context).size.width * 0.04,
                                                                        ),
                                                                      ),
                                                                      onPressed:
                                                                          () {
                                                                        if (reasonController
                                                                            .text
                                                                            .isEmpty) {
                                                                          errorToast(
                                                                              msg: "Fill the text box");
                                                                        } else {
                                                                          reportServiceProvider(
                                                                              tid!,
                                                                              user.id,
                                                                              reasonController.text.toString());
                                                                          Navigator.of(context)
                                                                              .pop();
                                                                        }
                                                                        // Handle the report submission logic
                                                                      },
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ],
                                                        );
                                                      },
                                                    );
                                                  }
                                                },
                                                borderRadius:
                                                    BorderRadius.circular(50),
                                                child: Container(
                                                  padding:
                                                      const EdgeInsets.all(12),
                                                  decoration: BoxDecoration(
                                                    color: Colors.red
                                                        .withOpacity(0.15),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            50),
                                                    boxShadow: const [
                                                      BoxShadow(
                                                        color: Colors.black12,
                                                        blurRadius: 6,
                                                        offset: Offset(0,
                                                            4), // Shadow under the button
                                                      ),
                                                    ],
                                                  ),
                                                  child: Icon(
                                                    Icons.report,
                                                    color: Colors.red,
                                                    size: MediaQuery.of(context)
                                                            .size
                                                            .width *
                                                        0.06,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  const Divider(
                                      color: Colors.black26, height: 20),

                                  // Reviews Title
                                  Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      'Reviews',
                                      style: TextStyle(
                                        fontSize:
                                            MediaQuery.of(context).size.width *
                                                0.045,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 10),

                                  Expanded(
                                    child: StatefulBuilder(
                                      builder: (context, setModalState) {
                                        return ListView.builder(
                                          controller: scrollController,
                                          itemCount: comments.length +
                                              (totalCommentsCount >
                                                      comments.length
                                                  ? 1
                                                  : 0), // +1 for the "See More" button
                                          itemBuilder: (context, index) {
                                            if (index < comments.length) {
                                              // Display the comment item
                                              final comment = comments[index];
                                              return _buildReviewItem(
                                                comment.ratingFullName,
                                                comment.score,
                                                comment.comment,
                                                comment.date,
                                              );
                                            } else {
                                              // Display the "See More" button
                                              return Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        vertical: 16.0),
                                                child: ElevatedButton(
                                                  onPressed: () async {
                                                    setModalState(() {
                                                      loadingMore =
                                                          true; // Show loading indicator
                                                    });
                                                    currentPage++;

                                                    // Fetch new comments
                                                    List<Comment> newComments =
                                                        await getComments(
                                                            user.id,
                                                            currentPage);
                                                    setModalState(() {
                                                      comments.addAll(
                                                          newComments); // Update comments list
                                                      loadingMore =
                                                          false; // Hide loading indicator
                                                    });
                                                  },
                                                  child: loadingMore
                                                      ? const CircularProgressIndicator()
                                                      : Text(
                                                          'See More (${totalCommentsCount - comments.length})'),
                                                ),
                                              );
                                            }
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      );
                    }
                  },
                  infoWindow: InfoWindow(
                    title: user.fullName,
                    snippet: (authController.token == null ||
                            authController.token!.isEmpty)
                        ? 'Sign in to view details'
                        : '⭐ ${user.averageRating} (${user.noOfRatingReceived})',
                  ));
            }).toSet();
            return GoogleMap(
              initialCameraPosition: CameraPosition(
                target: currentPosition,
                zoom: 14.4746,
              ),
              mapType: MapType.normal,
              myLocationEnabled: true,
              compassEnabled: true,
              onMapCreated: (GoogleMapController controller) {
                _customInfoWindowController.googleMapController = controller;
                _controller.complete(controller);
              },
              markers: markers.union(_marker.toSet()),
            );
          }),
          Positioned(
            bottom: screenHeight * 0.26,
            right: screenWidth * 0.03,
            child: FloatingActionButton(
              onPressed: () async {
                getUserCurrentLocation().then((value) async {
                  _circles.add(
                    Circle(
                      circleId: const CircleId("currentLocationCircle"),
                      center: LatLng(value.latitude, value.longitude),
                      radius: 20,
                      fillColor: Colors.red.withOpacity(1),
                      strokeColor: Colors.redAccent,
                      strokeWidth: 3,
                    ),
                  );

                  CameraPosition cameraPosition = CameraPosition(
                    target: LatLng(value.latitude, value.longitude),
                    zoom: 14,
                  );

                  final GoogleMapController controller =
                      await _controller.future;
                  controller.animateCamera(
                      CameraUpdate.newCameraPosition(cameraPosition));
                  setState(() {});
                });
              },
              child: const Icon(Icons.my_location),
            ),
          ),
          SizedBox(height: MediaQuery.of(context).size.height * 0.012),
          Positioned(
            bottom: screenHeight * 0.35,
            right: screenWidth * 0.03,
            child: FloatingActionButton(
              onPressed: () {
                consolelog("okays");
                _sortProvidersByDistance(getWorker());
                setState(() {
                  _showServiceProvidersList = !_showServiceProvidersList;
                });
              },
              backgroundColor: const Color.fromARGB(240, 0, 131, 143),
              child: Icon(
                _showServiceProvidersList ? Icons.close : Icons.list,
                color: Colors.white,
                size: screenWidth * 0.07, // Responsive icon size
              ),
            ),
          ),
          SizedBox(height: MediaQuery.of(context).size.height * 0.012),
          // Positioned(
          //   bottom: screenHeight * 0.120,
          //   right: screenWidth * 0.015,
          //   child: FloatingActionButton(
          //     onPressed: () async {
          //       getUserCurrentLocation().then((value) async {
          //         CameraPosition cameraPosition = CameraPosition(
          //           target: LatLng(value.latitude, value.longitude),
          //           zoom: 14,
          //         );

          //         final GoogleMapController controller =
          //             await _controller.future;
          //         controller.animateCamera(
          //             CameraUpdate.newCameraPosition(cameraPosition));
          //         setState(() {});
          //       });
          //     },
          //     child: Icon(
          //       Icons.my_location,
          //       size: screenWidth * 0.06,
          //     ),
          //   ),
          // ),
          if (_showServiceProvidersList)
            Positioned(
              bottom: MediaQuery.of(context).size.height * 0.1,
              left: 0,
              right: 0,
              child: Container(
                height: MediaQuery.of(context).size.height * 0.5,
                margin: EdgeInsets.symmetric(
                  horizontal: MediaQuery.of(context).size.width * 0.025,
                  vertical: MediaQuery.of(context).size.height * 0.01,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.white,
                      Colors.grey.shade50,
                    ],
                  ),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(
                        MediaQuery.of(context).size.width * 0.06),
                    topRight: Radius.circular(
                        MediaQuery.of(context).size.width * 0.06),
                    bottomLeft: Radius.circular(
                        MediaQuery.of(context).size.width * 0.04),
                    bottomRight: Radius.circular(
                        MediaQuery.of(context).size.width * 0.04),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.15),
                      blurRadius: MediaQuery.of(context).size.width * 0.04,
                      spreadRadius: MediaQuery.of(context).size.width * 0.002,
                      offset:
                          Offset(0, -MediaQuery.of(context).size.width * 0.01),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Top handle indicator
                    Container(
                      margin: EdgeInsets.only(
                          top: MediaQuery.of(context).size.width * 0.02),
                      width: MediaQuery.of(context).size.width * 0.12,
                      height: MediaQuery.of(context).size.width * 0.012,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(
                            MediaQuery.of(context).size.width * 0.01),
                      ),
                    ),
                    // Header section
                    Container(
                      padding: EdgeInsets.all(
                          MediaQuery.of(context).size.width * 0.04),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: Colors.grey.shade200,
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Service Providers Nearby',
                                style: TextStyle(
                                  fontSize:
                                      MediaQuery.of(context).size.width * 0.048,
                                  fontWeight: FontWeight.bold,
                                  color: const Color.fromARGB(240, 0, 131, 143),
                                ),
                              ),
                              SizedBox(
                                  height:
                                      MediaQuery.of(context).size.width * 0.01),
                              Text(
                                '${_sortedServiceProviders.length} providers found',
                                style: TextStyle(
                                  fontSize:
                                      MediaQuery.of(context).size.width * 0.035,
                                  color: Colors.grey.shade600,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.grey.shade100,
                            ),
                            child: IconButton(
                              icon: Icon(
                                Icons.close,
                                size: MediaQuery.of(context).size.width * 0.06,
                                color: Colors.grey.shade600,
                              ),
                              onPressed: () {
                                setState(() {
                                  _showServiceProvidersList = false;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    // List content
                    Expanded(
                      child: _sortedServiceProviders.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(
                                        MediaQuery.of(context).size.width *
                                            0.08),
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Colors.grey.shade100,
                                    ),
                                    child: Icon(
                                      Icons.search_off,
                                      size: MediaQuery.of(context).size.width *
                                          0.12,
                                      color: Colors.grey.shade400,
                                    ),
                                  ),
                                  SizedBox(
                                      height:
                                          MediaQuery.of(context).size.width *
                                              0.04),
                                  Text(
                                    'No service providers found',
                                    style: TextStyle(
                                      fontSize:
                                          MediaQuery.of(context).size.width *
                                              0.045,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                  SizedBox(
                                      height:
                                          MediaQuery.of(context).size.width *
                                              0.02),
                                  Text(
                                    'Try expanding your search area',
                                    style: TextStyle(
                                      fontSize:
                                          MediaQuery.of(context).size.width *
                                              0.037,
                                      color: Colors.grey.shade500,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              padding: EdgeInsets.symmetric(
                                horizontal:
                                    MediaQuery.of(context).size.width * 0.02,
                                vertical:
                                    MediaQuery.of(context).size.width * 0.02,
                              ),
                              itemCount: _sortedServiceProviders.length,
                              itemBuilder: (context, index) {
                                final provider = _sortedServiceProviders[index];
                                String distanceText;
                                if (provider.distance! < 1000) {
                                  distanceText =
                                      '${provider.distance!.toStringAsFixed(0)} meters';
                                } else {
                                  distanceText =
                                      '${(provider.distance! / 1000).toStringAsFixed(1)} km';
                                }

                                return Container(
                                  margin: EdgeInsets.only(
                                    bottom: MediaQuery.of(context).size.width *
                                        0.03,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(
                                      MediaQuery.of(context).size.width * 0.04,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.05),
                                        blurRadius:
                                            MediaQuery.of(context).size.width *
                                                0.02,
                                        spreadRadius: 1,
                                        offset: Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: ListTile(
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal:
                                          MediaQuery.of(context).size.width *
                                              0.04,
                                      vertical:
                                          MediaQuery.of(context).size.width *
                                              0.02,
                                    ),
                                    leading: Container(
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: const Color.fromARGB(
                                                  240, 0, 131, 143)
                                              .withOpacity(0.2),
                                          width: 2,
                                        ),
                                      ),
                                      child: CircleAvatar(
                                        radius:
                                            MediaQuery.of(context).size.width *
                                                0.065,
                                        backgroundColor: Colors.grey.shade100,
                                        backgroundImage:
                                            provider.picture != null
                                                ? NetworkImage(
                                                    "$baseUrl/api/allimg/image/${provider.picture}",
                                                    headers: {
                                                      'Authorization':
                                                          "Bearer $token"
                                                    },
                                                  )
                                                : null,
                                        child: provider.picture == null
                                            ? Icon(
                                                Icons.person,
                                                size: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    0.06,
                                                color: const Color.fromARGB(
                                                    240, 0, 131, 143),
                                              )
                                            : null,
                                      ),
                                    ),
                                    title: Text(
                                      provider.fullName,
                                      style: TextStyle(
                                        fontSize:
                                            MediaQuery.of(context).size.width *
                                                0.045,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.grey.shade800,
                                      ),
                                    ),
                                    subtitle: Padding(
                                      padding: EdgeInsets.only(
                                          top: MediaQuery.of(context)
                                                  .size
                                                  .width *
                                              0.02),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              RatingBarIndicator(
                                                rating:
                                                    provider.averageRating ?? 0,
                                                itemSize: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    0.04,
                                                itemBuilder: (context, _) =>
                                                    const Icon(
                                                  Icons.star,
                                                  color: Colors.amber,
                                                ),
                                              ),
                                              SizedBox(
                                                  width: MediaQuery.of(context)
                                                          .size
                                                          .width *
                                                      0.02),
                                              Text(
                                                '${(provider.averageRating ?? 0).toStringAsFixed(1)}',
                                                style: TextStyle(
                                                  fontSize:
                                                      MediaQuery.of(context)
                                                              .size
                                                              .width *
                                                          0.032,
                                                  color: Colors.grey.shade600,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(
                                              height: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.015),
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.location_on,
                                                size: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    0.035,
                                                color: Colors.grey.shade600,
                                              ),
                                              SizedBox(
                                                  width: MediaQuery.of(context)
                                                          .size
                                                          .width *
                                                      0.01),
                                              Text(
                                                distanceText,
                                                style: TextStyle(
                                                  fontSize:
                                                      MediaQuery.of(context)
                                                              .size
                                                              .width *
                                                          0.035,
                                                  color: Colors.grey.shade600,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    trailing: Container(
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        gradient: LinearGradient(
                                          colors: [
                                            Colors.green.shade400,
                                            Colors.green.shade600,
                                          ],
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                Colors.green.withOpacity(0.3),
                                            blurRadius: MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                0.02,
                                            offset: Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: IconButton(
                                        icon: Icon(
                                          Icons.phone,
                                          color: Colors.white,
                                          size: MediaQuery.of(context)
                                                  .size
                                                  .width *
                                              0.055,
                                        ),
                                        onPressed: () {
                                          final Uri url = Uri(
                                            scheme: 'tel',
                                            path: provider.mobileNumber,
                                          );
                                          launchUrl(url);
                                        },
                                      ),
                                    ),
                                    onTap: () {
                                      _navigateToProviderOnMap(provider);
                                    },
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ),
            )
        ],
      ),
    );
  }
}

Widget _buildReviewList(List<Comment> comments) {
  // If there are no comments, display a message
  if (comments.isEmpty) {
    return const Center(child: Text('No reviews available.'));
  }

  // Return empty container if called directly
  return const SizedBox.shrink();
}

// Function to fetch comments
Future<List<Comment>> getComments(int id, int page) async {
  List<Comment> comments = await fetchComments(id, page, 10);
  return comments; // Return the list of comments
}

// Function to display individual reviews with a modern card style
Widget _buildReviewItem(
    String reviewerName, double rating, String comment, String dat) {
  DateFormat inputFormat = DateFormat('dd-MM-yy');
  DateTime date = inputFormat.parse(dat);
  String formattedDate = DateFormat('dd MMMM yyyy').format(date);

  return Card(
    elevation: 3,
    margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
    child: ListTile(
      leading: CircleAvatar(
        backgroundColor: Colors.teal[200],
        child: const Icon(Icons.person, color: Colors.white),
      ),
      title: Text(
        reviewerName,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              RatingBarIndicator(
                rating: rating,
                itemSize: 18,
                itemBuilder: (context, index) =>
                    const Icon(Icons.star, color: Colors.amber),
              ),
              const Spacer(),
              Text(
                formattedDate,
                style: TextStyle(color: Colors.grey[500], fontSize: 12),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            comment,
            style: TextStyle(color: Colors.grey[600]),
          ),
        ],
      ),
    ),
  );
}
