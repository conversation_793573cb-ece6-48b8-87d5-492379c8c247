import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:smartsewa/core/development/console.dart';

import 'package:smartsewa/network/base_client.dart';
import 'package:smartsewa/views/widgets/custom_toasts.dart';
import 'package:url_launcher/url_launcher.dart';

/////////////////base url//////////
String baseUrl = BaseClient().baseUrl;

/////////////////////// password visible   ///////////////////////////////////////////
bool _passwordVisible = false;
bool _confirmPasswordVisible = false;

//////////////    for skills and sub skills dropdown  ////////////////////////////////////
String selectedMainSkill = '';
List<String> selectedSkills = [];
Map<String, List<String>> selectedSubSkills = {};
String? selectedJobCategoryId;
List<String> selectedServiceIds = [];

final TextEditingController documentController = TextEditingController();

String? citizenshipFrontUrl;
String? citizenshipBackUrl;
String? drivingLicenseUrl;
String? nationalIdUrl;
int id = 0;

int count = 0, count1 = 0;
dynamic response;
dynamic request;

//////////////////    validation for email  ///////////////////////////////////////////
bool isValidEmail(String email) {
  final RegExp emailRegex =
      RegExp(r'^(?=.*[A-Za-z])[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$');
  return emailRegex.hasMatch(email);
}

///////////////////////////////  validation for password //////////////////////////////////////
bool containsUppercase(String value) {
  return value.contains(RegExp(r'[A-Z]'));
}

bool containsLowercase(String value) {
  return value.contains(RegExp(r'[a-z]'));
}

bool containsNumber(String value) {
  return value.contains(RegExp(r'[0-9]'));
}

bool containsSpecialCharacter(String value) {
  return value.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
}

Future<Map<String, String>> uploadImage(String paramName, File image) async {
  try {
    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$baseUrl/api/allimg/user/otherimg'),
    );

    request.files.add(await http.MultipartFile.fromPath(
      paramName,
      image.path,
    ));

    var response = await request.send();
    String responseBody = await response.stream.bytesToString();

    consolelog('Response Body: $responseBody');
    if (response.statusCode == 200) {
      Map<String, dynamic> imageData = jsonDecode(responseBody);

      if (imageData.containsKey(paramName) && imageData[paramName] != null) {
        String imageUrl = imageData[paramName];

        return {paramName: imageUrl};
      } else {
        consolelog('Image upload failed or invalid response from server');
        return {paramName: ''};
      }
    } else {
      count1 = 6;
      errorToast(msg: '${jsonDecode(responseBody)['message']} ');
      return {paramName: ''};
    }
  } catch (e) {
    consolelog('Error uploading image: $e');
    return {paramName: ''};
  }
}

Future<List<Map<String, dynamic>>> fetchSkills() async {
  try {
    final response = await http.get(Uri.parse('$baseUrl/api/categories/'));
    if (response.statusCode == 200) {
      List<dynamic> categories = jsonDecode(response.body);
      return categories.map((category) {
        return {
          'categoryId': category['categoryId'].toString(),
          'categoryTitle': category['categoryTitle'].toString()
        };
      }).toList();
    } else {
      print('Failed to load skills: ${response.statusCode}');
      throw Exception('Failed to load skills');
    }
  } catch (e) {
    print('Error fetching skills: $e');
    throw Exception('Failed to load skills');
  }
}

Future<List<Map<String, dynamic>>> fetchSubSkills(String categoryId) async {
  try {
    final response =
        await http.get(Uri.parse('$baseUrl/api/category/$categoryId/services'));
    if (response.statusCode == 200) {
      List<dynamic> responseBody = jsonDecode(response.body);
      return responseBody.map((subSkill) {
        return {
          'id': subSkill['id'].toString(),
          'name': subSkill['name'].toString()
        };
      }).toList();
    } else {
      log('Failed to load subskills: ${response.statusCode}');
      throw Exception('Failed to load subskills');
    }
  } catch (e) {
    log('Error fetching subskills: $e');
    throw Exception('Failed to load subskills');
  }
}

Future<File?> compressImage(File file) async {
  final filePath = file.absolute.path;

  final lastIndex = filePath.lastIndexOf('.');
  final split = filePath.substring(0, lastIndex);
  final outPath = "${split}_compressed.jpg";

  var result = await FlutterImageCompress.compressAndGetFile(
    file.absolute.path,
    outPath,
    quality: 70,
    minWidth: 1080,
    minHeight: 720,
  );

  if (result != null) {
    return File(result.path);
  } else {
    return null;
  }
}

///*******Code For Google Map*******//////
bool onpressed = false;
GoogleMapController? googleMapController;
final Completer<GoogleMapController> completeGoogleMapController = Completer();
LatLng selectedLatLng = const LatLng(27.707795, 85.343362);
const CameraPosition kGoogle = CameraPosition(
  target: LatLng(27.707795, 85.343362),
  zoom: 14.4746,
);

final List<Marker> marker = <Marker>[
  const Marker(
      markerId: MarkerId('1'),
      //  position: LatLng(27.707795, 85.343362),
      infoWindow: InfoWindow(
        title: 'My Position',
      )),
];

LocationPermission? permission;
Future<void> urlLaunch(String url) async {
  final Uri uri = Uri.parse(url);
  if (!await launchUrl(
    uri,
    mode: LaunchMode.inAppWebView,
  )) {
    throw 'Error launching URL: $url';
  }
}
