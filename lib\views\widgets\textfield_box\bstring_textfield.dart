// ignore_for_file: public_member_api_docs, sort_constructors_first

/*
import 'package:flutter/material.dart';

class BStringText<PERSON>ield extends StatelessWidget {
  final String name;
  final IconData boxIcon;
  final String hintText;
  final TextEditingController controller;
  final bool? readOnly;

  const BStringTextField({
    super.key,
    required this.name,
    required this.boxIcon,
    required this.hintText,
    required this.controller,
    this.readOnly,
  });

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;

    return Column(
      children: [
        Row(
          children: [
            Icon(
              boxIcon,
              size: size.aspectRatio * 55,
              color: Colors.white,
            ),
            SizedBox(width: size.width * 0.01),
            Text(
              name,
              style: const TextStyle(
                fontFamily: 'hello',
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Color.fromRGBO(0, 131, 143, 1),
              ),
            ),
          ],
        ),
        Sized<PERSON><PERSON>(height: size.height * 0.012),
        TextFormField(
          keyboardType: TextInputType.multiline,
          maxLines: null,
          controller: controller,
          validator: (value) {
            if (value!.isEmpty) {
              return 'required**';
            }
            return null;
          },
          readOnly: readOnly ?? false,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          textAlign: TextAlign.left,
          style: const TextStyle(color: Colors.black),
          decoration: InputDecoration(
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(18),
              ),
              hintText: hintText,
              hintStyle: const TextStyle(
                color: Color.fromRGBO(0, 131, 143, 1),
              )),
        ),
      ],
    );
  }
}
*/

import 'package:flutter/material.dart';

class BStringTextField extends StatefulWidget {
  final String name;
  final IconData boxIcon;
  final String hintText;
  final TextEditingController controller;
  final bool? readOnly;

  const BStringTextField({
    super.key,
    required this.name,
    required this.boxIcon,
    required this.hintText,
    required this.controller,
    this.readOnly,
  });

  @override
  _BStringTextFieldState createState() => _BStringTextFieldState();
}

class _BStringTextFieldState extends State<BStringTextField> {
  final List<String> _dropdownValues = ["Value 1", "Value 2", "Value 3"];
  String? _selectedValue;

  void _onFieldTap() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select a value'),
          titleTextStyle: const TextStyle(
            color: Color.fromRGBO(0, 131, 143, 1),
          ),
          content: SizedBox(
            width: double.minPositive,
            child: ListView(
              shrinkWrap: true,
              children: _dropdownValues.map((value) {
                return ListTile(
                  title: Text(value),
                  textColor: const Color.fromRGBO(0, 131, 143, 1),
                  onTap: () {
                    setState(() {
                      _selectedValue = value;
                      widget.controller.text = value;
                    });
                    Navigator.pop(context);
                  },
                );
              }).toList(),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              widget.boxIcon,
              size: size.aspectRatio * 55,
              color: Colors.white,
            ),
            SizedBox(width: size.width * 0.01),
            Text(
              widget.name,
              style: const TextStyle(
                fontFamily: 'hello',
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Color.fromRGBO(0, 131, 143, 1), // Custom color for text,
              ),
            ),
          ],
        ),
        SizedBox(height: size.height * 0.012),
        GestureDetector(
          onTap: _onFieldTap,
          child: AbsorbPointer(
            child: TextFormField(
              controller: widget.controller,
              validator: (value) {
                if (value!.isEmpty) {
                  return 'required**';
                }
                return null;
              },
              readOnly: widget.readOnly ?? false,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              textAlign: TextAlign.left,
              style: const TextStyle(color: Colors.black),
              decoration: InputDecoration(
                filled: true,
                fillColor: Colors.white,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(18),
                ),
                hintText: widget.hintText,
                hintStyle: const TextStyle(
                  color: Color.fromRGBO(0, 131, 143, 1),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
